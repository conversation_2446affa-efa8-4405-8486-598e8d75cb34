﻿using Connectors.town.connectors.driver.transactions;
using GamesEngine.Business;
using GamesEngine.Settings;
using Newtonsoft.Json;
using Puppeteer.EventSourcing;
using RestSharp;
using System;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Runtime.Serialization;
using System.Threading;
using System.Threading.Tasks;
using static town.connectors.drivers.Result;

namespace town.connectors.drivers.fiero.processors.AuthTransaction
{
    internal abstract class Withdrawal : FierroProcessorDriver
    {
        public static TokenDriver CashierToken { get; set; }
        private RestClient _postClient;

        public string Password { get; set; }
        public string ServicesUrl { get; set; }

        private const float VERSION = 1.1F;
        public override string Description => $"Fiero {nameof(Deposit)} driver {VERSION}";

        public Withdrawal(string currencyIsoCode)
            : base(town.connectors.drivers.TransactionType.Withdrawal, VERSION, currencyIsoCode)
        {
        }

        public Withdrawal(string currencyIsoCode, PaymentMethod paymentMethod)
            : base(town.connectors.drivers.TransactionType.Withdrawal, VERSION, currencyIsoCode, paymentMethod)
        {
        }
        public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            throw new NotImplementedException();
        }

        public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            bool passwordChange =
                          Password != CustomSettings.Get(now, "TokenSystemPassword").AsString;
            if (passwordChange)
            {
                Password = CustomSettings.Get(now, "TokenSystemPassword").AsString;
            }
            bool servicesUrlChange =
                         ServicesUrl != CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
            if (servicesUrlChange)
            {
                ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
            }


            if (_postClient == null || _postClient.Options.BaseUrl.ToString() != ServicesUrl)
            {
                _postClient = new RestClient(ServicesUrl);
            }


            var bodyWithdrawal = new WithdrawBody()
            {
                Amount = recordSet.Mappings["amount"].AsDecimal,
                Destination = recordSet.Mappings["accountNumber"].AsString,
                ExternalReference = recordSet.Mappings["reference"].AsString,
                AtAddress = recordSet.Mappings["atAddress"].AsString,
                EmployeeName = recordSet.Mappings["who"].AsString
            };

            if (CurrencyIsoCodes.Length > 1) throw new NotImplementedException("Drives must have only one currency code");
            string currencyCode = CurrencyIsoCodes.FirstOrDefault();
            string domain = recordSet.Mappings["domain"].AsString;
            var endpoint = $"/town/v1/transaction/withdrawal/{currencyCode}";
            var result = await WithdrawalAsync(endpoint, domain, bodyWithdrawal);

            WithdrawalTransaction auth;

            if (result.AuthorizationId > 0)
            {
                auth = new WithdrawalTransaction(result.AuthorizationId, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);

                return (T)Convert.ChangeType(auth, typeof(T));
            }
            else
            {
                auth = new WithdrawalTransaction(1000,TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
            }
            return (T)Convert.ChangeType(auth, typeof(T));
        }


        public override void Prepare(DateTime now)
        {

            CustomSettings.AddVariableParameter("amount");
            CustomSettings.AddVariableParameter("accountNumber");
            CustomSettings.AddVariableParameter("currency");
            CustomSettings.AddVariableParameter("atAddress");
            CustomSettings.AddVariableParameter("domain");
            CustomSettings.AddVariableParameter("sourceNumber");
            CustomSettings.AddVariableParameter("sourceName");
            CustomSettings.AddVariableParameter("who");
            CustomSettings.AddVariableParameter("agent");
            CustomSettings.AddVariableParameter("storeId");
            CustomSettings.AddVariableParameter("processorId");
            CustomSettings.AddVariableParameter("description");
            CustomSettings.AddVariableParameter("reference");

            Password = CustomSettings.Get(now, "TokenSystemPassword").AsSecret.ToString();
            ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
        }

        protected const int MAX_RETRIES = 5;

        private async Task<WithdrawResponse> WithdrawalAsync(string url,string domain, WithdrawBody transactionBody)
        {
            //int authorization = 0;
            int retryNumber = 0;
            Loggers.GetIntance().GetOrCreateLogger(Id).Debug($"Driver={Id}\nurl={url}");

            var jsonString = JsonConvert.SerializeObject(transactionBody);
            var defaultWithdrawResponse = new WithdrawResponse
            {
                WithdrawalId = 0,
            };

            string responseString = string.Empty;
            while (true)
            {
                try
                {
                    var request = new RestRequest(url,Method.Post);
                   
                    request.AddHeader("apikey", $"{Password.Trim()}");
                    request.AddHeader("domain-url", $"{domain.Trim()}");
                    request.AddHeader("Content-Type", "application/json");
                    request.AddParameter("application/json", jsonString, ParameterType.RequestBody);

                    var response = await _postClient.ExecuteAsync(request);

                    responseString = response.Content;
                    Loggers.GetIntance().GetOrCreateLogger(Id).Debug($"Driver={Id}\response={responseString}");

                    if (response.StatusCode == HttpStatusCode.OK)
                    {
                        string json = JsonConvert.DeserializeObject<string>(responseString);
                        WithdrawResponse requesResult = JsonConvert.DeserializeObject<WithdrawResponse>(json);
                        if (requesResult != null)
                        {
                            requesResult.AuthorizationId = requesResult.WithdrawalId;
                            return new WithdrawResponse() { AuthorizationId=1000, WithdrawalId=1000};
                        }
                        return defaultWithdrawResponse;
                    }
                    else
                    {
                        Loggers.GetIntance().GetOrCreateLogger(Id).Debug($"Driver={Id}\nErrorMessage={response.ErrorMessage}");
                    }
                    break;
                }
                catch (Exception e)
                {
                    retryNumber++;
                    var extraErrorMessage = string.Empty;
                    if (e.InnerException is HttpRequestException) extraErrorMessage = "There was a problem getting the response from server.";
                    InternalOnError(nameof(Withdrawal), retryNumber, e, $"Url:{url}", $"Request: {jsonString}", $"Response: {responseString}");

                    Thread.Sleep(5000);
                    if (retryNumber == MAX_RETRIES)
                    {
                        break;
                    }
                }
            }

            return defaultWithdrawResponse;
        }

    }

    [DataContract(Name = "WithdrawBody")]
    public class WithdrawBody
    {
        [DataMember(Name = "amount")]
        public decimal Amount { get; set; }
        [DataMember(Name = "destination")]
        public string Destination { get; set; }
        [DataMember(Name = "externalReference")]
        public string ExternalReference { get; set; }
        [DataMember(Name = "atAddress")]
        public string AtAddress { get; set; }
        [DataMember(Name = "employeeName")]
        public string EmployeeName { get; set; }

    }
}
