﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamesEngine.RealTime.Events
{
    internal abstract class DispenserChangeEvent : ContainerChangeEvent
    {
        internal DispenserChangeEvent(int dispenserId, PlatformEventType eventType) : base(dispenserId, eventType)
        {
            if (dispenserId <= 0) throw new ArgumentNullException(nameof(dispenserId));
        }
    }

    internal class DispenserChangeColorEvent : DispenserChangeEvent
    {
        internal DispenserChangeColorEvent(int dispenserId) : base(dispenserId, PlatformEventType.DISPENSER_COLOR_HAS_CHANGE)
        {
        }
    }

    internal class DispenserHeaderInfoChangeEvent : DispenserChangeEvent
    {
        internal DispenserHeaderInfoChangeEvent(int dispenserId) : base(dispenserId, PlatformEventType.DISPENSER_HEADER_INFO_HAS_CHANGE)
        {
        }
    }

    internal class DispenserDiscardedEvent : DispenserChangeEvent
    {
        internal DispenserDiscardedEvent(int dispenserId) : base(dispenserId, PlatformEventType.DISPENSER_DISCARDED)
        {
        }
    }

    internal class DispenserWithdrawalsCommittedEvent : DispenserChangeEvent
    {
        internal DispenserWithdrawalsCommittedEvent(int dispenserId) : base(dispenserId, PlatformEventType.DISPENSER_WITHDRAWALS_HAS_BEEN_COMMITTED)
        {
        }
    }

    internal class DispenserWithdrawalsAddedEvent : DispenserChangeEvent
    {
        internal DispenserWithdrawalsAddedEvent(int dispenserId) : base(dispenserId, PlatformEventType.DISPENSER_WITHDRAWALS_HAS_BEEN_ADDED)
        {
        }
    }

    internal class DispenserWithdrawalHasBeenMoveEvent : DispenserChangeEvent
    {
        internal DispenserWithdrawalHasBeenMoveEvent(int dispenserId) : base(dispenserId, PlatformEventType.DISPENSER_WITHDRAWAL_HAS_BEEN_MOVE)
        {
        }
    }

    internal class DispenserHaveBeenMoveToDispenserEvent : DispenserChangeEvent
    {
        internal DispenserHaveBeenMoveToDispenserEvent(int dispenserId) : base(dispenserId, PlatformEventType.DISPENSER_HAVE_BEEN_MOVE_TO_DISPENSER)
        {
        }
    }
}
