﻿using GamesEngine.Business;
using GamesEngine.Settings;
using RestSharp;
using System;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.drivers;
using town.connectors.drivers.fiero;

namespace GamesEngine.Exchange.town.connectors.drivers.fierro.processors
{
    internal abstract class UnlockDeposit : FierroProcessorDriver, IDriverUserProperties
    {
        private const float VERSION = 1.0F;

        public static TokenDriver CashierToken { get; set; }

        public string DriverUserName { get; set; }
        public string DriverPassword { get; set; }

        private HttpClient _postTransactionClient;

        public UnlockDeposit(string currencyIsoCode) : base(TransactionType.Unlock, VERSION, currencyIsoCode, PaymentMethod.ThirdParty)
        {
        }

        public override void Prepare(DateTime now)
        {
            CustomSettings.AddVariableParameter("atAddress");
            CustomSettings.AddVariableParameter("amount");
            CustomSettings.AddVariableParameter("accountNumber");
            CustomSettings.AddVariableParameter("who");
            CustomSettings.AddVariableParameter("documentNumber");
            CustomSettings.AddVariableParameter("storeId");
            CustomSettings.AddVariableParameter("concept");
            CustomSettings.AddVariableParameter("reference");
            CustomSettings.AddVariableParameter("processorId");

            DriverUserName = CustomSettings.Get(now, "CashierDriver.userName").AsString;
            DriverPassword = CustomSettings.Get(now, "CashierDriver.password").AsString;
        }

        public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            string cashierUrl = CustomSettings.Get(now, "CashierDriver.url").AsString;
            if (_postTransactionClient == null || _postTransactionClient.BaseAddress.ToString() != cashierUrl)
            {
                _postTransactionClient = new HttpClient();
                _postTransactionClient.BaseAddress = new Uri(cashierUrl);
            }

            bool userPassHasChange = DriverUserName != CustomSettings.Get(now, "CashierDriver.userName").AsString || DriverPassword != CustomSettings.Get(now, "CashierDriver.password").AsString;
            if (userPassHasChange)
            {
                DriverUserName = CustomSettings.Get(now, "CashierDriver.userName").AsString;
                DriverPassword = CustomSettings.Get(now, "CashierDriver.password").AsString;
            }
            
            bool hasExpired = Refund.CashierToken == null || SecurityConfiguration.ItsSecurityTokenExpired(Refund.CashierToken.access_token, now);
            bool itsSecurityConfigured = SecurityConfiguration.WasConfigure() && SecurityConfiguration.ItsSecuritySchemeConfigured();

            bool needToChangeToken = (Refund.CashierToken == null || userPassHasChange || hasExpired) && itsSecurityConfigured;
            if (needToChangeToken) Refund.CashierToken = TokenDriver.GetToken(DriverUserName, DriverPassword);

            string atAddress = recordSet.Mappings["atAddress"].AsString;
            decimal amount = recordSet.Mappings["amount"].AsDecimal;
            string accountNumber = recordSet.Mappings["accountNumber"].AsString;
            string who = recordSet.Mappings["who"].AsString;
            string documentNumber = recordSet.Mappings["documentNumber"].AsString;
            int store = recordSet.Mappings["storeId"].AsInt;
            string concept = recordSet.Mappings["concept"].AsString;
            string reference = recordSet.Mappings["reference"].AsString;
            int processorId = recordSet.Mappings["processorId"].AsInt;

            if (CurrencyIsoCodes.Length > 1) throw new NotImplementedException("Drives must have only one currency code");
            string currencyCode = CurrencyIsoCodes.FirstOrDefault();

            TransactionMovementBody body = new TransactionMovementBody
            {
                AtAddress = recordSet.Mappings["atAddress"].AsString,
                Currency = currencyCode,
                Amount = recordSet.Mappings["amount"].AsDecimal,
                AccountNumber = recordSet.Mappings["accountNumber"].AsString,
                Who = recordSet.Mappings["who"].AsString,
                DocumentNumber = recordSet.Mappings["documentNumber"].AsString,
                Store = recordSet.Mappings["storeId"].AsInt,
                Concept = recordSet.Mappings["concept"].AsString,
                Reference = recordSet.Mappings["reference"].AsString,
                ProcessorId = recordSet.Mappings["processorId"].AsInt
            };

            bool isDone = UnlockAuthorization(body);
            var result = new DoneResponse(isDone, Entity, PaymentMethod, CurrencyIsoCodes, TransactionTypeEnum.Value);
            return (T)Convert.ChangeType(result, typeof(T));
        }

        public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            string cahiserUrl = CustomSettings.Get(now, "CashierDriver.url").AsString;
            if (_postTransactionClient == null || _postTransactionClient.BaseAddress.ToString() != cahiserUrl)
            {
                _postTransactionClient = new HttpClient();
                _postTransactionClient.BaseAddress = new Uri(cahiserUrl);
            }

            bool userPassHasChange = DriverUserName != CustomSettings.Get(now, "CashierDriver.userName").AsString || DriverPassword != CustomSettings.Get(now, "CashierDriver.password").AsString;
            if (userPassHasChange)
            {
                DriverUserName = CustomSettings.Get(now, "CashierDriver.userName").AsString;
                DriverPassword = CustomSettings.Get(now, "CashierDriver.password").AsString;
            }

            bool hasExpired = Refund.CashierToken == null || SecurityConfiguration.ItsSecurityTokenExpired(Refund.CashierToken.access_token, now);

            bool itsSecurityConfigured = SecurityConfiguration.WasConfigure() && SecurityConfiguration.ItsSecuritySchemeConfigured();

            bool needToChangeToken = (Refund.CashierToken == null || userPassHasChange || hasExpired) && itsSecurityConfigured;
            if (needToChangeToken) Refund.CashierToken = await TokenDriver.GetTokenAsync(DriverUserName, DriverPassword);

            string atAddress = recordSet.Mappings["atAddress"].AsString;
            decimal amount = recordSet.Mappings["amount"].AsDecimal;
            string accountNumber = recordSet.Mappings["accountNumber"].AsString;
            string who = recordSet.Mappings["who"].AsString;
            string documentNumber = recordSet.Mappings["documentNumber"].AsString;
            int store = recordSet.Mappings["storeId"].AsInt;
            string concept = recordSet.Mappings["concept"].AsString;
            string reference = recordSet.Mappings["reference"].AsString;
            int processorId = recordSet.Mappings["processorId"].AsInt;

            if (CurrencyIsoCodes.Length > 1) throw new NotImplementedException("Drives must have only one currency code");
            string currencyCode = CurrencyIsoCodes.FirstOrDefault();

            TransactionMovementBody body = new TransactionMovementBody
            {
                AtAddress = recordSet.Mappings["atAddress"].AsString,
                Currency = currencyCode,
                Amount = recordSet.Mappings["amount"].AsDecimal,
                AccountNumber = recordSet.Mappings["accountNumber"].AsString,
                Who = recordSet.Mappings["who"].AsString,
                DocumentNumber = recordSet.Mappings["documentNumber"].AsString,
                Store = recordSet.Mappings["storeId"].AsInt,
                Concept = recordSet.Mappings["concept"].AsString,
                Reference = recordSet.Mappings["reference"].AsString,
                ProcessorId = recordSet.Mappings["processorId"].AsInt
            };

            bool isDone = await UnlockAuthorizationAsync(body);
            var result = new DoneResponse(isDone, Entity, PaymentMethod, CurrencyIsoCodes, TransactionTypeEnum.Value);
            return (T)Convert.ChangeType(result, typeof(T));
        }

        private RestClient _postTransactionRestClient;
        private bool UnlockAuthorization(TransactionMovementBody body)
        {
            string url = "api/deposit/unlock";

            if (_postTransactionRestClient == null)
            {
                string cashierUrl = _postTransactionClient.BaseAddress.ToString();
                _postTransactionRestClient = new RestClient(cashierUrl);
            }

            var request = new RestRequest(url, Method.Post);
            if (SecurityConfiguration.WasConfigure() && SecurityConfiguration.ItsSecuritySchemeConfigured())
            {
                request.AddHeader("Authorization", $"Bearer {Refund.CashierToken.access_token}");
            }

            request.AddJsonBody(body);
            var response = _postTransactionRestClient.Execute(request);
            if (response.IsSuccessful)
            {
                return true;
            }
            return false;
        }

        private async Task<bool> UnlockAuthorizationAsync(TransactionMovementBody body)
        {
            string url = "api/deposit/unlock";

            if (SecurityConfiguration.WasConfigure() && SecurityConfiguration.ItsSecuritySchemeConfigured())
            {
                _postTransactionClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", Refund.CashierToken.access_token);
            }

            string jsonString = System.Text.Json.JsonSerializer.Serialize(body);
            var content = new StringContent(jsonString, System.Text.Encoding.UTF8, "application/json");

            using (HttpResponseMessage response = await _postTransactionClient.PostAsync(url, content))
            {
                string responseString = await response.Content.ReadAsStringAsync();
                if (response.IsSuccessStatusCode)
                {
                    return true;
                }
                return false;
            }
        }

        public sealed class DoneResponse : Result
        {
            public bool Done { get; set; }

            public DoneResponse(bool isDone, string entity, PaymentMethod paymentMethod, string[] currencyIsoCode, TransactionType transactionType) : base(entity, paymentMethod, currencyIsoCode, transactionType)
            {
                Done = isDone;
            }
        }
    }
}
