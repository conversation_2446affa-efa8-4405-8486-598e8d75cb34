﻿using GamesEngine.Domains;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using System;

namespace GamesEngine.Business.Liquidity.Transactions
{
    internal class Deposit : Transaction
    {
        internal string ConfirmedCurrency { get; private set; } 
        internal decimal ConfirmedAmount { get; private set; }
        internal decimal Amount { get; private set; }
        internal string Address { get; private set; }
        internal string Kind { get; private set; }
        internal string InvoiceId { get; private set; }
        internal int AuthorizationId { get; private set; }
        internal int ExternalReference { get; private set; }
        internal string ExternalAtAddress { get; private set; } = string.Empty;
        internal string Destination { get; private set; }
        internal string PaymentLink { get; private set; } = string.Empty;        
        internal int StoreId { get; private set; }
        internal Domain Domain { get; private set; }

        internal Deposit(int id, string address, DateTime createdAt, string kind, string invoiceId, int authorizationId, string externalAtAddress, int externalReference, string destination, decimal exchangeAmount, decimal exchangeRate, string confirmedCurrency, decimal confirmedAmount, int storeId, Domain domain):base(id, createdAt, exchangeRate)
        {
            if (id <= 0) throw new ArgumentNullException(nameof(id));
            if (string.IsNullOrWhiteSpace(address)) throw new ArgumentNullException(nameof(address));
            if (string.IsNullOrWhiteSpace(kind)) throw new ArgumentNullException(nameof(kind));
            if (string.IsNullOrWhiteSpace(invoiceId)) throw new ArgumentNullException(nameof(invoiceId));
            if (authorizationId <= 0) throw new ArgumentNullException(nameof(authorizationId));
            if (externalReference <= 0) throw new ArgumentNullException(nameof(externalReference));
            if (string.IsNullOrWhiteSpace(destination)) throw new ArgumentNullException(nameof(destination));
            if (exchangeAmount <= 0) throw new ArgumentNullException(nameof(exchangeAmount));
            if (exchangeRate <= 0) throw new ArgumentNullException(nameof(exchangeRate));
            if (string.IsNullOrWhiteSpace(confirmedCurrency)) throw new ArgumentNullException(nameof(confirmedCurrency));
            if (confirmedAmount <= 0) throw new ArgumentNullException(nameof(confirmedAmount));
            if (storeId <= 0) throw new ArgumentNullException(nameof(storeId));
            if (domain == null) throw new ArgumentNullException(nameof(domain), "Domain cannot be null.");

            Address = address;
            Kind = kind;
            InvoiceId = invoiceId;
            AuthorizationId = authorizationId;
            ExternalReference = externalReference;
            ExternalAtAddress = externalAtAddress;
            Destination = destination;
            ConfirmedCurrency = confirmedCurrency;
            ConfirmedAmount = confirmedAmount;
            Amount = exchangeAmount;
            StoreId = storeId;//PASARLO AL ConfirmationsInboundTask
            Domain = domain;//PASARLO AL ConfirmationsInboundTask
        }
    }
}
