﻿using GamesEngine.Settings;
using System.Collections.Concurrent;

namespace LiquidityAPI
{
    public class ExchangeRate
    {
        private readonly ConcurrentDictionary<string, decimal> exchangeRates = new();
        public static readonly ExchangeRate LastKnown = new ExchangeRate();
        private ExchangeRate() { }

        public TResultExchangeRate this[string currencyCode]
        {
            get
            {
                if (string.IsNullOrWhiteSpace(currencyCode))
                    throw new ArgumentNullException(nameof(currencyCode));

                if (!exchangeRates.TryGetValue(currencyCode, out decimal exchange))
                {
                    var result = RetrieveCurrent(currencyCode);
                    if (!result.IsSuccess) return result;

                    exchange = result.Value;
                }
                return TResultExchangeRate.Success(exchange);
            }
        }

        internal TResultExchangeRate RetrieveCurrent(string currencyCode)
        {
            if (string.IsNullOrWhiteSpace(currencyCode))
                return TResultExchangeRate.Fail("Currency code is null or whitespace.");

            var result = RetrieveExchangeRateFrom(currencyCode);
            if (result.IsSuccess)
            {
                exchangeRates[currencyCode] = result.Value;
            }
            return result;
        }

        private TResultExchangeRate RetrieveExchangeRateFrom(string currencyCode)
        {
            try
            {
                var resultRate = PaymentManager.ExchangeRateAsync(currencyCode, $"{GamesEngine.Finance.Currencies.CODES.USD}").GetAwaiter().GetResult();
                if (resultRate == null)
                    return TResultExchangeRate.Fail($"Exchange rate from {currencyCode} to USD not found.");

                return TResultExchangeRate.Success(resultRate.Rate);
            }
            catch (Exception ex)
            {
                return TResultExchangeRate.Fail(ex.Message);
            }
        }

        internal void AddOrUpdateRate(string currencyCode, decimal rate)
        {
            if (string.IsNullOrWhiteSpace(currencyCode))
                throw new ArgumentNullException(nameof(currencyCode));
            if (rate < 0)
                throw new ArgumentOutOfRangeException(nameof(rate), "Rate must be non-negative.");

            exchangeRates[currencyCode] = rate;
        }
    }

    public struct TResultExchangeRate
    {
        public bool IsSuccess { get; }
        public decimal Value { get; }
        public string ErrorMessage { get; }

        private TResultExchangeRate(bool isSuccess, decimal value, string errorMessage)
        {
            IsSuccess = isSuccess;
            Value = value;
            ErrorMessage = errorMessage;
        }

        public static TResultExchangeRate Success(decimal value)
        {
            return new TResultExchangeRate(true, value, null);
        }

        public static TResultExchangeRate Fail(string errorMessage)
        {
            return new TResultExchangeRate(false, default, errorMessage);
        }
    }
}
