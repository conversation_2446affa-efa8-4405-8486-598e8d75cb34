﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamesEngine.RealTime.Events
{
    internal abstract class LiquidityTransactionEvent : PlatformEvent
    {
        public int Id { get; set; }

        internal LiquidityTransactionEvent(int id, PlatformEventType eventType) : base(eventType)
        {
            if (id <= 0) throw new ArgumentNullException(nameof(id));
            Id = id;
        }
    }

    internal class WithdrawalCanceledEvent : LiquidityTransactionEvent
    {
        internal WithdrawalCanceledEvent(int id) : base(id, PlatformEventType.LIQUIDITY_WITHDRAWAL_HAS_BEEN_CANCELED)
        {
        }
    }

    internal class WithdrawalClaimedEvent : LiquidityTransactionEvent
    {
        internal WithdrawalClaimedEvent(int id) : base(id, PlatformEventType.LIQUIDITY_WITHDRAWAL_HAS_BEEN_CLAIMED)
        {
        }
    }

    internal class DepositCanceledEvent : LiquidityTransactionEvent
    {
        internal DepositCanceledEvent(int id) : base(id, PlatformEventType.LIQUIDITY_DEPOSIT_HAS_BEEN_CANCELED)
        {
        }
    }

    internal class DepositConfirmedEvent : LiquidityTransactionEvent
    {
        internal DepositConfirmedEvent(int id) : base(id, PlatformEventType.LIQUIDITY_DEPOSIT_HAS_BEEN_CONFIRMED)
        {
        }
    }
}
