﻿using System.Collections.Generic;
using System;
using System.Linq;

namespace GamesEngine.RealTime
{
    internal class SlidingWindow<T>
    {
        private readonly TimeSpan windowSize;
        private readonly Func<T, DateTime> getTimestamp;
        private readonly Queue<T> items = new();

        public SlidingWindow(TimeSpan windowSize, Func<T, DateTime> getTimestamp)
        {
            this.windowSize = windowSize;
            this.getTimestamp = getTimestamp ?? throw new ArgumentNullException(nameof(getTimestamp));
        }

        public void Add(T item, DateTime now)
        {
            if (item == null) throw new ArgumentNullException(nameof(item));
            var itemTime = getTimestamp(item);

            if (itemTime > now)
                throw new ArgumentException("Item timestamp cannot be in the future.", nameof(item));

            items.Enqueue(item);
            PurgeOld(now);
        }

        public IEnumerable<T> GetRecent(DateTime now)
        {
            return items
                .Where(x => (now - getTimestamp(x)) <= windowSize && getTimestamp(x) <= now);
                
        }

        private void PurgeOld(DateTime now)
        {
            while (items.Count > 0 && (now - getTimestamp(items.Peek())) > windowSize)
            {
                items.Dequeue();
            }
        }
    }
}
