﻿using System;

namespace GamesEngine.RealTime.Events
{
    internal abstract class TankerChangeEvent : ContainerChangeEvent
    {
        internal TankerChangeEvent(int tankerId, PlatformEventType eventType) : base(tankerId, eventType)
        {
            if (tankerId <= 0) throw new ArgumentNullException(nameof(tankerId));
        }
    }

    internal class TankerHeaderInfoHasChangeEvent : TankerChangeEvent
    {
        internal TankerHeaderInfoHasChangeEvent(int tankerId) : base(tankerId, PlatformEventType.TANKER_HEADER_INFO_HAS_CHANGE)
        {
        }
    }

    internal class TankerChangeColorEvent : TankerChangeEvent
    {
        internal TankerChangeColorEvent(int tankerId) : base(tankerId, PlatformEventType.TANKER_COLOR_HAS_CHANGE)
        {
        }
    }

    internal class TankerArchivedEvent : TankerChangeEvent
    {
        internal TankerArchivedEvent(int tankerId) : base(tankerId, PlatformEventType.TANKER_ARCHIVED)
        {
        }
    }

    internal class TankerDispatchedEvent : TankerChangeEvent
    {
        internal TankerDispatchedEvent(int tankerId) : base(tankerId, PlatformEventType.TANKER_DISPATCHED)
        {
        }
    }

    internal class TankerDepositHasBeenAddedEvent : TankerChangeEvent
    {
        internal TankerDepositHasBeenAddedEvent(int tankerId) : base(tankerId, PlatformEventType.DEPOSIT_ADDEED_TO_TANKER)
        {
        }
    }

    internal class TankHaveBeenMovedToTankerEvent : TankerChangeEvent
    {
        internal TankHaveBeenMovedToTankerEvent(int tankerId) : base(tankerId, PlatformEventType.TANK_HAVE_BEEN_MOVE_TO_TANKER)
        {
        }
    }

    internal class DisburdenTankerEvent : TankerChangeEvent
    {
        internal DisburdenTankerEvent(int tankerId) : base(tankerId, PlatformEventType.DISBURDEN_TANKER)
        {
            if (tankerId <= 0) throw new ArgumentNullException(nameof(tankerId));
        }
    }

    internal class UndisburdenTankerEvent : TankerChangeEvent
    {
        internal UndisburdenTankerEvent(int tankerId) : base(tankerId, PlatformEventType.UNDISBURDEN_TANKER)
        {
            if (tankerId <= 0) throw new ArgumentNullException(nameof(tankerId));
        }
    }

    internal class TankerPartialDispatchedEvent : TankerChangeEvent
    {
        internal TankerPartialDispatchedEvent(int tankerId) : base(tankerId, PlatformEventType.TANKER_PARTIAL_DISPATCHED)
        {
            if (tankerId <= 0) throw new ArgumentNullException(nameof(tankerId));
        }
    }

    internal class TankerSealedEvent : TankerChangeEvent
    {
        internal TankerSealedEvent(int tankerId) : base(tankerId, PlatformEventType.TANKER_SEALED)
        {
            if (tankerId <= 0) throw new ArgumentNullException(nameof(tankerId));
        }
    }

    internal class CreatedTankerEvent : TankerChangeEvent
    {
        internal CreatedTankerEvent(int tankerId) : base(tankerId, PlatformEventType.CREATED_TANKER)
        {
        }
    }
}
