﻿using GamesEngine.Business.Liquidity.Sentinels;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;

namespace GamesEngine.Business.Liquidity
{
    internal class Liquid : Objeto
    {   
        internal Liquid(string kind, LiquidFlow parentFlow, DateTime now)
        {
            if (string.IsNullOrWhiteSpace(kind)) throw new ArgumentException("Kind cannot be null or empty.", nameof(kind));
            if (parentFlow == null) throw new ArgumentNullException(nameof(parentFlow));
            if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now));

            Kind = kind;
            Source = new Source(this);
            Outlet = new Outlet(this, now);

            ParentFlow = parentFlow;
            IngressSentinel = IngressSentinel.IngressSentinelByKind(this, kind);
            EgressSentinel = EgressSentinel.EgressSentinelByKind(this, kind);

            // CREATE TASK FOR RATES
            IngressSentinel.StartRatesTask(kind);
        }

        internal IngressSentinel IngressSentinel { get; private set; }
        internal EgressSentinel EgressSentinel { get; private set; }

        internal string Kind { get; private set; }

        internal LiquidFlow ParentFlow { get; private set; }

        internal Outlet Outlet { get; private set; }
        internal Source Source { get; private set; }
        internal IEnumerable<Source> Sources => new List<Source> { Source };

        internal void NotifyExchangeRateChange(DateTime now, decimal rate)
        {
            if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now));
            if (rate <= 0) throw new ArgumentNullException(nameof(rate));

            var exchangeRateChangeEvent = new ExchangeRateChangeEvent(now, Kind, rate);
            PlatformMonitor.GetInstance().WhenNewEvent(exchangeRateChangeEvent);
        }

        internal decimal AmountTo(DateTime moment)
        {
            throw new NotImplementedException();
        }

        internal bool ExistXpub(string xpub)
        {
            if (string.IsNullOrWhiteSpace(xpub)) throw new GameEngineException(nameof(xpub));
            return Source.ExsitXpub(xpub);
        }

        internal Source AddSource(Xpub xpub)
        {
            if (xpub == null) throw new ArgumentNullException(nameof(xpub));

            bool existInOtherLiquid = ParentFlow.ExistXpub(xpub.Value);
            if (existInOtherLiquid) throw new GameEngineException($"The xpub: {xpub.Value} is already associated with an existing source in another liquid.");

            Source.AddXpub(xpub);
            return Source;
        }

        internal void RemoveSource(Xpub xpub)
        {
            throw new NotImplementedException();
            //var source = _sources.FirstOrDefault(s => s.Xpub.Value == xpub.Value);
            //if (source != null) _sources.Remove(source);
        }
       
    }
}
