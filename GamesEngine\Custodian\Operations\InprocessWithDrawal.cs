﻿using GamesEngine.Exchange;
using GamesEngine.Finance;
using GamesEngine.Settings;
using System;
using System.Collections.Generic;
using town.connectors.drivers;

namespace GamesEngine.Custodian.Operations
{
	internal sealed class InprocessWithDrawal : WithDrawal
	{
		private readonly Guardian guardian;
		internal int Approvals { get; }
		internal bool HasExecutions { get; private set; }
		internal int ApprovalsRequired { get; }
		internal RequiredProfilesApproval RequiredProfilesApproval { get; }
		public InprocessWithDrawal(Guardian guardian, PendingWithDrawal withdrawal) :
			base(guardian, 
				StatusCodes.IN_PROCESS,
				TransactionType.Withdrawal,
				withdrawal.CreationDate,
				withdrawal.TransactionId,
				withdrawal.ReferenceNumber,
				withdrawal.DisbursementAmount,
				withdrawal.Group,
				withdrawal.Processor,
				withdrawal.Domain,
				withdrawal.Disbursements,
				withdrawal.Identifier,
				withdrawal.AccountNumber,
				withdrawal.EmployeeName,
                withdrawal.DestinationAddress)
		{
			if (withdrawal == null) throw new ArgumentException(nameof(PendingWithDrawal));
			if (withdrawal.ApproversWhoAlreadyApprove == null || withdrawal.ApproversWhoAlreadyApprove.Count()<=0) throw new GameEngineException($"The transaction {withdrawal.TransactionId} it's not completly approved.");
			if (withdrawal.ApprovalsRequired > withdrawal.Approvals) throw new GameEngineException($"The {nameof(PendingWithDrawal)} {withdrawal.TransactionId} it's not completly approved.");
			//if (!withdrawal.Scheduled) throw new GameEngineException($"The {nameof(PendingWithDrawal)} {withdrawal.TransactionId} it's not scheduled yet.");

			ApproversWhoAlreadyApprove = withdrawal.ApproversWhoAlreadyApprove;
			Approvals = withdrawal.Approvals; 
			ApprovalsRequired = withdrawal.ApprovalsRequired;
			Scheduled = withdrawal.Scheduled;
			this.guardian = guardian;
			RequiredProfilesApproval = withdrawal.RequiredProfilesApproval;
			this.HasExecutions = false;
		}
		internal bool WasCreatedFrom(WithDrawal withdrawal)
		{
			return (TransactionId == withdrawal.TransactionId);
		}
		internal override InternalOperationUpdateMessage GenerateMessage(DateTime now)
		{
			Approvers approvers = (ApproversWhoAlreadyApprove == null) ? new Approvers() : ApproversWhoAlreadyApprove;

			return new InternalOperationUpdateMessage(
				Abbreviation,
				CreationDate,
				now,
				TransactionId,
				ProcessorAccount.Id,
				ReferenceNumber,
				Description,
				DisbursementAmount,
				Group,
				Processor.Driver.Id,
				Approvals, 
				ApprovalsRequired,
				Scheduled,
				Domain.Url,
				approvers,
				Status,
				HasExecutions,
				CalculatePercentajeOfExecution(),
				Identifier,
                AccountNumber,
				DestinationAddress
                );
		}
		internal InternalOperationUpdateMessageWithProfiles GenerateMessageWithProfiles(DateTime now)
		{
			Approvers approvers = (ApproversWhoAlreadyApprove == null) ? new Approvers() : ApproversWhoAlreadyApprove;

			return new InternalOperationUpdateMessageWithProfiles(
				Abbreviation,
				CreationDate,
				now,
				TransactionId,
				ProcessorAccount.Id,
				ReferenceNumber,
				Description,
				DisbursementAmount,
				Group,
				Processor.Driver.Id,
				Approvals,
				ApprovalsRequired,
				Scheduled,
				Domain.Url,
				approvers,
				Status,
				RequiredProfilesApproval.ListProfileIds(),
				HasExecutions,
				CalculatePercentajeOfExecution(),
				Identifier,
                AccountNumber,
				DestinationAddress
                );
		}
		internal CompleteWithdrawal Complete()
		{
			return new CompleteWithdrawal(guardian, this);
		}
		internal void ChangeToHasAtLeastOneExecution()
		{
			HasExecutions = true;
		}
	}
}
