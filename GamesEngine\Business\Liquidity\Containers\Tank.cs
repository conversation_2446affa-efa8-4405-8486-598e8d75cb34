﻿using GamesEngine.Business.Liquidity.Transactions;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using static GamesEngine.Business.Liquidity.Containers.Tanker;

namespace GamesEngine.Business.Liquidity.Containers
{
    internal abstract class Tank : Container
    {
        protected List<EnclosureDeposit> enclosureDeposits;
        protected List<Tank> tanks;
        protected int partentTankerId = 0;

        internal Tank(int id, string name, string description, DateTime createdAt, int version, string kind, Liquid liquid, List<EnclosureDeposit> enclosureDeposits, List<Tank> tanks) : base(id, kind,liquid, name, description, createdAt)
        {
            if (version < 0) throw new ArgumentNullException(nameof(version));
            if (enclosureDeposits == null) throw new ArgumentNullException(nameof(enclosureDeposits));
            if (tanks == null) throw new ArgumentNullException(nameof(tanks));
            if (createdAt == DateTime.MinValue) throw new ArgumentNullException(nameof(createdAt));

            this.version = version;
            this.enclosureDeposits = enclosureDeposits;
            this.tanks = tanks;

            RecalculateAmount();            
        }

        internal Tank(int id, string name, string description, DateTime createdAt, int version, string kind, Liquid liquid) : base(id, kind, liquid, name, description, createdAt)
        {
            if (version < 0) throw new ArgumentNullException(nameof(version));
            if (createdAt == DateTime.MinValue) throw new ArgumentNullException(nameof(createdAt));

            this.version = version;
            this.enclosureDeposits = new List<EnclosureDeposit>();
            this.tanks = new List<Tank>();

            RecalculateAmount();
        }

        internal Tank(int id, string name, string description, DateTime createdAt, int version, string kind, Liquid liquid, List<Tank> tankList) : base(id, kind, liquid, name, description, createdAt)
        {
            if (version < 0) throw new ArgumentNullException(nameof(version));
            if (createdAt == DateTime.MinValue) throw new ArgumentNullException(nameof(createdAt));
            if (tankList == null) throw new ArgumentNullException(nameof(tanks));
            if (tankList.Count == 0) throw new ArgumentException("The tank list cannot be empty.", nameof(tanks));

            this.version = version;
            this.enclosureDeposits = new List<EnclosureDeposit>();

            List<Tank> delegatedTanks = new List<Tank>();
            foreach (var _tank in tankList)
            {
                var newDelegatedTank = _tank.Delegate() as Tank;
                newDelegatedTank.Parent = this;
                delegatedTanks.Add(newDelegatedTank);
            }
            this.tanks = delegatedTanks;

            RecalculateAmount();
        }

        internal void SetParentTanker(int parentTankerId)
        {
            if(parentTankerId <=0) throw new ArgumentOutOfRangeException(nameof(parentTankerId), "Parent Tanker ID must be greater than zero.");
            partentTankerId = parentTankerId;
        }

        internal bool IsInsideTanker()
        {
           return partentTankerId > 0;
        }

        internal Tank(int id, string name, string description, DateTime createdAt, int version, string kind, Liquid liquid, List<EnclosureDeposit> deposits, List<Tank> tanks, string color, DateTime targetDate, GoalAmount goalAmount) : this(id, name, description, createdAt, version, kind,liquid, deposits, tanks)
        {
            GoalAmount = goalAmount;
            TargetDate = targetDate;
            SetColor(color);
        }

        internal Tank? Parent { get; private set; }

        internal string Path
        {
            get
            {
                if (Parent == null) return $"/{Name}";
                return $"{Parent.Path}/{Name}";
            }
        }

        internal IEnumerable<ParentInfoDto> ParentChain
        {
            get
            {
                var result = new List<ParentInfoDto>();
                var current = this.Parent;
                while (current != null)
                {
                    result.Add(new ParentInfoDto(current.Id, current.Name));
                    current = current.Parent;
                }
                result.Reverse();
                return result;
            }
        }


        internal Source Source => Liquid.Source;

        internal void UpdateName(bool itIsThePresent, DateTime now, string name)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentException("Name cannot be null or whitespace.", nameof(name));
            this.Name = name;                        
        }

        internal void UpdateDescription(bool itIsThePresent, DateTime now, string description)
        {
            this.Description = description ?? string.Empty;            
        }

        internal DateTime TargetDate { get; private set; } = DateTime.MinValue;

        internal IEnumerable<EnclosureDeposit> EnclousureDeposits => enclosureDeposits;

        internal IEnumerable<Deposit> Deposits
        {
            get
            {
                var deposits = new List<Deposit>();
                foreach (var ed in enclosureDeposits)
                {
                    deposits.Add(ed.Deposit);
                }
                return deposits;
            }
        }

        internal IEnumerable<Deposit> ExplandedDeposits
        {
            get
            {
                List<Deposit> depositsResults = new List<Deposit>();
                if (this.enclosureDeposits != null)
                {
                    foreach (var _d in Deposits)
                    {
                        depositsResults.Add(_d);
                    }
                }
                if (this.tanks != null)
                {
                    foreach (var tank in this.tanks)
                    {
                        if (tank.enclosureDeposits != null)
                        {
                            depositsResults.AddRange(tank.ExplandedDeposits);                            
                        }
                    }
                }
                return depositsResults;
            }
        }

        internal int TotalDeposits => ExplandedDeposits.Count();

        internal bool ContainsTank(Tank tank)
        {
            if (tank == null) throw new ArgumentNullException(nameof(tank));

            return tanks.Contains(tank);
        }

        internal IEnumerable<int> DescendentChildrenId()
        {
            var result = new List<int>();
            if (tanks != null)
            {
                foreach (var child in tanks)
                {
                    result.Add(child.Id);
                    result.AddRange(child.DescendentChildrenId());
                }
            }
            return result;
        }

        internal TankSummary BuildMonthlySummary(DateTime startDate,DateTime endDate,string name = null,string status = null, string color = null)
        {
            var filteredTanks = this.tanks.Where(t => !(t is TankRoot));
            var totalTanks = filteredTanks.Count();
            if (startDate != DateTime.MinValue)
                filteredTanks = filteredTanks.Where(t => t.CreatedAt >= startDate);
            if (endDate != DateTime.MaxValue)
                filteredTanks = filteredTanks.Where(t => t.CreatedAt <= endDate.AddDays(1));
            if (!string.IsNullOrWhiteSpace(name))
                filteredTanks = filteredTanks.Where(t => t.Name.IndexOf(name, StringComparison.OrdinalIgnoreCase) >= 0);
            if (!string.IsNullOrWhiteSpace(status))
                filteredTanks = filteredTanks.Where(t => t.Type.Equals(status, StringComparison.OrdinalIgnoreCase));
            if (!string.IsNullOrWhiteSpace(color))
                filteredTanks = filteredTanks.Where(t => t.ContainerColor.Equals(color, StringComparison.OrdinalIgnoreCase));

            var tankGroups = filteredTanks
                .GroupBy(tank => tank.CreatedAt.ToString("MM/yyyy"))
                .ToDictionary(g => g.Key, g => g.ToList());

            var ownDepositGroups = this.Deposits
                .GroupBy(dep => dep.CreatedAt.ToString("MM/yyyy"))
                .ToDictionary(g => g.Key, g => g.ToList());

            var rootTank = this.tanks.OfType<TankRoot>().FirstOrDefault();
            var rootDepositGroups = new Dictionary<string, List<Deposit>>();
            if (rootTank != null)
            {
                rootDepositGroups = rootTank.ExplandedDeposits
                    .GroupBy(dep => dep.CreatedAt.ToString("MM/yyyy"))
                    .ToDictionary(g => g.Key, g => g.ToList());
            }

            var allMonthKeys = tankGroups.Keys
                .Union(ownDepositGroups.Keys)
                .Union(rootDepositGroups.Keys)
                .OrderBy(k => k)
                .ToList();

            var monthlySummaries = new List<MonthlyTankSummary>();
            
            foreach (var key in allMonthKeys)
            {
                var tanksInMonth = tankGroups.ContainsKey(key) ? tankGroups[key] : new List<Tank>();

                var depositsInMonth = new List<Deposit>();
                if (ownDepositGroups.ContainsKey(key))
                    depositsInMonth.AddRange(ownDepositGroups[key]);
                if (rootDepositGroups.ContainsKey(key))
                    depositsInMonth.AddRange(rootDepositGroups[key]);
                var tankDetails = tanksInMonth
                    .Select(t => new TankDetail(t.Id, t.Name, t.Type, t.Amount, t.ReceivedAmount,t.ContainerColor, t.CreatedAt))
                    .ToList();
                var depositDetails = depositsInMonth
                    .Select(d => new DepositDetail(d.Id, d.InvoiceId, d.Amount, d.ConfirmedAmount, d.CreatedAt))
                    .ToList();

                var monthTotalReceivedAmount = tankDetails.Sum(t => t.ReceivedAmount) + depositDetails.Sum(d => d.ReceivedAmount);
                monthlySummaries.Add(new MonthlyTankSummary(key, monthTotalReceivedAmount, tankDetails, depositDetails));
            }
            return new TankSummary(this.Amount, this.ReceivedAmount, monthlySummaries, totalTanks, this.TotalDeposits);
        }
       
        internal bool ExistDeposit(int depositId)
        {
            if (depositId <= 0) throw new ArgumentOutOfRangeException(nameof(depositId), "Deposit ID must be greater than zero.");
            return this.ExplandedDeposits.Any(d => d.Id == depositId);
        }

        internal Deposit FindDepositById(int depositId)
        {
            if (depositId <= 0) throw new ArgumentOutOfRangeException(nameof(depositId), "Deposit ID must be greater than zero.");
            var deposit = this.ExplandedDeposits.FirstOrDefault(d => d.Id == depositId);
            if (deposit == null) throw new GameEngineException($"Deposit with ID {depositId} does not exist in this tank.");
            
            return deposit;
        }

        internal EnclosureDeposit FindEnclosureDeposit(int depositId)
        {
            if (depositId <= 0) throw new ArgumentNullException(nameof(depositId));
            var enclosureDeposit = this.enclosureDeposits.FirstOrDefault(d => d.Id == depositId);
            if (enclosureDeposit == null) throw new GameEngineException($"Enclosure Deposit with ID {depositId} does not exist in this tank.");
            return enclosureDeposit;
        }

        internal bool BuildDepositsBetween(DateTime from, DateTime to)
        {
            if (from == DateTime.MinValue && to == DateTime.MinValue) throw new ArgumentNullException(nameof(from), "Both 'from' and 'to' dates cannot be MinValue.");
            if (this.MinDate == DateTime.MaxValue && this.MaxDate == DateTime.MinValue)
               return false;
            return this.MinDate >= from && this.MaxDate <= to;

        }

        internal bool BuildDepositsFrom(DateTime startDate)
        {
            if (startDate == DateTime.MinValue) throw new ArgumentNullException(nameof(startDate));
            return BuildDepositsBetween(startDate, DateTime.MaxValue);
        }

        internal bool BuildDepositsUpTo(DateTime endDate)
        {
            if (endDate == DateTime.MinValue) throw new ArgumentNullException(nameof(endDate));
            return BuildDepositsBetween(DateTime.MinValue, endDate);
        }

        protected override DateTime CalculateMinDate()
        {
            

            DateTime result = DateTime.MaxValue;
            if (enclosureDeposits != null && enclosureDeposits.Count > 0)
            {
                result = enclosureDeposits.Min(d => d.CreatedAt);
            }
            if (tanks != null && tanks.Count > 0)
            {
                foreach (var tank in tanks)
                {
                   
                    DateTime childMinDate = tank.MinDate;
                    if (childMinDate < result)
                    {
                        result = childMinDate;
                    }
                }
            }

            return result;
        }
        protected override DateTime CalculateMaxDate()
        {
            DateTime result = DateTime.MinValue;
            if (enclosureDeposits != null && enclosureDeposits.Count > 0)
            {
                result = enclosureDeposits.Max(d => d.CreatedAt);
            }
            if (tanks != null && tanks.Count > 0)
            {
                foreach (var tank in tanks)
                {
                    DateTime childMaxDate = tank.MaxDate;
                    if (childMaxDate > result)
                    {
                        result = childMaxDate;
                    }
                }
            }
            return result;
        }

        internal class FlatterTank : Objeto
        {
            public int Id { get; private set; }
            public string Name { get; private set; }
            public string Path { get; private set; }
            public decimal Amount { get; private set; }
            public decimal ReceivedAmount { get; private set; }
            public string Description { get; private set; }
            public string ContainerColor { get; private set; }
            public FlatterTank(int id, string name, decimal currentAmount,decimal receivedAmount, string description,string containerColor, string path)
            {
                if (id <= 0) throw new ArgumentOutOfRangeException(nameof(id), "Id must be greater than zero.");
                if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name), "Name cannot be null or empty.");
                if (currentAmount < 0) throw new ArgumentOutOfRangeException(nameof(currentAmount), "Current amount cannot be negative.");
                if (receivedAmount < 0) throw new ArgumentOutOfRangeException(nameof(receivedAmount), "ReceivedAmount amount cannot be negative.");
                if (string.IsNullOrWhiteSpace(path)) throw new ArgumentNullException(nameof(path), "Path cannot be null or empty.");
                Id = id;
                Name = name;
                Path = path;
                Amount = currentAmount;
                ReceivedAmount = receivedAmount;
                Description = description ?? string.Empty;
                ContainerColor = containerColor;
            }
        }

        internal IEnumerable<FlatterTank> FlattenedList
        {
            get
            {
                List<FlatterTank> result = new List<FlatterTank>();
                if (tanks != null)
                {
                    foreach (var child in tanks)
                    {
                        var flattenedList = GetFlattenedList(child, $"/{Name}");
                        result.AddRange(flattenedList);
                    }
                }
                return result;
            }
        }

        private IEnumerable<FlatterTank> GetFlattenedList(Tank current, string name)
        {
            string currentPath = string.IsNullOrEmpty(name) ? current.Name : $"{name}/{current.Name}";

            yield return new FlatterTank(current.Id, current.Name, current.Amount,current.ReceivedAmount,current.Description, current.ContainerColor, currentPath);

            if (this.tanks != null)
            {
                foreach (var child in current.tanks)
                {
                    foreach (var item in GetFlattenedList(child, currentPath))
                    {
                        yield return item;
                    }
                }
            }
        }

        internal bool ContainsRecursive(TankReady target)
        {
            if (tanks.Any(t => t.Id == target.Id)) return true;

            return tanks.Any(c => c.ContainsRecursive(target));
        }

        internal IEnumerable<Tank> DescendantTanks(bool includeThis)
        {
            List<Tank> tankDescendants = new List<Tank>();
            if (includeThis) tankDescendants.Add(this);

            foreach (var childTank in this.tanks)
            {
                tankDescendants.Add(childTank);

                foreach (var sub in childTank.DescendantTanks(false))
                {
                    tankDescendants.Add(sub);
                }
            }
            return tankDescendants;
        }

        public class MonthlyTankSummary : Objeto
        {
            public string Month { get; }
            public decimal MonthTotal { get; }
            public IEnumerable<DepositDetail> Deposits { get; }
            public IEnumerable<TankDetail> Tanks { get; }


            public MonthlyTankSummary(string month, decimal monthTotal, IEnumerable<TankDetail> tanks, IEnumerable<DepositDetail> deposits)
            {
                Month = month;
                MonthTotal = monthTotal;
                Deposits = deposits ?? new List<DepositDetail>();
                Tanks = tanks ?? new List<TankDetail>();
            }
        }

        public class TankSummary : Objeto
        {
            public decimal TankTotalAmount { get; }
            public decimal TotalReceivedAmount { get; }
            public int TotalTanks { get; }
            public int TotalDeposits { get; }

            public IEnumerable<MonthlyTankSummary> MonthlySummaries { get; }

            public TankSummary(decimal tankTotal, decimal totalReceivedAmount, IEnumerable<MonthlyTankSummary> monthlySummaries,int totalTanks, int totalDeposits)
            {
                TankTotalAmount = tankTotal;
                TotalReceivedAmount = totalReceivedAmount;
                MonthlySummaries = monthlySummaries;
                TotalDeposits = totalDeposits;
                TotalTanks = totalTanks;
            }
        }

        internal class TankReady : Tank
        {
            internal TankReady(int id, string name, string description, DateTime createdAt, int version, string kind,Liquid liquid, List<EnclosureDeposit> enclosureDeposits) :
                base(id, name, description, createdAt, version, kind, liquid, enclosureDeposits, new List<Tank>())
            {
            }

            internal TankReady(int id, string name, string description, DateTime createdAt, int version, string kind, Liquid liquid, List<Tank> tankList) :
                base(id, name, description, createdAt, version, kind, liquid, tankList)
            {
            }

            internal TankReady(int id, string name, string description, DateTime createdAt, int version, string kind, Liquid liquid) :
                base(id, name, description, createdAt, version, kind, liquid)
            {
            }

            protected override TankReady Delegate()
            {
                TankReady newTankReady = new TankReady(Id, Name, Description, CreatedAt, Version + 1, Kind, Liquid, this.enclosureDeposits.ToList());
               
                newTankReady.partentTankerId = partentTankerId;
                newTankReady.tanks = tanks.ToList();

                newTankReady.Parent = Parent;
                if (newTankReady.Parent != null)
                {
                    
                    Parent.tanks.Remove(this);
                    Parent.tanks.Add(newTankReady);
                }

                foreach (var _tank in this.tanks)
                {
                    if (_tank.Parent == this) _tank.Parent = newTankReady;
                }

                newTankReady.previous = this;
                next = newTankReady;

                Source.AddOrUpdateTank(newTankReady);
                return newTankReady;
            }

            internal TankReady MergeWith(bool itIsThePresent, int draftTankId, DateTime createdAt, TankReady otherTank)
            {
                if (otherTank == null) throw new ArgumentException("Cannot merge with null tank.");
                if (otherTank == this) throw new ArgumentException("Cannot merge with the same tank.");
                if (otherTank.ExplandedDeposits.Count() == 0) throw new ArgumentException("Cannot merge with an empty tank.");

                string name = "Merged Tank " + draftTankId;
                string description = $"Merged Tank from {this.Name} and {otherTank.Name}";

                List<EnclosureDeposit> mergedDeposits = new List<EnclosureDeposit>(this.enclosureDeposits);
                mergedDeposits.AddRange(otherTank.enclosureDeposits);

                var result = new TankReady(draftTankId, name, description, createdAt, 1, Kind, Liquid, mergedDeposits);
                result.tanks = this.tanks.ToList();
                RecalculateAmount();

                Source.AddOrUpdateTank(result);
                TankHasBeenMergeEvent eventMsg = new TankHasBeenMergeEvent(result.Id);
                PlatformMonitor.GetInstance().WhenNewEvent(eventMsg);

                TankDiscarded thisTankDiscarded = new TankDiscarded(this);
                Source.AddOrUpdateTank(thisTankDiscarded);
                TankDiscarded tankDiscarded = new TankDiscarded(otherTank);
                Source.AddOrUpdateTank(tankDiscarded);
                return result;
            }

            internal TankReady MergeWith(TankReady tankReady)
            {
                if (tankReady == null) throw new ArgumentException("Cannot merge with null tank.");
                if (tankReady.ExplandedDeposits.Count() == 0) throw new ArgumentException("Cannot merge with an empty tank.");

                var newDelegatedTank = Delegate();

                newDelegatedTank.enclosureDeposits.AddRange(tankReady.enclosureDeposits.ToList());
                newDelegatedTank.tanks.AddRange(tankReady.tanks.ToList());

                newDelegatedTank.RecalculateAmount();
                TankDepositHasBeenAddedEvent eventMsg = new TankDepositHasBeenAddedEvent(Id);
                PlatformMonitor.GetInstance().WhenNewEvent(eventMsg);

                TankDiscarded tankDiscarded = new TankDiscarded(tankReady);
                Source.AddOrUpdateTank(tankDiscarded);
                return newDelegatedTank;
            }

            internal void AddDeposits(IEnumerable<EnclosureDeposit> enclosureDepositsToAdd)
            {
                if (enclosureDepositsToAdd == null || !enclosureDepositsToAdd.Any()) throw new ArgumentNullException(nameof(enclosureDepositsToAdd));

                var newDelegatedTank = Delegate();

                foreach (var enclosureDeposit in enclosureDepositsToAdd)
                {
                    if (enclosureDeposit == null) throw new GameEngineException("The Deposit is null.");
                    if (enclosureDeposit.Amount <= 0) throw new GameEngineException("The Deposit amount is zero or negative.");
                    newDelegatedTank.enclosureDeposits.Add(enclosureDeposit);
                }
                newDelegatedTank.RecalculateAmount();
                TankDepositHasBeenAddedEvent eventMsg = new TankDepositHasBeenAddedEvent(Id);
                PlatformMonitor.GetInstance().WhenNewEvent(eventMsg);
            }

            internal void MoveDepositToTank(int tankId, Deposit deposit)
            {
                if(tankId <= 0) throw new ArgumentNullException(nameof(tankId));
                if (deposit == null) throw new ArgumentNullException(nameof(deposit));
                if (deposit == null) throw new GameEngineException("The Deposit is null.");
                if (deposit.Amount <= 0) throw new GameEngineException("The Deposit amount is zero or negative.");

                if(!ExistDeposit(deposit.Id))
                {
                    throw new GameEngineException($"The Deposit with ID {deposit.Id} is not in the tank.");
                }

                var newDelegatedTank = Delegate();

                EnclosureDeposit enclosureDeposit = newDelegatedTank.FindEnclosureDeposit(deposit.Id);
                newDelegatedTank.enclosureDeposits.Remove(enclosureDeposit);
                newDelegatedTank.RecalculateAmount();
                
                TankDepositsHaveBeenMovedEvent eventHasBeenMove = new TankDepositsHaveBeenMovedEvent(Id);
                PlatformMonitor.GetInstance().WhenNewEvent(eventHasBeenMove);

                Tank tank = Liquid.Source.FindTank(tankId);
                Tank delegatedTank = tank.Delegate() as Tank;
                delegatedTank.enclosureDeposits.Add(enclosureDeposit);
                delegatedTank.RecalculateAmount();

                TankDepositHasBeenAddedEvent eventMsg = new TankDepositHasBeenAddedEvent(Id);
                PlatformMonitor.GetInstance().WhenNewEvent(eventMsg);
            }

            internal void MoveToTanks(List<int> list)
            {
                if (list == null || !list.Any()) throw new ArgumentException("List of tank IDs cannot be null or empty.");

                var newDelegatedTank = Delegate();

                HashSet<Tank> tanksToUpdate = new HashSet<Tank>();

                foreach (var tankId in list)
                {
                    var targetTank = Source.FindTank(tankId);
                    if (targetTank == null) throw new GameEngineException($"Tank with ID {tankId} does not exist.");
                    if (targetTank.Kind != this.Kind) throw new GameEngineException($"Cannot move to a tank of different kind: {targetTank.Kind}.");
                    if (targetTank is not TankReady tankReady) throw new GameEngineException("Cannot move to a tank that is not ready.");
                    newDelegatedTank.MoveToTank(tankReady, tanksToUpdate);
                }

                tanksToUpdate.Add(newDelegatedTank);
                foreach (var tank in tanksToUpdate)
                {
                    tank.RecalculateAmount();
                    TanksHaveBeenMovedToTankEvent eventMsg = new TanksHaveBeenMovedToTankEvent(tank.Id);
                    PlatformMonitor.GetInstance().WhenNewEvent(eventMsg);
                }
            }

            private void MoveToTank(TankReady newTank, HashSet<Tank> tanksToUpdate)
            {
                if (newTank == null) throw new ArgumentNullException(nameof(newTank));
                if (newTank == this) throw new GameEngineException("Cannot move to the same tank.");

                bool inUseForAnyTanker = Source.IsTankInUse(newTank);
                if (inUseForAnyTanker) throw new GameEngineException("Cannot move to a tank that is in use by a tanker.");

                if (newTank.ContainsRecursive(this)) throw new GameEngineException("Cannot move to a tank that is a child of this tank.");

                var currentParent = newTank.Parent;
                currentParent?.tanks.Remove(newTank);

                while (currentParent != null)
                {
                    tanksToUpdate.Add(currentParent);
                    currentParent = currentParent.Parent;
                }

                newTank.Parent = this;
                tanks.Add(newTank);
            }

            internal void AddTargetDate(DateTime now, DateTime targetDate)
            {
                if (targetDate == DateTime.MinValue) throw new ArgumentNullException(nameof(targetDate));
                if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now));
                if (targetDate < DateTime.MinValue) throw new GameEngineException("Target date cannot be less than DateTime.MinValue.");
                if (targetDate < now) throw new GameEngineException("Target date cannot be in the past.");
                this.TargetDate = targetDate;

                TankHeaderInfoHasChangeEvent eventMsg = new TankHeaderInfoHasChangeEvent(Id);
                PlatformMonitor.GetInstance().WhenNewEvent(eventMsg);
            }
        }

        internal class TankRoot : Tank
        {
            internal TankRoot(int id, string name, string description, DateTime createdAt, int version, string kind, Liquid liquid, List<EnclosureDeposit> enclosureDeposits) :
                base(id, name, description, createdAt, version, kind, liquid, enclosureDeposits, new List<Tank>()) { }

            protected override TankRoot Delegate()
            {
                TankRoot newTankRoot = new TankRoot(Id, Name, Description, CreatedAt, Version + 1, Kind, Liquid, this.enclosureDeposits.ToList());
                newTankRoot.partentTankerId = partentTankerId;
                newTankRoot.tanks = tanks.ToList();
                
                newTankRoot.Parent = Parent;
                if (newTankRoot.Parent != null)
                {
                    Parent.tanks.Remove(this);
                    Parent.tanks.Add(newTankRoot);
                }

                foreach (var _tank in this.tanks)
                {
                    if (_tank.Parent == this) _tank.Parent = newTankRoot;
                }

                newTankRoot.previous = this;
                next = newTankRoot;

                Source.AddOrUpdateTank(newTankRoot);
                return newTankRoot;
            }

            internal TankRoot Add(IEnumerable<EnclosureDeposit> enclosureDepositSelected)
            {
                if (enclosureDepositSelected == null || !enclosureDepositSelected.Any()) throw new ArgumentException("Deposits cannot be null or empty.");
                if (enclosureDepositSelected.Any(d => d == null || d.Amount == 0)) throw new GameEngineException("One of the deposits is null.");

                var newDelegatedTank = Delegate();

                foreach (var deposit in enclosureDepositSelected)
                {
                    newDelegatedTank.enclosureDeposits.Add(deposit);
                }
                newDelegatedTank.RecalculateAmount();
                TankDepositHasBeenAddedEvent eventMsg = new TankDepositHasBeenAddedEvent(Id);
                PlatformMonitor.GetInstance().WhenNewEvent(eventMsg);

                return newDelegatedTank;
            }

            internal void MoveDepositToTank(int tankId, Deposit deposit)
            {
                if (tankId <= 0) throw new ArgumentNullException(nameof(tankId));
                if (deposit == null) throw new ArgumentNullException(nameof(deposit));
                if (deposit == null) throw new GameEngineException("The Deposit is null.");
                if (deposit.Amount <= 0) throw new GameEngineException("The Deposit amount is zero or negative.");

                if (!ExistDeposit(deposit.Id))
                {
                    throw new GameEngineException($"The Deposit with ID {deposit.Id} is not in the tank.");
                }

                var newDelegatedTank = Delegate();

                EnclosureDeposit enclosureDeposit = FindEnclosureDeposit(deposit.Id);
                newDelegatedTank.enclosureDeposits.Remove(enclosureDeposit);
                newDelegatedTank.RecalculateAmount();

                TankDepositsHaveBeenMovedEvent eventHasBeenMove = new TankDepositsHaveBeenMovedEvent(Id);
                PlatformMonitor.GetInstance().WhenNewEvent(eventHasBeenMove);

                Tank tank = Liquid.Source.FindTank(tankId);
                Tank delegatedTank = tank.Delegate() as Tank;
                delegatedTank.enclosureDeposits.Add(enclosureDeposit);
                delegatedTank.RecalculateAmount();

                TankDepositHasBeenAddedEvent eventMsg = new TankDepositHasBeenAddedEvent(Id);
                PlatformMonitor.GetInstance().WhenNewEvent(eventMsg);
            }
        }


        internal class TankLocked : Tank
        {
            internal TankLocked(TankReady tankReady) :
                base(tankReady.Id, tankReady.Name, tankReady.Description, tankReady.CreatedAt, tankReady.Version, tankReady.Kind, tankReady.Liquid,  tankReady.enclosureDeposits, tankReady.tanks, tankReady.ContainerColor, tankReady.TargetDate, tankReady.GoalAmount)
            {
                TankHasBeenLockedEvent tankHasBeenLockedEvent = new TankHasBeenLockedEvent(Id);
                PlatformMonitor.GetInstance().WhenNewEvent(tankHasBeenLockedEvent);
            }
            internal TankLocked(TankRoot tankReady) :
                base(tankReady.Id, tankReady.Name, tankReady.Description, tankReady.CreatedAt, tankReady.Version, tankReady.Kind, tankReady.Liquid, tankReady.enclosureDeposits, tankReady.tanks, tankReady.ContainerColor, tankReady.TargetDate, tankReady.GoalAmount)
            {
                TankHasBeenLockedEvent tankHasBeenLockedEvent = new TankHasBeenLockedEvent(Id);
                PlatformMonitor.GetInstance().WhenNewEvent(tankHasBeenLockedEvent);
            }

            private TankLocked(int id, string name, string description, DateTime createdAt, int version, string kind, Liquid liquid, List<EnclosureDeposit> enclosureDeposits, List<Tank> tanks, string color, DateTime targetDate, GoalAmount goalAmount) :
                base(id, name, description, createdAt, version, kind, liquid, enclosureDeposits, tanks, color, targetDate, goalAmount)
            {
            }

            protected override TankLocked Delegate()
            {
                TankLocked newTankLocked = new TankLocked(Id, Name, Description, CreatedAt, Version + 1, Kind, Liquid, this.enclosureDeposits.ToList(), tanks.ToList(), ContainerColor, TargetDate, GoalAmount);
                newTankLocked.partentTankerId = partentTankerId;
                newTankLocked.Parent = Parent;
                if (newTankLocked.Parent != null)
                {
                    Parent.tanks.Remove(this);
                    Parent.tanks.Add(newTankLocked);
                }

                foreach (var _tank in this.tanks)
                {
                    if (_tank.Parent == this) _tank.Parent = newTankLocked;
                }

                newTankLocked.previous = this;
                next = newTankLocked;

                Source.AddOrUpdateTank(newTankLocked);
                return newTankLocked;
            }

            internal List<EnclosureDeposit> RemoveUsedAndGetUnusedDeposits(IEnumerable<int> depositIdsUsedOnTransaction)
            {
                if (depositIdsUsedOnTransaction == null || !depositIdsUsedOnTransaction.Any()) throw new ArgumentException("Deposit IDs cannot be null or empty.");
                List<EnclosureDeposit> remainingDeposits = new List<EnclosureDeposit>();
                foreach (var enclosureDeposit in enclosureDeposits)
                {
                    if (!depositIdsUsedOnTransaction.Contains(enclosureDeposit.Id))
                    {
                        remainingDeposits.Add(enclosureDeposit);
                    }
                }

                for (int i = enclosureDeposits.Count - 1; i >= 0; i--)
                    {
                    if (depositIdsUsedOnTransaction.Contains(enclosureDeposits[i].Id))
                    {
                        enclosureDeposits.RemoveAt(i);
                    }
                }

                return remainingDeposits;
            }

            internal TankReady CreateTank(int newTankId, DateTime createdAt, List<EnclosureDeposit> remainingEnclosureDeposits)
            {
                if (remainingEnclosureDeposits == null || !remainingEnclosureDeposits.Any()) throw new ArgumentNullException(nameof(remainingEnclosureDeposits));
                if (newTankId <= 0) throw new ArgumentNullException(nameof(newTankId));
                if (this.Kind != Kind) throw new ArgumentNullException(nameof(Kind));

                TankReady tankReady = new TankReady(
                    newTankId,
                    $"{Name} - Partial From Tank ID {Id}",
                    Description,
                    createdAt,
                    Version,
                    this.Kind,
                    Liquid,
                    remainingEnclosureDeposits
                );
                Source.AddOrUpdateTank(tankReady);
                return tankReady;
            }
        }

        internal class TankDispatched : Tank
        {
            internal TankDispatched(TankLocked tankLocked) :
            base(tankLocked.Id, tankLocked.Name, tankLocked.Description, tankLocked.CreatedAt, tankLocked.Version, tankLocked.Kind, tankLocked.Liquid, tankLocked.enclosureDeposits, tankLocked.tanks, tankLocked.ContainerColor, tankLocked.TargetDate, tankLocked.GoalAmount)
            {
                TankHasBeenDispatchedEvent eventMsg = new TankHasBeenDispatchedEvent(Id);
                PlatformMonitor.GetInstance().WhenNewEvent(eventMsg);
            }

            private TankDispatched(int id, string name, string description, DateTime createdAt, int version, string kind, Liquid liquid, List<EnclosureDeposit> enclosureDeposits, List<Tank> tanks, string color, DateTime targetDate, GoalAmount goalAmount) :
                base(id, name, description, createdAt, version, kind, liquid, enclosureDeposits, tanks, color, targetDate, goalAmount)
            {
            }

            protected override TankDispatched Delegate()
            {
                TankDispatched newTankDispatched = new TankDispatched(Id, Name, Description, CreatedAt, Version + 1, Kind, Liquid, this.enclosureDeposits.ToList(), tanks.ToList(), ContainerColor, TargetDate, GoalAmount);
                newTankDispatched.partentTankerId = partentTankerId;
                newTankDispatched.Parent = Parent;
                if (newTankDispatched.Parent != null)
                {
                    Parent.tanks.Remove(this);
                    Parent.tanks.Add(newTankDispatched);
                }

                foreach (var _tank in this.tanks)
                {
                    if (_tank.Parent == this) _tank.Parent = newTankDispatched;
                }

                newTankDispatched.previous = this;
                next = newTankDispatched;

                Source.AddOrUpdateTank(newTankDispatched);
                return newTankDispatched;
            }
        }

        internal class TankArchived : Tank
        {
            private static readonly List<EnclosureDeposit> EMPTY_ENCLOSURE_DEPOSITS = new List<EnclosureDeposit>();
            private static readonly List<Tank> EMPTY_TANKS = new List<Tank>();


            internal TankArchived(Tank tank) :
                base(tank.Id, tank.Name, tank.Description, tank.CreatedAt, tank.Version, tank.Kind, tank.Liquid, tank.enclosureDeposits, tank.tanks, tank.ContainerColor, tank.TargetDate, tank.GoalAmount)
            {
                HashSet<int> jarVersions = new HashSet<int>();

                foreach (var _tank in tank.tanks)
                {
                    IEnumerable<int> enumerable = _tank.EnclousureDeposits.Select(d => d.TankVersion);
                    foreach (var version in enumerable)
                    {
                        jarVersions.Add(version);
                    }

                    TankArchived tankArchived = new TankArchived(_tank);
                    Source.AddOrUpdateTank(tankArchived);
                }

                if (jarVersions.Any())
                {
                    foreach (var version in jarVersions)
                    {
                        Liquid.Source.Jar.TryReleaseVersion(version);
                    }
                }

                TankHasBeenArchivedEvent eventMsg = new TankHasBeenArchivedEvent(Id);
                PlatformMonitor.GetInstance().WhenNewEvent(eventMsg);

                if (Integration.UseKafka)
                {
                    bool itIsThePresent = ExecutionContext.Current.ItIsThePresent;
                    using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, $"{Integration.Kafka.TopicForContainerEvents}"))
                    {
                        TankArchivedMessage tankArchivedMessage = new TankArchivedMessage(
                            tankId: Id,
                            kind: Kind,
                            name: Name,
                            description: Description,
                            version: Version,
                            parentId: Parent == null ? 0 : Parent.Id,
                            createdAt: CreatedAt,
                            depositIds: enclosureDeposits.Select(d => d.Id).ToList()
                        );
                        buffer.Send(tankArchivedMessage);
                    }
                }

                this.enclosureDeposits = null;
                this.enclosureDeposits = EMPTY_ENCLOSURE_DEPOSITS;

                this.tanks = null;
                this.tanks = EMPTY_TANKS;
            }

            private TankArchived(int id, string name, string description, DateTime createdAt, int version, string kind, Liquid liquid, List<EnclosureDeposit> enclosureDeposits, List<Tank> tanks, string color, DateTime targetDate, GoalAmount goalAmount) :
                base(id, name, description, createdAt, version, kind, liquid, enclosureDeposits, tanks, color, targetDate, goalAmount)
            {
            }

            protected override TankArchived Delegate()
            {
                TankArchived tankArchived = new TankArchived(Id, Name, Description, CreatedAt, Version + 1, Kind, Liquid, new List<EnclosureDeposit>(), new List<Tank>(), ContainerColor, TargetDate, GoalAmount);
                tankArchived.partentTankerId = partentTankerId;
                tankArchived.Parent = Parent;
                if (tankArchived.Parent != null)
                {
                    Parent.tanks.Remove(this);
                    Parent.tanks.Add(tankArchived);
                }

                foreach (var _tank in this.tanks)
                {
                    if (_tank.Parent == this) _tank.Parent = tankArchived;
                }

                tankArchived.previous = this;
                next = tankArchived;

                Source.AddOrUpdateTank(tankArchived);
                return tankArchived;
            }
        }

        internal class TankDiscarded : Tank
        {
            internal TankDiscarded(Tank tank) :
                base(tank.Id, tank.Name, tank.Description, tank.CreatedAt, tank.Version, tank.Kind, tank.Liquid, tank.enclosureDeposits, tank.tanks, tank.ContainerColor, tank.TargetDate, tank.GoalAmount)
            {
                if (tank is TankDiscarded) throw new GameEngineException("The tank has already been discarded.");

                TankHasBeenDiscardedEvent eventMsg = new TankHasBeenDiscardedEvent(Id);
                PlatformMonitor.GetInstance().WhenNewEvent(eventMsg);

                if (Integration.UseKafka)
                {
                    bool itIsThePresent = ExecutionContext.Current.ItIsThePresent;
                    using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, $"{Integration.Kafka.TopicForContainerEvents}"))
                    {
                        TankDiscardedMessage tankArchivedMessage = new TankDiscardedMessage(
                            tankId: Id,
                            kind: Kind,
                            name: Name,
                            description: Description,
                            version: Version,
                            parentId: Parent == null ? 0 : Parent.Id,
                            createdAt: CreatedAt,
                            depositIds: enclosureDeposits.Select(d => d.Id).ToList()
                        );
                        buffer.Send(tankArchivedMessage);
                    }
                }

                foreach (var _tank in tank.tanks)
                {
                    TankDiscarded discardedChildTank = new TankDiscarded(_tank);
                    Source.AddOrUpdateTank(discardedChildTank);
                }
            }

            private TankDiscarded(int id, string name, string description, DateTime createdAt, int version, string kind, Liquid liquid, List<EnclosureDeposit> enclosureDeposits, List<Tank> tanks, string color, DateTime targetDate, GoalAmount goalAmount) :
                base(id, name, description, createdAt, version, kind, liquid, enclosureDeposits, tanks, color, targetDate, goalAmount)
            {
            }

            protected override TankDiscarded Delegate()
            {
                TankDiscarded tankDiscarded = new TankDiscarded(Id, Name, Description, CreatedAt, Version + 1, Kind, Liquid, new List<EnclosureDeposit>(), new List<Tank>(), ContainerColor, TargetDate, GoalAmount);
                tankDiscarded.partentTankerId = partentTankerId;
                tankDiscarded.Parent = Parent;
                if (tankDiscarded.Parent != null)
                {
                    Parent.tanks.Remove(this);
                    Parent.tanks.Add(tankDiscarded);
                }

                foreach (var _tank in this.tanks)
                {
                    if (_tank.Parent == this) _tank.Parent = tankDiscarded;
                }

                tankDiscarded.previous = this;
                next = tankDiscarded;

                Source.AddOrUpdateTank(tankDiscarded);
                return tankDiscarded;
            }
        }
        internal enum FilterTankStatus
        {
            READY,
            LOCKED,
            DISPATCHED,
            ARCHIVED,
            DISCARDED,
            ALL
        };

        
    }
}
