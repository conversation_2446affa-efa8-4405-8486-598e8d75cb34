﻿using GamesEngine.Finance;
using GamesEngine.Games.Lotto;
using GamesEngine.Gameboards;
using GamesEngine.Gameboards.Lines;
using GamesEngine.Gameboards.Lotto;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using static GamesEngine.Finance.FragmentsCreationBody;
using static GamesEngine.Exchange.PaymentProcessorsAndActionsByDomains;
using static GamesEngine.Finance.PaymentChannels;
using System.Text;
using GamesEngine.Domains;
using town.connectors.commons;

namespace GamesEngine.Business
{
	internal abstract class Accounting : Objeto
	{
		internal abstract bool CustomerHasFoundsFor(Order order, decimal founds);

		internal abstract void Purchase(bool itIsThePresent, OrderCompleted order, DateTime now);

		internal abstract void Purchase(bool itIsThePresent, OrderCompleted order, DateTime now, IEnumerable<Ticket> tickets);

		internal abstract void GradeTicketWager(bool itIsThePresent, TicketWager wager, DateTime now, string employeeName);

		internal abstract void DeleteTickets(bool itIsThePresent, IEnumerable<Ticket> tickets, Store store, DateTime now, string employeeName);

		internal abstract void DeleteWager(bool itIsThePresent, TicketWager wager, DateTime now, string employeeName);

		internal abstract void PayPrize(bool itIsThePresent, int betId, string accountNumber, decimal prize, string description, string additionalInfo, DateTime now);

		internal abstract void Refund(bool itIsThePresent, int betId, string accountNumber, decimal amount, string description, string additionalInfo, DateTime now);

		internal abstract void ReturnPrize(bool itIsThePresent, int betId, string accountNumber, decimal amount, string description, string additionalInfo, DateTime now);

		internal abstract void RevertRefund(bool itIsThePresent, int betId, string accountNumber, decimal amount, string description, string additionalInfo, DateTime now);

		public static string ReplaceAccountNumbers(string text)
		{
			return new HideAccount(text).Hidden();
		}

		public static bool IsValidAccount(string text)
		{
			return new HideAccount(text).IsValidAccount();
		}

        protected void SendFragmentsCreation(bool itIsThePresent, Customer customer, int ticketNumber, int lowBetId, int theHighestBetId, int orderNumber, Coin currencyCode, PicksLotteryGame lotto900, decimal total, Agents agent, DateTime now, string domain)
		{
			if (( ! itIsThePresent || ! Integration.UseKafka) && ! Integration.UseKafkaForAuto) return;

			var wagers = lotto900.TakeWagersBetweenBetIds(lowBetId, theHighestBetId, false);
			var maxPurchaseDate = DateTime.MinValue;
			Fragment[] listOfWagers = new Fragment[Enumerable.Count(wagers)];

			int index = 0;
            int authorizationNumber = wagers.First().TicketNumber;
            bool isTheSameTicketNumber = true;
            foreach (var wager in wagers)
			{
				listOfWagers[index] = new Fragment()
				{
					BetDescription = wager.BetDescription,
					Risk = wager.Risk.ToString(),
					ReferenceNumber = wager.ReferenceNumber,
					ToWin = wager.ToWin.ToString(),
					Number = wager.WagerNumber.ToString()

				};
				index++;
				if (maxPurchaseDate < wager.DrawDate) maxPurchaseDate = wager.DrawDate;
                if (authorizationNumber != wager.TicketNumber) isTheSameTicketNumber = false;
            }

            if (!isTheSameTicketNumber) authorizationNumber = CommonAuthorizationNumberForMultipleAuthorizations;
            FragmentCreationKafkaMessage message = new FragmentCreationKafkaMessage(
				customer.Company.Sales.CurrentStore.Id, 
				domain,
				customer.AccountNumber,
                authorizationNumber, 
				orderNumber,
				lowBetId, 
				theHighestBetId, 
				currencyCode,
				listOfWagers,
				total,
				(int)agent,
				now,
				maxPurchaseDate.AddHours(lotto900.Company.Settings.HoursKeepingAliveTickets),
				WholePaymentProcessor.NoPaymentProcessor.ToString()
			);

			Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForFragmentsCreation, message);
		}

		public const int CommonAuthorizationNumberForMultipleAuthorizations = -1;
        protected void SendFragmentsCreation(bool itIsThePresent, Customer customer, Domain domain, IEnumerable<TicketWager> wagers, int orderNumber, Coin currencyCode, PicksLotteryGame lotto900, decimal total, Agents agent, DateTime now, string processorKey)
        {
            if ((!itIsThePresent || !Integration.UseKafka) && !Integration.UseKafkaForAuto) return;

            var maxPurchaseDate = DateTime.MinValue;
            Fragment[] listOfWagers = new Fragment[Enumerable.Count(wagers)];

            int index = 0;
			int lowBetId = int.MaxValue;
			int theHighestBetId = int.MinValue;
			int authorizationNumber = wagers.First().TicketNumber;
			bool isTheSameTicketNumber = true;
            foreach (var wager in wagers)
            {
                listOfWagers[index] = new Fragment()
                {
                    BetDescription = wager.BetDescription,
                    Risk = wager.Risk.ToString(),
                    ReferenceNumber = wager.ReferenceNumber,
                    ToWin = wager.ToWin.ToString(),
                    Number = wager.WagerNumber.ToString(),
					TicketNumber = wager.TicketNumber.ToString()
                };
                index++;

				if (wager.BetNumber < lowBetId) lowBetId = wager.BetNumber;
				if (wager.BetNumber > theHighestBetId) theHighestBetId = wager.BetNumber;
                if (maxPurchaseDate < wager.DrawDate) maxPurchaseDate = wager.DrawDate;
				if (authorizationNumber != wager.TicketNumber) isTheSameTicketNumber = false;
            }

			if (!isTheSameTicketNumber) authorizationNumber = CommonAuthorizationNumberForMultipleAuthorizations;
            FragmentCreationKafkaMessage message = new FragmentCreationKafkaMessage(
				customer.Company.Sales.CurrentStore.Id,
				domain.Url,
                customer.AccountNumber,
                authorizationNumber,
                orderNumber,
				lowBetId,
				theHighestBetId,
				currencyCode,
				listOfWagers,
				total,
				(int)agent,
				now,
				maxPurchaseDate.AddHours(lotto900.Company.Settings.HoursKeepingAliveTickets),
				processorKey
			);

            Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForFragmentsCreation, message);
        }
    }

	internal class HideAccount
	{
		private readonly string originalText = null;
		private readonly char[] resultText = null;

		private const char PREFIX_5 = '5';
		private const char PREFIX_D = 'D';
		private const char PREFIX_I = 'I';
		private const char PREFIX_C = 'C';
		private const char PREFIX_S = 'S';
		private const char PREFIX_B = 'B';
		private const char PREFIX_A = 'A';
		private const char PREFIX_G = 'G';
		private const char PREFIX_P = 'P';
		private const char PREFIX_F = 'F';
		private const char PREFIX_V = 'V';
		private const char ASTERISK = '*';
		private const int LONGITUDE_FOR_ACCOUNTS = 10;
		private const int LONGITUD_MINIMA_ACCOUNTS = 6;
		private int initialPositionOfAccount = 0;


		internal HideAccount(string textToEvaluate)
		{
			originalText = textToEvaluate;
			resultText = new char[textToEvaluate.Length];
			FindAccount(textToEvaluate);
		}

		private void FindAccount(string text)
		{
			string upperText = text.ToUpper();
			for (int i = 0; i < text.Length; i++)
			{
				if (i > text.Length - LONGITUD_MINIMA_ACCOUNTS)
				{
					resultText[i] = text[i];
					continue;
				}
				switch (upperText[i])
				{
					case PREFIX_5:
						if (upperText[i + 1] == PREFIX_D && IsAccountWithTwoPrefixes(i))
						{
							i = SubstituteAccount(2, i);
						}
						else
						{
							resultText[i] = text[i];
						}
						break;
					case PREFIX_B:
						if (upperText[i + 1] == PREFIX_A && IsAccountWithTwoPrefixes(i))
						{
							i = SubstituteAccount(2, i);
						}
						else
						{
							resultText[i] = text[i];
						}
						break;
					case PREFIX_C:
						if (upperText[i + 1] == PREFIX_B && IsAccountWithTwoPrefixes(i))
						{
							i = SubstituteAccount(2, i);
						}
						else
						{
							resultText[i] = text[i];
						}
						break;
					case PREFIX_A:
						if (upperText[i + 1] == PREFIX_F && IsAccountWithTwoPrefixes(i))
						{
							i = SubstituteAccount(2, i);
						}
						else if (IsAccountWithOnePrefix(i))
						{
							i = SubstituteAccount(1, i);
						}
						else
						{
							resultText[i] = text[i];
						}
						break;
					case PREFIX_I:
						if (upperText[i + 1] == PREFIX_C && IsAccountWithTwoPrefixes(i))
						{
							i = SubstituteAccount(2, i);
						}
						else
						{
							resultText[i] = text[i];
						}
						break;
					case PREFIX_S:
						if (upperText[i + 1] == PREFIX_S && IsAccountWithTwoPrefixes(i))
						{
							i = SubstituteAccount(2, i);
						}
						else if (upperText[i + 1] == PREFIX_B && IsAccountWithTwoPrefixes(i))
						{
							i = SubstituteAccount(2, i);
						}
						else if (upperText[i + 1] == PREFIX_A && IsAccountWithTwoPrefixes(i))
						{
							i = SubstituteAccount(2, i);
						}
						else
						{
							resultText[i] = text[i];
						}
						break;
					case PREFIX_V:
						if (upperText[i + 1] == PREFIX_C && IsAccountWithTwoPrefixes(i))
						{
							i = SubstituteAccount(2, i);
						}
						else if (upperText[i + 1] == PREFIX_B && IsAccountWithTwoPrefixes(i))
						{
							i = SubstituteAccount(2, i);
						}
						else if (IsAccountWithOnePrefix(i))
						{
							i = SubstituteAccount(1, i);
						}
						else
						{
							resultText[i] = text[i];
						}
						break;
					case PREFIX_G:
						if (upperText[i + 1] == PREFIX_P && IsAccountWithTwoPrefixes(i))
						{
							i = SubstituteAccount(2, i);
						}
						else
						{
							resultText[i] = text[i];
						}
						break;
					default:
						if (resultText[i] != ASTERISK)
						{
							resultText[i] = text[i];
						}
						break;
				}
			}
		}

		private bool IsAccountWithTwoPrefixes(int currentPosition)
		{
			bool isAccount = true;
			int initialPosition = currentPosition + 2;
			int finalPosition = initialPosition + LONGITUD_MINIMA_ACCOUNTS - 2;

			if (currentPosition + LONGITUD_MINIMA_ACCOUNTS > originalText.Length) return false;

			for (int i = initialPosition; isAccount && i < finalPosition; i++)
			{
				isAccount = char.IsDigit(originalText[i]);
			}

			return isAccount;
		}

		private bool IsAccountWithOnePrefix(int currentPosition)
		{
			bool isAccount = true;
			int initialPosition = currentPosition + 1;
			int finalPosition = initialPosition + LONGITUD_MINIMA_ACCOUNTS - 1;

			if (currentPosition + LONGITUD_MINIMA_ACCOUNTS > originalText.Length) return false;

			for (int i = initialPosition; isAccount && i < finalPosition; i++)
			{
				isAccount = char.IsDigit(originalText[i]);
			}

			return isAccount;
		}

		internal int SubstituteAccount(int amountOfLetterAtBeginning, int position)
		{
			int endIteration = Math.Min(position + LONGITUD_MINIMA_ACCOUNTS, originalText.Length);

			initialPositionOfAccount = position;

			for (; position < endIteration; position++)
			{
				resultText[position] = '*';
			}

			endIteration = Math.Min(position + LONGITUDE_FOR_ACCOUNTS, originalText.Length);
			for (; position < endIteration; position++)
			{
				if (!char.IsDigit(originalText[position])) break;
				resultText[position] = '*';
			}
			return position - 1;
		}

		internal string Hidden()
		{
			return new String(resultText);
		}

		internal bool IsValidAccount()
		{
			for (int index = 0; index < resultText.Length; index++)
			{
				if (resultText[index] == originalText[index]) { return false; }
			}
			return true;
		}
	}
}
