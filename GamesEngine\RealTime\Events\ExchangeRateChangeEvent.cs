﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamesEngine.RealTime.Events
{
    internal class ExchangeRateChangeEvent : PlatformEvent
    {
        public string Kind { get; set; }
        public decimal Rate { get; set; }

        public ExchangeRateChangeEvent(DateTime timeStamp, string kind, decimal rate) : base(timeStamp, PlatformEventType.EXCHANGE_RATE_CHANGE)
        {
            if (string.IsNullOrWhiteSpace(kind)) throw new ArgumentNullException(nameof(kind));
            if (rate <= 0) throw new ArgumentOutOfRangeException(nameof(rate), "Rate must be greater than zero.");
            Kind = kind;
            Rate = rate;
        }
    }
}
