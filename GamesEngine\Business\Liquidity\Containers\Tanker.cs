﻿using GamesEngine.Business.Liquidity.Transactions;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using static GamesEngine.Business.Liquidity.Containers.Tank;

namespace GamesEngine.Business.Liquidity.Containers
{
    internal abstract class Tanker : Container
    {
        internal enum TankerStatus
        {
            PENDING,
            SEALED,
            DISPATCHED,
            ARCHIVED,
            ALL
        };

        internal Tanker(int id, string kind, Source source, string name, string description, DateTime createdAt, int version) : base(id, kind,source.Liquid, name, description, createdAt)
        {
            if (source == null) throw new ArgumentNullException(nameof(source));
            if (source.Jar == null) throw new ArgumentNullException(nameof(source.Jar));
            this.version = version;
            this.Source = source;
        }

        internal Tanker(int id, string kind, Source source, string name, string description, DateTime createdAt, int version, string color, DateTime targetDate, GoalAmount goalAmount) : this(id, kind, source, name, description, createdAt, version)
        {
            SetColor(color);
            TargetDate = targetDate;
            GoalAmount = goalAmount;
        }

        internal Source Source { get; private set; }

        internal void UpdateName(bool itIsThePresent, DateTime now, string name)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentException("Name cannot be null or whitespace.", nameof(name));
            this.Name = name;
        }

        internal void UpdateDescription(bool itIsThePresent, DateTime now, string description)
        {
            this.Description = description ?? string.Empty;
        }

        internal DateTime TargetDate { get; private set; } = DateTime.MinValue;

        internal IEnumerable<Deposit> ExplandedDeposits
        {
            get
            {
                List<Deposit> resultDeposits = new List<Deposit>();
                foreach (var tank in Tanks)
                {
                    foreach (var deposit in tank.ExplandedDeposits)
                    {
                        resultDeposits.Add(deposit);
                    }
                }
                return resultDeposits;
            }
        }

        internal abstract IEnumerable<Tank> Tanks { get; }

        internal int TotalTanks => Tanks.Count();
        internal int TotalDeposits => ExplandedDeposits.Count();

        internal TankerArchived Archive()
        {
            var result = new TankerArchived(this);
            Source.AddOrUpdateTanker(result);
            return result;
        }

        static DateTime noStartDateSentinel = new DateTime(1951, 1, 1);
        static DateTime noEndDateSentinel = new DateTime(2049, 12, 31);
       

        internal TankerSummary BuildMonthlySummary(DateTime startDate, DateTime endDate, string name = null,FilterContainerType status = FilterContainerType.ALL,    string color = null)
        {
            IEnumerable<Tank> filteredTanks = Enumerable.Empty<Tank>();
            var rootTank = this.Tanks.OfType<TankRoot>().FirstOrDefault();

            
            if (status == FilterContainerType.CONTAINER || status == FilterContainerType.ALL || status.ToString().Equals("POCKETS", StringComparison.OrdinalIgnoreCase))
            {
                filteredTanks = this.Tanks.Where(t => !(t is TankRoot));
                if (startDate != noStartDateSentinel)
                    filteredTanks = filteredTanks.Where(t => t.CreatedAt >= startDate);
                if (endDate != noEndDateSentinel)
                    filteredTanks = filteredTanks.Where(t => t.CreatedAt <= endDate.AddDays(1));
                if (!string.IsNullOrWhiteSpace(name))
                    filteredTanks = filteredTanks.Where(t => t.Name.IndexOf(name, StringComparison.OrdinalIgnoreCase) >= 0);
                if (!string.IsNullOrWhiteSpace(color))
                    filteredTanks = filteredTanks.Where(t => t.ContainerColor.Equals(color, StringComparison.OrdinalIgnoreCase));
            }
            var totalTank = filteredTanks.Count();

            
            var depositGroups = new Dictionary<string, List<Deposit>>();
            if ((rootTank != null) && (status == FilterContainerType.TRANSACTION || status == FilterContainerType.ALL || status.ToString().Equals("TRANSACTIONS", StringComparison.OrdinalIgnoreCase)))
            {
                var deposits = rootTank.ExplandedDeposits.AsEnumerable();
                if (startDate != noStartDateSentinel)
                    deposits = deposits.Where(d => d.CreatedAt >= startDate);
                if (endDate != noEndDateSentinel)
                    deposits = deposits.Where(d => d.CreatedAt <= endDate.AddDays(1));
                if (!string.IsNullOrWhiteSpace(name))
                    deposits = deposits.Where(d => d.InvoiceId != null && d.InvoiceId.IndexOf(name, StringComparison.OrdinalIgnoreCase) >= 0);
                if (!string.IsNullOrWhiteSpace(color))
                    deposits = deposits.Where(d => d.ConfirmedCurrency != null && d.ConfirmedCurrency.Equals(color, StringComparison.OrdinalIgnoreCase));
                depositGroups = deposits
                    .GroupBy(dep => dep.CreatedAt.ToString("MM/yyyy"))
                    .ToDictionary(g => g.Key, g => g.ToList());
            }

            
            var tankGroups = filteredTanks
                .GroupBy(tank => tank.CreatedAt.ToString("MM/yyyy"))
                .ToDictionary(g => g.Key, g => g.ToList());

            
            var allMonthKeys = tankGroups.Keys.Union(depositGroups.Keys).OrderBy(k => k).ToList();
            var monthlySummaries = new List<MonthlyTankSummary>();

            foreach (var key in allMonthKeys)
            {
                var tanksInMonth = (status == FilterContainerType.TRANSACTION || status.ToString().Equals("TRANSACTIONS", StringComparison.OrdinalIgnoreCase))
                    ? new List<Tank>()
                    : (tankGroups.ContainsKey(key) ? tankGroups[key] : new List<Tank>());

                var depositsInMonth = (status == FilterContainerType.CONTAINER || status.ToString().Equals("POCKETS", StringComparison.OrdinalIgnoreCase))
                    ? new List<Deposit>()
                    : (depositGroups.ContainsKey(key) ? depositGroups[key] : new List<Deposit>());

                var tankDetails = tanksInMonth.Select(t => new TankDetail(t.Id, t.Name, t.Type, t.Amount, t.ReceivedAmount, t.ContainerColor, t.CreatedAt)).ToList();
                var depositDetails = depositsInMonth.Select(d => new DepositDetail(d.Id, d.InvoiceId, d.Amount, d.ConfirmedAmount, d.CreatedAt)).ToList();

                var monthTotal = tankDetails.Sum(t => t.ReceivedAmount) + depositDetails.Sum(d => d.ReceivedAmount);

                monthlySummaries.Add(new MonthlyTankSummary(key, monthTotal, tankDetails, depositDetails));
            }

            return new TankerSummary(this.Amount, this.ReceivedAmount, monthlySummaries, this.TotalDeposits, totalTank);
        }

        protected override DateTime CalculateMaxDate()
        {
            DateTime maxDate = DateTime.MinValue;
            foreach (var tank in Tanks)
            {
                if (tank.MaxDate > maxDate)
                {
                    maxDate = tank.MaxDate;
                }
            }
            return maxDate;
        }

        protected override DateTime CalculateMinDate()
        {
            DateTime minDate = DateTime.MaxValue;
            foreach (var tank in Tanks)
            {
                if (tank.MinDate < minDate)
                {
                    minDate = tank.MinDate;
                }
            }
            return minDate;
        }

        internal bool BuildDepositsBetween(DateTime from, DateTime to)
        {
            if (from == DateTime.MinValue && to == DateTime.MinValue) throw new ArgumentNullException(nameof(from), "Both 'from' and 'to' dates are DateTime.MinValue.");
            if (from > to) throw new GameEngineException("The 'from' date cannot be greater than the 'to' date.");

            foreach (var tank in Tanks)
            {
                if (tank.BuildDepositsBetween(from, to))
                    return true;
            }
            return false;
        }

        internal bool BuildDepositsFrom(DateTime startDate)
        {
            if (startDate == DateTime.MinValue) throw new ArgumentNullException(nameof(startDate));
            return BuildDepositsBetween(startDate, DateTime.MaxValue);
        }

        internal bool BuildDepositsUpTo(DateTime endDate)
        {
            if (endDate == DateTime.MinValue) throw new ArgumentNullException(nameof(endDate));
            return BuildDepositsBetween(DateTime.MinValue, endDate);
        }

        public class DepositDetail : Objeto
        {
            public int Id { get; }
            public string InvoiceId { get; }
            public decimal Amount { get; }
            public decimal ReceivedAmount { get; }
            public string CreatedAt { get; }

            public DepositDetail(int id, string invoiceId, decimal amount, decimal receivedAmount, DateTime createdAt)
            {
                Id = id;
                InvoiceId = invoiceId;
                Amount = amount;
                ReceivedAmount = receivedAmount;
                CreatedAt = createdAt.ToString("MM/dd/yyyy HH:mm:ss");
            }
        }

        public class TankDetail : Objeto
        {
            public int Id { get; }
            public string Name { get; }
            public string Type { get; }
            public decimal Amount { get; }
            public decimal ReceivedAmount { get; }
            public string CreatedAt { get; }
            public string ContainerColor { get; }

            public TankDetail(int id, string name, string type, decimal amount, decimal receivedAmount,string color, DateTime createdAt)
            {
                Id = id;
                Name = name;
                Type = type;
                Amount = amount;
                ReceivedAmount = receivedAmount;
                ContainerColor = color;
                CreatedAt = createdAt.ToString("MM/dd/yyyy HH:mm:ss");
            }
        }

        public class MonthlyTankSummary : Objeto
        {
            public string Month { get; }
            public decimal MonthTotal { get; }
            public IEnumerable<TankDetail> Tanks { get; }
            public IEnumerable<DepositDetail> RootDeposits { get; }

            public MonthlyTankSummary(string month, decimal monthTotal, IEnumerable<TankDetail> tanks, IEnumerable<DepositDetail> rootDeposits)
            {
                Month = month;
                MonthTotal = monthTotal;
                Tanks = tanks ?? new List<TankDetail>();
                RootDeposits = rootDeposits ?? new List<DepositDetail>();
            }
        }

        public class TankerSummary : Objeto
        {
            public decimal TankerTotalAmount { get; }
            public decimal TotalReceivedAmount { get; }
            public decimal TotalDeposits { get; }
            public decimal TotalTanks{ get; }
            public IEnumerable<MonthlyTankSummary> MonthlySummaries { get; }

            public TankerSummary(decimal tankerTotal,decimal totalReceivedAmount, IEnumerable<MonthlyTankSummary> monthlySummaries,int totalDeposits, int totalTanks)
            {
                TankerTotalAmount = tankerTotal;
                TotalReceivedAmount = totalReceivedAmount;
                MonthlySummaries = monthlySummaries;
                TotalDeposits = totalDeposits;
                TotalTanks = totalTanks;
            }
        }

        internal class TankerPending : Tanker
        {
            private readonly List<Tank> tanksReady= new List<Tank>();
            private TankRoot rootTank;
            private const string DEFAULT_ROOT_NAME = "Root Tank";
            private const string DEFAULT_ROOT_DESCRIPTION = "Root tank";

            internal TankerPending(int tankerId, string name, string description, DateTime createdAt, int version, string kind, Source source, IEnumerable<TankReady> tanksReady) : base(tankerId, kind, source, name, description, createdAt, version)
            {
                rootTank = new TankRoot(source.NextTankId(), DEFAULT_ROOT_NAME, DEFAULT_ROOT_DESCRIPTION, createdAt, version: 1, kind,source.Liquid, new List<EnclosureDeposit>());
                source.AddOrUpdateTank(rootTank);
                this.tanksReady.Add(rootTank);
                foreach (var tank in tanksReady)
                {
                    tank.SetParentTanker(tankerId);
                    this.tanksReady.Add(tank);
                }

                RecalculateAmount();
            }

            internal TankerPending(int tankerId, string name, string description, DateTime createdAt, int version, string kind, Source source) : base(tankerId, kind, source, name, description, createdAt, version)
            {
                rootTank = new TankRoot(source.NextTankId(), DEFAULT_ROOT_NAME, DEFAULT_ROOT_DESCRIPTION, createdAt, version: 1, kind,source.Liquid, new List<EnclosureDeposit>());
                source.AddOrUpdateTank(rootTank);
                this.tanksReady.Add(rootTank);
            }

            internal TankerPending(int tankerId, string name, string description, DateTime createdAt, int version, string kind, Source source,List<EnclosureDeposit> enclosureDeposits) : base(tankerId, kind, source, name, description, createdAt, version)
            {
                if(enclosureDeposits == null) throw new ArgumentNullException(nameof(enclosureDeposits));
                if (!enclosureDeposits.Any()) throw new ArgumentException("The enclosureDeposits list is empty.", nameof(enclosureDeposits));
                rootTank = new TankRoot(source.NextTankId(), DEFAULT_ROOT_NAME, DEFAULT_ROOT_DESCRIPTION, createdAt, version: 1, kind, source.Liquid, enclosureDeposits);
                source.AddOrUpdateTank(rootTank);
                this.tanksReady.Add(rootTank);
                RecalculateAmount();
            }

            private TankerPending(int tankerId, string name, string description, DateTime createdAt, int version, string kind, Source source, TankRoot rootTank, List<Tank> tanksReady) : base(tankerId, kind, source, name, description, createdAt, version)
            {
                this.rootTank = rootTank;
                this.tanksReady = tanksReady;
                
                RecalculateAmount();
            }

            protected override TankerPending Delegate()
            {
                TankerPending tankerPending = new TankerPending(Id, Name, Description, CreatedAt, Version + 1, Kind, Source, rootTank, tanksReady.ToList());

                tankerPending.previous = this;
                next = tankerPending;

                Source.AddOrUpdateTanker(tankerPending);
                return tankerPending;

            }

            internal override IEnumerable<Tank> Tanks =>  tanksReady;

            internal void Add(List<TankReady> tanksReady)
            {
                if(tanksReady == null) throw new GameEngineException("The TankReady list is null.");
                if (!tanksReady.Any()) throw new GameEngineException("The TankReady list is empty.");

                var newDelegatedTanker = Delegate();

                foreach (var tankReady in tanksReady)
                {
                    if (tankReady == null) throw new GameEngineException("The TankReady is null.");
                    if (this.tanksReady.Contains(tankReady)) throw new GameEngineException("The TankReady already exists in the TankerPending.");
                }

                for (int i = 0; i < tanksReady.Count; i++)
                {
                    tanksReady[i].SetParentTanker(this.Id);
                    newDelegatedTanker.tanksReady.Add(tanksReady[i]);
                }

                newDelegatedTanker.RecalculateAmount();
                TankHaveBeenMovedToTankerEvent eventMsm = new TankHaveBeenMovedToTankerEvent(Id);
                PlatformMonitor.GetInstance().WhenNewEvent(eventMsm);
            }

            internal void Add(List<EnclosureDeposit> enclosuredepositsSelected)
            {
                if(enclosuredepositsSelected == null) throw new GameEngineException("The Deposit list is null.");
                if (!enclosuredepositsSelected.Any()) throw new GameEngineException("The Deposit list is empty.");
                if (enclosuredepositsSelected.Any(d => d == null)) throw new GameEngineException("The Deposit list contains null values.");

                var newDelegatedTanker = Delegate();

                var newRoot = newDelegatedTanker.rootTank.Add(enclosuredepositsSelected);
                newDelegatedTanker.tanksReady.Remove(newDelegatedTanker.rootTank);
                newDelegatedTanker.rootTank = newRoot;
                newDelegatedTanker.tanksReady.Add(newDelegatedTanker.rootTank);
                newDelegatedTanker.RecalculateAmount();
                TankerDepositHasBeenAddedEvent eventMsg = new TankerDepositHasBeenAddedEvent(Id);
                PlatformMonitor.GetInstance().WhenNewEvent(eventMsg);
            }

            internal void AddDepositsByIds(IEnumerable<int> depositIds)
            {
                if (depositIds == null || !depositIds.Any()) return;

                var depositsToAdd = new List<EnclosureDeposit>();
                foreach (var id in depositIds)
                {
                    var deposit = this.Source.Jar.FindDepositById(id);
                    if (deposit == null) throw new GameEngineException($"Deposit with ID {id} not found in the current Jar.");
                    var enclosureDeposit = this.Source.Jar.FindEnclosureDeposit(id);
                    depositsToAdd.Add(enclosureDeposit);
                }

                this.Add(depositsToAdd);
            }

            internal void AddTargetDate(DateTime now, DateTime targetDate)
            {
                if (targetDate == DateTime.MinValue) throw new ArgumentNullException(nameof(targetDate));
                if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now));
                if (targetDate < DateTime.MinValue) throw new GameEngineException("Target date cannot be less than DateTime.MinValue.");
                if (targetDate < now) throw new GameEngineException("Target date cannot be in the past.");
                this.TargetDate = targetDate;

                TankerHeaderInfoHasChangeEvent msg = new TankerHeaderInfoHasChangeEvent(Id);
                PlatformMonitor.GetInstance().WhenNewEvent(msg);
            }

            internal TankerSealed Sealed()
            {
                if (tanksReady == null || !tanksReady.Any()) throw new GameEngineException("The TankerPending has no tanks ready.");

                var result = new TankerSealed(this);
                Source.AddOrUpdateTanker(result);

                return result;
            }
        }

        internal class TankerSealed : Tanker
        {
            private TankerPending tankerPending;
            private readonly List<TankLocked> lockedTanks = new();

            internal TankerSealed(TankerPending tankerPending) : base(tankerPending.Id, tankerPending.Kind, tankerPending.Source, tankerPending.Name, tankerPending.Description, tankerPending.CreatedAt, tankerPending.Version, tankerPending.ContainerColor, tankerPending.TargetDate, tankerPending.GoalAmount)
            {
                this.tankerPending = tankerPending;
                SetColor(tankerPending.ContainerColor);
                var tanks = tankerPending.Tanks;
                foreach (var tank in tanks)
                {
                    TankLocked tankLocked ;
                    if(tank is TankRoot tankRoot)
                    {
                        tankLocked = new TankLocked(tankRoot);
                    }
                    else if(tank is TankReady tankReady)
                    {
                        tankLocked = new TankLocked(tankReady);
                    }
                    else
                    {
                        throw new GameEngineException($"The Tank type {tank.GetType().Name} is not supported in TankerSealed.");
                    }

                    lockedTanks.Add(tankLocked);
                    Source.AddOrUpdateTank(tankLocked);
                }
                RecalculateAmount();
                TankerSealedEvent tankerSealedEvent = new TankerSealedEvent(Id);
                PlatformMonitor.GetInstance().WhenNewEvent(tankerSealedEvent);
            }

            private TankerSealed(int id, string name, string description, DateTime createdAt, int version, string kind, Source source, string color, DateTime targetDate, GoalAmount goalAmount, List<TankLocked> lockedTanks) : base(id, kind, source, name, description, createdAt, version, color, targetDate, goalAmount)
            {
                this.lockedTanks = lockedTanks;
                RecalculateAmount();
            }

            protected override TankerSealed Delegate()
            {
                TankerSealed sealedTanker = new TankerSealed(Id, Name, Description, CreatedAt, Version + 1, Kind, Source, ContainerColor, TargetDate, GoalAmount, lockedTanks.ToList());
                
                sealedTanker.previous = this;
                next = sealedTanker;

                Source.AddOrUpdateTanker(sealedTanker);
                return sealedTanker;
            }

            internal override IEnumerable<TankLocked> Tanks => lockedTanks;

            internal IEnumerable<Deposit> Deposits
            {
                get
                {
                    List<Deposit> resultDeposits = new List<Deposit>();
                    foreach (var tank in Tanks)
                    {
                        foreach (var deposit in tank.ExplandedDeposits)
                        {
                            resultDeposits.Add(deposit);
                        }
                    }
                    return resultDeposits;
                }
            }

            internal TankerSealed PartialDispatched(int partialTankerId, DateTime dispachedDate, IEnumerable<int> dispatchDeposits)
            {
                if (dispachedDate == DateTime.MinValue) throw new ArgumentNullException(nameof(dispachedDate));
                if (dispatchDeposits == null || !dispatchDeposits.Any()) throw new ArgumentNullException(nameof(dispatchDeposits));

                Dictionary<TankLocked, (List<EnclosureDeposit> RemainingDeposits, bool StillHasDeposits) > tankDepositsMap = new();
                foreach (var tank in Tanks)
                {
                    List<EnclosureDeposit> remainingDeposits = tank.RemoveUsedAndGetUnusedDeposits(dispatchDeposits);
                    if (!remainingDeposits.Any()) continue;

                    bool tankStillHasDeposits = tank.ExplandedDeposits.Any();
                    tankDepositsMap.Add(tank, (remainingDeposits, tankStillHasDeposits));
                }

                List<TankReady> tankReadies = new List<TankReady>();
                foreach (var tankMap in tankDepositsMap)
                {
                    var loackedTank = tankMap.Key;
                    var remainingDeposits = tankMap.Value.RemainingDeposits;
                    bool stillHasDeposits = tankMap.Value.StillHasDeposits;
                    if (!stillHasDeposits)
                    {
                        lockedTanks.Remove(loackedTank);
                        TankDiscarded tankDiscarded = new TankDiscarded(loackedTank);
                        Source.AddOrUpdateTank(tankDiscarded);
                    }

                    int newTankId = Source.NextTankId();
                    var newTankReady = loackedTank.CreateTank(newTankId, dispachedDate, remainingDeposits);
                    Source.AddOrUpdateTank(newTankReady);
                    tankReadies.Add(newTankReady);
                }
                if (!tankReadies.Any()) throw new GameEngineException("No tanks where found with the provided deposits to dispatch.");

                TankerPending partialPending = new TankerPending(
                    partialTankerId,
                    $"{this.tankerPending.Name} - Partial From Tanker ID {this.Id}",
                    this.tankerPending.Description,
                    dispachedDate,
                    1,
                    this.tankerPending.Kind,
                    this.Source,
                    tankReadies
                );
                TankerSealed partialTanker = new TankerSealed(partialPending);
                Source.AddOrUpdateTanker(partialTanker);

                var dispatchCurrentTanker = new TankerDispatched(this, dispachedDate);
                Source.AddOrUpdateTanker(dispatchCurrentTanker);

                TankerPartialDispatchedEvent tankerPartialDispatchedEvent = new TankerPartialDispatchedEvent(Id);
                PlatformMonitor.GetInstance().WhenNewEvent(tankerPartialDispatchedEvent);
                return partialTanker;
            }

            internal TankerDispatched Dispatched(DateTime dispachedDate)
            {
                if (dispachedDate == DateTime.MinValue) throw new ArgumentNullException(nameof(dispachedDate));
                var result = new TankerDispatched(this, dispachedDate);
                Source.AddOrUpdateTanker(result);
            
                return result;
            }

            internal void Disburden(bool itIsThePresent)
            {
                Source.Liquid.IngressSentinel.AwaitForTanker(itIsThePresent, this);

                DisburdenTankerEvent disburdenTankerEvent = new DisburdenTankerEvent(Id);
                PlatformMonitor.GetInstance().WhenNewEvent(disburdenTankerEvent);
            }

            internal void Undisburden()
            {
                Source.Liquid.IngressSentinel.UndisburdenTanker(this);

                UndisburdenTankerEvent undisburdenTankerEvent = new UndisburdenTankerEvent(Id);
                PlatformMonitor.GetInstance().WhenNewEvent(undisburdenTankerEvent);
            }            
        }

        internal class TankerDispatched : Tanker
        {
            private TankerSealed tankerSealed;
            private readonly List<TankDispatched> dispachedTanks = new();

            internal DateTime DispatchedDate { get; private set; }

            internal TankerDispatched(TankerSealed tankerSealed, DateTime dispatchedDate) : base(tankerSealed.Id, tankerSealed.Kind, tankerSealed.Source, tankerSealed.Name, tankerSealed.Description, tankerSealed.CreatedAt, tankerSealed.Version, tankerSealed.ContainerColor, tankerSealed.TargetDate, tankerSealed.GoalAmount)
            {
                if (tankerSealed == null) throw new ArgumentNullException(nameof(tankerSealed));
                if (dispatchedDate == DateTime.MinValue) throw new ArgumentNullException(nameof(dispatchedDate));
                this.tankerSealed = tankerSealed;

                DispatchedDate = dispatchedDate;

                var lockedTanks = tankerSealed.Tanks;
                foreach (var tankLocked in lockedTanks)
                {
                    var tankDispatched = new TankDispatched(tankLocked);
                    Source.AddOrUpdateTank(tankDispatched);
                    dispachedTanks.Append(tankDispatched);
                }

                TankerDispatchedEvent tankerDispatchedEvent= new TankerDispatchedEvent(tankerSealed.Id);
                PlatformMonitor.GetInstance().WhenNewEvent(tankerDispatchedEvent);
            }

            private TankerDispatched(int id, string name, string description, DateTime createdAt, int version, string kind, Source source, string color, DateTime targetDate, GoalAmount goalAmount, List<TankDispatched> dispachedTanks) : base(id, kind, source, name, description, createdAt, version, color, targetDate, goalAmount)
            {
                this.dispachedTanks = dispachedTanks;
                RecalculateAmount();
            }

            protected override TankerDispatched Delegate()
            {
                TankerDispatched tankerDispatched = new TankerDispatched(Id, Name, Description, CreatedAt, Version + 1, Kind, Source, ContainerColor, TargetDate, GoalAmount, dispachedTanks.ToList());
                
                tankerDispatched.previous = this;
                next = tankerDispatched;

                Source.AddOrUpdateTanker(tankerDispatched);
                return tankerDispatched;
            }

            internal override IEnumerable<TankDispatched> Tanks => dispachedTanks;

            internal class DispatchTankerMessage : LiquidityEventMessage
            {
                internal int TankerId { get; private set; }
                internal string Kind { get; private set; }

                internal DispatchTankerMessage(int tankerId, string kind) : base(LiquidityMessageType.TankerDispatched)
                {
                    if (tankerId <= 0) throw new ArgumentNullException(nameof(tankerId));
                    if (string.IsNullOrWhiteSpace(kind)) throw new ArgumentNullException(nameof(kind));
                    TankerId = tankerId;
                    Kind = kind;
                }

                internal DispatchTankerMessage(string message) : base(message)
                {
                }

                protected override void Deserialize(string[] serializedMessage, out int fieldOrder)
                {
                    base.Deserialize(serializedMessage, out fieldOrder);
                    TankerId = int.Parse(serializedMessage[fieldOrder++]);
                    Kind = serializedMessage[fieldOrder++];
                }

                protected override void InternalSerialize()
                {
                    base.InternalSerialize();
                    AddProperty(TankerId).
                    AddProperty(Kind);
                }
            }
        }

        internal class TankerArchived : Tanker
        {
            internal TankerArchived(Tanker tanker) : base(tanker.Id, tanker.Kind, tanker.Source, tanker.Name, tanker.Description, tanker.CreatedAt, tanker.Version, tanker.ContainerColor, tanker.TargetDate, tanker.GoalAmount)
            {
                foreach (var tank in tanker.Tanks)
                {
                    var tankArchived = new TankArchived(tank);
                    Source.AddOrUpdateTank(tankArchived);
                }

                TankerArchivedEvent tankerArchivedEvent = new TankerArchivedEvent(tanker.Id);
                PlatformMonitor.GetInstance().WhenNewEvent(tankerArchivedEvent);

                if (Integration.UseKafka)
                {
                    bool itIsThePresent = ExecutionContext.Current.ItIsThePresent;
                    using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, $"{Integration.Kafka.TopicForContainerEvents}"))
                    {
                        TankerArchivedMessage tankArchivedMessage = new TankerArchivedMessage(
                            tanker.Id,
                            tanker.Kind,
                            tanker.Name,
                            tanker.Description,
                            tanker.CreatedAt,
                            tanker.Version,
                            tanker.Tanks.Select(t => t.Id)
                        );
                        buffer.Send(tankArchivedMessage);
                    }
                }
            }

            private TankerArchived(int id, string name, string description, DateTime createdAt, int version, string kind, Source source, string color, DateTime targetDate, GoalAmount goalAmount) : base(id, kind, source, name, description, createdAt, version, color, targetDate, goalAmount)
            {
                RecalculateAmount();
            }

            protected override TankerArchived Delegate()
            {
                TankerArchived tankerArchived = new TankerArchived(Id, Name, Description, CreatedAt, Version + 1, Kind, Source, ContainerColor, TargetDate, GoalAmount);
                
                tankerArchived.previous = this;
                next = tankerArchived;

                Source.AddOrUpdateTanker(tankerArchived);
                return tankerArchived;
            }

            internal override IEnumerable<TankArchived> Tanks => Enumerable.Empty<TankArchived>();

        }
    }
}
