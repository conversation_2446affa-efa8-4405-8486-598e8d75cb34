﻿using GamesEngine.Domains;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Text;
using town.connectors.drivers;
using static GamesEngine.Business.WholePaymentProcessor;

namespace GamesEngine.Custodian.Operations
{
	[Puppet]
	internal sealed class CompleteDeposit : Deposit
	{
		internal DisbursementExecution LastExecution { get;}
		internal CompleteDeposit(Guardian guardian, DateTime creationDate, int transactionId, int authorizationId, string description, Currency amount, int group, PaymentProcessor processor, Domain domain, 
			string identificationDocumentNumber, string accountNumber)
			: base(guardian,
				  creationDate,
				  transactionId,
				  authorizationId,
				  description,
				  amount,
				  group,
				  processor,
				  domain,
				  StatusCodes.COMPLETE,
				  identificationDocumentNumber,
				  accountNumber)
		{
		}

		internal override InternalOperationUpdateMessage GenerateMessage(DateTime now)
		{
			return new InternalOperationUpdateMessage(
				Abbreviation,
				CreationDate,
				now,
				TransactionId,
				ProcessorAccount.Id,
				AuthorizationId,
				Description,
				Amount,
				Group,
				Processor.Driver.Id,
				0,
				0,
				false,
				Domain.Url,
				new Approvers(),
				Status,
				true,
				100,
				Identifier,
                AccountNumber,
				string.Empty
                );
		}
	}
}
