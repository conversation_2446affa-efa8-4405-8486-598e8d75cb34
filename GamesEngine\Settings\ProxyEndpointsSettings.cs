﻿using Elastic.Clients.Elasticsearch.Snapshot;
using GamesEngine.Custodian;
using GamesEngine.RealTime.RestAPI;
using log4net.Plugin;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using ZstdSharp.Unsafe;

namespace GamesEngine.Settings
{
    public class ProxyEndpointsSettings
    {
        private enum StatusRoute
        {
            DISABLED = 0,
            ENABLED = 1,
        }
        private const string X_API_KEY_HEADER = "x-api-key";
        private const string DOMAIN_URL = "domain-url";
        public static string ApiUrl { get; private set; }
        public static string AccessKey { get; private set; }

        private static HttpClient client;


        public static void Configure(IConfiguration configuration)
        {
            var customSettings = configuration.GetSection("ApiSix");
            if (customSettings == null) throw new GameEngineException("ApiSix configuration is missing in the appsettings.");

            var apiUrl = customSettings.GetValue<string>("ApiUrl");
            if (string.IsNullOrEmpty(apiUrl)) throw new GameEngineException("ApiSix.ApiUrl is not configured or is empty.");
            if (!Uri.IsWellFormedUriString(apiUrl, UriKind.Absolute)) throw new GameEngineException($"ApiSix.ApiUrl '{ApiUrl}' is not a valid absolute URI.");
            ApiUrl = apiUrl;

            var accessKey = customSettings.GetValue<string>("AccessKey");
            if (string.IsNullOrEmpty(accessKey)) throw new GameEngineException("ApiSix.AccessKey is not configured or is empty.");
            AccessKey = accessKey;

            // Initialize HttpClient
            client = new HttpClient();
            client.BaseAddress = new Uri(ApiUrl);

        }

        public static async Task AddProxyEndpointAsync(string name, string endPointPath, string endpointBaseUrl)
        {
            if (string.IsNullOrEmpty(name)) throw new GameEngineException("Proxy endpoint name cannot be null or empty.");
            if (string.IsNullOrEmpty(endPointPath)) throw new GameEngineException("Proxy endpoint cannot be null or empty.");
            if (string.IsNullOrEmpty(endpointBaseUrl)) throw new GameEngineException("Base URL cannot be null or empty.");

            var route = new
            {
                name = name,
                desc = name,
                methods = new[] { "POST" },
                uri = endPointPath,
                plugins = new
                {
                    key_auth = new { },
                    real_ip = new { source = "X-Forwarded-For" },
                    consumer_restriction = new { whitelist = new[] { "Client2" } },
                    keycloak_token = new
                    {
                        client_id = "exchange",
                        user_name = "ccajeroqa8",
                        password = "123456",
                        token_url = $"http://*************:8081/auth/realms/Clerks/protocol/openid-connect/token"
                    }
                },
                upstream_id = "1"
            };

            var jsonContent = System.Text.Json.JsonSerializer.Serialize(route);
            var content = new StringContent(jsonContent, System.Text.Encoding.UTF8, "application/json");
            content.Headers.Add(X_API_KEY_HEADER, AccessKey);

            var response = await client.PostAsync("/apisix/admin/routes", content);
        }
        
       
        public static async Task UpdateWhiteListRouteAsync(string routeName, List<string> whiteList, string endpoint)
        {
            if (string.IsNullOrEmpty(routeName)) throw new GameEngineException("Route name cannot be null or empty.");
            if (string.IsNullOrEmpty(endpoint)) throw new GameEngineException("Endpoint cannot be null or empty.");
            if (whiteList == null) throw new GameEngineException("White list cannot be null or empty.");

            var route = new
            {
                name = routeName,
                desc = "",
                uri = endpoint,
                status = whiteList.Count == 0 ? StatusRoute.DISABLED : StatusRoute.ENABLED,
                methods = new[] { "POST" },
                plugins = new Dictionary<string, object>
                {
                   
                },
            };

            if (route.status == StatusRoute.ENABLED )
            {
                route.plugins.Add("consumer-restriction", new Dictionary<string, object>
                {
                    { "whitelist", whiteList.ToArray() },
                    { "type", "consumer_name" },
                    { "rejected_code", 403 }
                });
            }
            else if (route.status == StatusRoute.DISABLED)
            {
                route.plugins.Add("consumer-restriction", new Dictionary<string, object>
                {
                    { "whitelist",  new List<string>{"disabled_route_dedault"} },
                    { "type", "consumer_name" },
                    { "rejected_code", 403 }
                });
            }


            var request = new HttpRequestMessage(HttpMethod.Patch, $"{ApiUrl}/apisix/admin/routes/{routeName}");
            request.Headers.Add(X_API_KEY_HEADER, AccessKey);
            var jsonContent = System.Text.Json.JsonSerializer.Serialize(route);
            var content = new StringContent(jsonContent, System.Text.Encoding.UTF8, "application/json");
            request.Content = content;

            var response = await client.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                var errorMessage = await response.Content.ReadAsStringAsync();
                throw new GameEngineException($"Failed to update route: {errorMessage} (Status Code: {response.StatusCode})");
            }
        }

        internal static async Task AddConsumerAsync(string routeName, string endPoint, List<string> subscribers, string organization, string secretKey, string description)
        {
            if (string.IsNullOrEmpty(secretKey)) throw new GameEngineException("Secret key cannot be null or empty.");
            if (string.IsNullOrEmpty(organization)) throw new GameEngineException("Organization cannot be null or empty.");
            if (string.IsNullOrEmpty(description)) throw new GameEngineException("Description cannot be null or empty.");

            var consumer = new
            {
                username = organization,
                desc = description,
                plugins = new Dictionary<string, Dictionary<string, string>>
                   {
                       {
                           "key-auth", new Dictionary<string, string>
                           {
                               { "key", secretKey }
                           }
                       }
                   }

            };

            var request = new HttpRequestMessage(HttpMethod.Put, $"{ApiUrl}/apisix/admin/consumers");
            request.Headers.Add(X_API_KEY_HEADER, AccessKey);
            var jsonContent = System.Text.Json.JsonSerializer.Serialize(consumer);
            var content = new StringContent(jsonContent, System.Text.Encoding.UTF8, "application/json");
            request.Content = content;

            var response = await client.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                var errorMessage = await response.Content.ReadAsStringAsync();
                throw new GameEngineException($"Failed to add consumer: {errorMessage} (Status Code: {response.StatusCode})");
            }
           
            await UpdateWhiteListRouteAsync(routeName, subscribers, endPoint);
        }

        internal static async Task RemoveConsumerAsync(string routeName, string endPoint, List<string> subscribers, string organization)
        {
            if (string.IsNullOrEmpty(organization)) throw new GameEngineException("Organization cannot be null or empty.");
            if (string.IsNullOrEmpty(routeName)) throw new GameEngineException("Route name cannot be null or empty.");
            if (string.IsNullOrEmpty(endPoint)) throw new GameEngineException("Endpoint cannot be null or empty.");
            if (subscribers == null) throw new GameEngineException("Subscribers list cannot be null or empty.");


            var request = new HttpRequestMessage(HttpMethod.Delete, $"{ApiUrl}/apisix/admin/consumers/{organization}");
            request.Headers.Add(X_API_KEY_HEADER, AccessKey);
            var response = await client.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                var errorMessage = await response.Content.ReadAsStringAsync();
                throw new GameEngineException($"Failed to add consumer: {errorMessage} (Status Code: {response.StatusCode})");
            }

            await UpdateWhiteListRouteAsync(routeName, subscribers, endPoint);
        }


        public static async Task<string> GetConsumerAsync(string organization)
        {
            if (string.IsNullOrEmpty(organization)) throw new GameEngineException("Organization cannot be null or empty.");

            var request = new HttpRequestMessage(HttpMethod.Get, $"{ApiUrl}/apisix/admin/consumers/{organization}");
            request.Headers.Add(X_API_KEY_HEADER, AccessKey);

            var response = await client.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                var errorMessage = await response.Content.ReadAsStringAsync();
                throw new GameEngineException($"Failed to add consumer: {errorMessage} (Status Code: {response.StatusCode})");
            }
            return await response.Content.ReadAsStringAsync();
        }

        public static async Task RefreshSecretAsync(string organization, string secretKey)
        {
            if (string.IsNullOrEmpty(secretKey)) throw new GameEngineException("Secret key cannot be null or empty.");
            if (string.IsNullOrEmpty(organization)) throw new GameEngineException("Organization cannot be null or empty.");

            var consumer = new
            {
                username = organization,
                plugins = new Dictionary<string, Dictionary<string, string>>
                   {
                       {
                           "key-auth", new Dictionary<string, string>
                           {
                               { "key", secretKey }
                           }
                       }
                   }

            };

            var request = new HttpRequestMessage(HttpMethod.Put, $"{ApiUrl}/apisix/admin/consumers/{organization}");
            request.Headers.Add(X_API_KEY_HEADER, AccessKey);
            var jsonContent = System.Text.Json.JsonSerializer.Serialize(consumer);
            var content = new StringContent(jsonContent, System.Text.Encoding.UTF8, "application/json");
            request.Content = content;

            var response = await client.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                var errorMessage = await response.Content.ReadAsStringAsync();
                throw new GameEngineException($"Failed to add consumer: {errorMessage} (Status Code: {response.StatusCode})");
            }
        }

        public static async Task<string> PostAsync(string endpoint, object data, Dictionary<string, string> headers = null)
        {
            Loggers.GetIntance().Apisix.Debug($"{nameof(PostAsync)}: Request to Apisix url:{endpoint}");
            if (string.IsNullOrEmpty(endpoint)) throw new GameEngineException("Endpoint cannot be null or empty.");

            Loggers.GetIntance().Apisix.Debug($"{nameof(PostAsync)}: Request to Apisix url:{endpoint} Body:{(data != null ? LogObjectToJson(data) : "null")}");

            HttpContent content = null;
            if (data != null)
            {
                var jsonContent = System.Text.Json.JsonSerializer.Serialize(data);
                content = new StringContent(jsonContent, System.Text.Encoding.UTF8, "application/json");
            }
            else
            {
                content = new StringContent(string.Empty, System.Text.Encoding.UTF8, "application/json");
            }

            content.Headers.Add(X_API_KEY_HEADER, AccessKey);

            if (headers != null && headers.Count > 0)
            {
                foreach (var header in headers)
                {
                    content.Headers.Add(header.Key, header.Value);
                }
            }

            var response = await client.PostAsync(endpoint, content);
            var contentString = await response.Content.ReadAsStringAsync();
            if (!response.IsSuccessStatusCode)
            {
                var errorMessage = contentString;
                Loggers.GetIntance().Apisix.Debug($"{nameof(PostAsync)}: Request to Apisix url:{endpoint} statusCode: {response.StatusCode} content:{errorMessage}");

                throw new GameEngineException($"Failed to post data: {errorMessage} (Status Code: {response.StatusCode})");
            }
            Loggers.GetIntance().Apisix.Debug($"{nameof(PostAsync)}: Request to Apisix url:{endpoint} statusCode: {response.StatusCode} response:{contentString}");
            return contentString;
        }


        public static async Task<string> PutAsync(string endpoint, object data, Dictionary<string, string> headers = null)
        {
            Loggers.GetIntance().Apisix.Debug($"{nameof(PutAsync)}: Request to Apisix url:{endpoint}");
            if (string.IsNullOrEmpty(endpoint)) throw new GameEngineException("Endpoint cannot be null or empty.");

            Loggers.GetIntance().Apisix.Debug($"{nameof(PutAsync)}: Request to Apisix url:{endpoint} Body:{(data != null ? LogObjectToJson(data) : "null")}");

            HttpContent content = null;
            if (data != null)
            {
                var jsonContent = System.Text.Json.JsonSerializer.Serialize(data);
                content = new StringContent(jsonContent, System.Text.Encoding.UTF8, "application/json");
            }
            else
            {
                content = new StringContent(string.Empty, System.Text.Encoding.UTF8, "application/json");
            }

            content.Headers.Add(X_API_KEY_HEADER, AccessKey);

            if (headers != null && headers.Count > 0)
            {
                foreach (var header in headers)
                {
                    content.Headers.Add(header.Key, header.Value);
                }
            }

            var response = await client.PutAsync(endpoint, content);
            var contentString = await response.Content.ReadAsStringAsync();
            if (!response.IsSuccessStatusCode)
            {
                var errorMessage = contentString;
                Loggers.GetIntance().Apisix.Debug($"{nameof(PutAsync)}: Request to Apisix url:{endpoint} statusCode: {response.StatusCode} content:{errorMessage}");

                throw new GameEngineException($"Failed to post data: {errorMessage} (Status Code: {response.StatusCode})");
            }
            Loggers.GetIntance().Apisix.Debug($"{nameof(PutAsync)}: Request to Apisix url:{endpoint} statusCode: {response.StatusCode} response:{contentString}");
            return contentString;
        }


        public static async Task<string> GetAsync(string endpoint)
        {
            Loggers.GetIntance().Apisix.Debug($"{nameof(GetAsync)}: Request to Apisix url:{endpoint}");

            if (string.IsNullOrEmpty(endpoint)) throw new GameEngineException("Endpoint cannot be null or empty.");
            var request = new HttpRequestMessage(HttpMethod.Get, endpoint);
            request.Headers.Add(X_API_KEY_HEADER, AccessKey);
            var response = await client.SendAsync(request);
            var contentString = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                var errorMessage = await response.Content.ReadAsStringAsync();
                Loggers.GetIntance().Apisix.Debug($"{nameof(GetAsync)}: Request to Apisix url:{endpoint} statusCode: {response.StatusCode} content:{errorMessage}");

                throw new GameEngineException($"Failed to get data: {errorMessage} (Status Code: {response.StatusCode})");
            }
            var objectResponse = await response.Content.ReadAsStringAsync();
            Loggers.GetIntance().Apisix.Debug($"{nameof(GetAsync)}: Request to Apisix url:{endpoint} statusCode: {response.StatusCode} response:{objectResponse}");

            return contentString;
        }

        private static string LogObjectToJson(object obj)
        {
            string serializedData = "";
            try
            {
                serializedData = JsonConvert.SerializeObject(obj, Formatting.Indented);
            }
            catch (Exception ex)
            {
                //log.Error("Error serializando objeto", ex);
            }
            return serializedData;
        }
    }

}
