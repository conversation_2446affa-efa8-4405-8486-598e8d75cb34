﻿using System;

namespace GamesEngine.RealTime.Events
{
    internal abstract class TankChangeEvent : ContainerChangeEvent
    {
        internal TankChangeEvent(int tankId, PlatformEventType eventType) : base(tankId, eventType)
        {
            if (tankId <= 0) throw new ArgumentNullException(nameof(tankId));
        }
    }

    internal class TankChangeColorEvent : TankChangeEvent
    {
        internal TankChangeColorEvent(int tankId) : base(tankId, PlatformEventType.TANK_COLOR_HAS_CHANGE)
        {
        }
    }

    internal class TankDepositHasBeenAddedEvent : TankChangeEvent
    {
        internal TankDepositHasBeenAddedEvent(int tankId) : base(tankId, PlatformEventType.DEPOSITS_ADDED_TO_TANK)
        {
        }
    }

    internal class TankHasBeenDiscardedEvent : TankChangeEvent
    {
        internal TankHasBeenDiscardedEvent(int tankId) : base(tankId, PlatformEventType.TANK_HAS_BEEN_DISCARDED)
        {
            if (tankId <= 0) throw new ArgumentNullException(nameof(tankId));
        }
    }

    internal class TanksHaveBeenMovedToTankEvent : TankChangeEvent
    {
        internal TanksHaveBeenMovedToTankEvent(int tankId) : base(tankId, PlatformEventType.TANKS_HAVE_BEEN_MOVE_TO_TANK)
        {
            if (tankId <= 0) throw new ArgumentNullException(nameof(tankId));
        }
    }

    internal class  TankHeaderInfoHasChangeEvent : TankChangeEvent
    {
        internal TankHeaderInfoHasChangeEvent(int tankId) : base(tankId, PlatformEventType.TANK_HEADER_INFO_HAS_CHANGE)
        {
        }
    }

    internal class TankHasBeenMergeEvent : TankChangeEvent
    {
        internal TankHasBeenMergeEvent(int targetTankId) : base(targetTankId, PlatformEventType.TANK_HAS_BEEN_MERGE)
        {
        }
    }

    internal class TankDepositsHaveBeenMovedEvent : TankChangeEvent
    {
        public TankDepositsHaveBeenMovedEvent(int tankId) : base(tankId, PlatformEventType.TANK_DEPOSITS_HAVE_BEEN_MOVED)
        {
        }
    }

    internal class TankHasBeenLockedEvent : TankChangeEvent
    {
        public TankHasBeenLockedEvent(int tankId) : base(tankId, PlatformEventType.TANK_HAS_BEEN_LOCKED)
        {
        }
    }

    internal class DispenserNoteHasBeenChangeEvent : TankChangeEvent
    {
        public DispenserNoteHasBeenChangeEvent(int tankId) : base(tankId, PlatformEventType.DISPENSER_NOTE_HAS_BEEN_CHANGE)
        {
        }
    }

    internal class TankHasBeenDispatchedEvent : TankChangeEvent
    {
        public TankHasBeenDispatchedEvent(int tankId) : base(tankId, PlatformEventType.TANK_HAS_BEEN_DISPACHED)
        {
        }
    }

    internal class TankHasBeenArchivedEvent : TankChangeEvent
    {
        public TankHasBeenArchivedEvent(int tankId) : base(tankId, PlatformEventType.TANK_HAS_BEEN_ARCHIVED)
        {
        }
    }

    internal class TankCreatedEvent : TankChangeEvent
    {
        internal TankCreatedEvent(int tankId) : base(tankId, PlatformEventType.TANK_CREATED)
        {
        }
    }

    internal class CreatedTankEvent : TankChangeEvent
    {
        internal CreatedTankEvent(int tankId) : base(tankId, PlatformEventType.CREATED_TANK)
        {
        }
    }
}
