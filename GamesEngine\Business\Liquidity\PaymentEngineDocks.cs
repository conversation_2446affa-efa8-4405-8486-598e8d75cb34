﻿using GamesEngine.Domains;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GamesEngine.Business.Liquidity
{
    internal class PaymentEngineDocks
    {
        private readonly Dictionary<Domain, PaymentEngineDock> paymentDocks;

        internal PaymentEngineDocks()
        {
            paymentDocks = new Dictionary<Domain, PaymentEngineDock>();
        }

        internal PaymentEngineDock this[Domain domain]
        {
            get
            {
                if (domain == null) throw new ArgumentNullException(nameof(domain));
                if (paymentDocks.TryGetValue(domain, out var dock))
                {
                    return dock;
                }
                throw new GameEngineException($"No payment engine dock found for domain {domain}.");
            }
        }

        internal PaymentEngineDock AddPaymentDock(Domain domain, string dock)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (string.IsNullOrWhiteSpace(dock)) throw new ArgumentNullException(nameof(dock));

            if (!paymentDocks.TryGetValue(domain, out var paymentDock))
            {
                paymentDock = new PaymentEngineDock(dock);
                paymentDocks[domain] = paymentDock;
            }
            return paymentDock;
        }

        internal bool ExistPaymentEngineDock(Domain domain)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            return paymentDocks.ContainsKey(domain);
        }

        internal PaymentEngineDock FindPaymentEngineDock(Domain domain)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (paymentDocks.TryGetValue(domain, out var dock))
            {
                return dock;
            }
            throw new GameEngineException($"No payment engine dock found for domain {domain}.");
        }

        internal class PaymentEngineDock : Objeto
        {
            internal string Dock { get; private set; }

            internal PaymentEngineDock(string dock)
            {
                if (string.IsNullOrWhiteSpace(dock)) throw new ArgumentNullException(nameof(dock));
                Dock = dock;
            }

            internal void SetPaymentDock(string paymentDockId)
            {
                if (string.IsNullOrWhiteSpace(paymentDockId)) throw new ArgumentNullException(nameof(paymentDockId));
                Dock = paymentDockId;
            }
        }
    }

}
