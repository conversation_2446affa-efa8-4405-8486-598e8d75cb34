﻿using GamesEngine.Business.Liquidity;
using GamesEngine.Domains;
using GamesEngine.Settings;
using GamesEngineTests.Unit_Tests.Games.Lotto;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using static GamesEngine.Finance.PaymentChannels;

namespace GamesEngineTests.Unit_Tests.Business
{
    [TestClass]
    public class SentinelTest//Server; Start9(Nuts) - Branch de BitcoinCore.
    {
        [TestMethod]
        public void TestIngressSentinel()
        {
            // hacer algunos que si se confirmen y otros que no.
            CurrenciesTest.AddCurrencies();
            LiquidFlow.ClearInstance();

            bool itIsThePresent = false;
            DateTime now = DateTime.Now;

            Liquid liquid = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "BTC");
            Assert.IsTrue(liquid.IngressSentinel.Deposits.Count() == 0);

            liquid.Source.AddXpub(new Xpub("dasfsdfdsfsd"));

            int depositId = liquid.Source.NextDepositId();
            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);
            var pendingDeposit = liquid.Source.CreateDraftDeposit(itIsThePresent, now, depositId, "invoice123", 1, 44, "tb1q4...", 100M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(liquid.Source.Amount == 100);

            Assert.IsTrue(liquid.Source.Jar.FindEnclosureDeposit(depositId).IsPending);

            liquid.Source.ConfirmDeposit(itIsThePresent, now, pendingDeposit);
            Assert.IsTrue(liquid.Source.Amount == 100);

            Assert.IsTrue(liquid.Source.Jar.FindEnclosureDeposit(depositId).IsConfirmed);
        }

        public static bool AssertUntilCondition(Func<bool> verificationFunc, TimeSpan? timeout = null)
        {
            var effectiveTimeout = timeout ?? TimeSpan.FromSeconds(10);
            var start = DateTime.UtcNow;
            while (DateTime.UtcNow - start < effectiveTimeout)
            {
                if (verificationFunc.Invoke()) return true;
            }
            return false;
        }

        [TestMethod]
        public void TestIngressSentinelInboundTask()
        {
            PaymentManager.NodoUrl = "http://10.10.0.30:31832";
            // hacer algunos que si se confirmen y otros que no.
            CurrenciesTest.AddCurrencies();
            LiquidFlow.ClearInstance();

            bool itIsThePresent = true;
            DateTime now = DateTime.Now;

            Liquid liquid = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "BTC");
            Assert.IsTrue(liquid.IngressSentinel.Deposits.Count() == 0);

            liquid.Source.AddXpub(new Xpub("dasfsdfdsfsd"));

            int depositId = liquid.Source.NextDepositId();
            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);

            // FROMT EXCHANGE TOWN1
            //var pendingDeposit = liquid.Source.CreateDraftDeposit(itIsThePresent, now, depositId, "invoice123", 1, 44, "tb1q4...", 100M, 100000M, "USD", 100, 3, 1, domain);
            var pendingDeposit = liquid.Source.CreateDraftDeposit(itIsThePresent, now, depositId, "invoice123", 1, 44, "tb1q4...", 100M, 100000M, "USD", 100, 3, 1, domain);

            // Evento de confirmacion de deposito desde Motor de pagos
            liquid.IngressSentinel.InvoiceReceivedPayment(itIsThePresent, now, pendingDeposit.InvoiceId, 6M, 6M, 100000M, "6052e84bd7d08d3764787418b5d24a17a5320c8661c958d086f051deb2cf8170", "ALSYBxGCpcEozJ6oTXc64HJGedenXtWb54krX5aKeWag");

            liquid.Source.ConfirmDeposit(itIsThePresent, now, pendingDeposit);

            bool waitTilExists = AssertUntilCondition(() =>
            {
                return liquid.IngressSentinel.InboundTasks.Count() == 0;
            }, TimeSpan.FromSeconds(10000));
            Assert.IsTrue(waitTilExists, "The Ingress Sentinel should have processed the inbound task.");
        }
    }
}
