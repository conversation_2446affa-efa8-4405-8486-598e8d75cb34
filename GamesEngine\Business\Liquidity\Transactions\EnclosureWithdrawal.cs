﻿using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using System;

namespace GamesEngine.Business.Liquidity.Transactions
{
    internal class EnclosureWithdrawal : Enclosure
    {
        internal EnclosureWithdrawal(Withdrawal withdrawal, int dispenserVersion) : base(withdrawal, dispenserVersion)
        {
        }

        internal int DispenserVersion => ContainerVersion;

        internal int Id => Transaction.Id;

        internal DateTime CreatedAt => Withdrawal.CreatedAt;

        internal Withdrawal Withdrawal => (Withdrawal)Transaction;

        internal string Destination => Withdrawal.Destination;

        internal int ClaimedVersion { get; private set; } = -1;

        internal DateTime ClaimedDate { get; private set; }
        internal DateTime CanceledDated { get; private set; }

        internal bool IsClaimed => ClaimedDate != DateTime.MinValue && CanceledDated == DateTime.MinValue;
        internal bool IsCanceled => CanceledDated != DateTime.MinValue;

        internal string SetNotes(string notes)
        {
            if (string.IsNullOrWhiteSpace(notes)) throw new ArgumentNullException(nameof(notes));

            AddNote(notes);

            DispenserNoteHasBeenChangeEvent eventMsg = new DispenserNoteHasBeenChangeEvent(Id);
            PlatformMonitor.GetInstance().WhenNewEvent(eventMsg);

            return notes;
        }

        internal void Claim(DateTime claimedDate, int claimedInVersion)
        {
            if (claimedDate == DateTime.MinValue) throw new ArgumentNullException(nameof(claimedDate));
            if (claimedInVersion < 0) throw new ArgumentNullException(nameof(claimedInVersion));
            if (IsCanceled) throw new GameEngineException("Withdrawal is already canceled.");
            if (IsClaimed) throw new GameEngineException("Withdrawal is already claimed.");
            ClaimedDate = claimedDate;
            ClaimedVersion = claimedInVersion;
            WithdrawalClaimedEvent withdrawalClaimedEvent = new WithdrawalClaimedEvent(Id);
            PlatformMonitor.GetInstance().WhenNewEvent(withdrawalClaimedEvent);
        }

        internal void Cancel(DateTime canceledDate)
        {
            if (canceledDate == DateTime.MinValue) throw new ArgumentNullException(nameof(canceledDate));
            if (IsCanceled) throw new GameEngineException("Withdrawal is already canceled.");
            if (IsClaimed) throw new GameEngineException("Withdrawal is already claimed.");
            CanceledDated = canceledDate;
            WithdrawalCanceledEvent withdrawalCanceledEvent = new WithdrawalCanceledEvent(Id);
            PlatformMonitor.GetInstance().WhenNewEvent(withdrawalCanceledEvent);
        }
    }
}
