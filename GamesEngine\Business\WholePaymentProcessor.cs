﻿using GamesEngine.Custodian.Operations;
using GamesEngine.Domains;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using GamesEngine.Logs;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Json;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.drivers;
using town.connectors.drivers.hades;
using town.connectors.exception;
using static GamesEngine.Business.CustomSettingsCollection;
using static town.connectors.CustomSettings;
using static town.connectors.CustomSettings.CustomSetting;
using Path = System.IO.Path;
using ProcessorPaymentMethod = GamesEngine.Business.WholePaymentMethods.ProcessorPaymentMethod;
[assembly: InternalsVisibleTo("ExchangeAPI")]
[assembly: InternalsVisibleTo("LiquidityAPI")]
[assembly: InternalsVisibleTo("GamesEngineMocks")]
namespace GamesEngine.Business
{
    internal class WholePaymentProcessor : WholeCatalog
    {
        internal const int NoPaymentProcessor = 0;
        private Indexer mainIndexerProcessors;
        private IndexedProcessors indexedValidateProcessor;
        private IndexedProcessors indexedFragmentProcessor;
        private IndexedProcessors otherProcessors;
        private IndexedProcessors indexedGradeProcessor;
        public MultipleProcessors Values { get; } = new MultipleProcessors();

        private PaymentProcessorsAndActionsByDomains _paymentProcessorsAndActionsByDomains;
        internal PaymentProcessorsAndActionsByDomains PaymentProcessorsAndActionsByDomains { get { return _paymentProcessorsAndActionsByDomains; } }

        private static WholePaymentProcessor _wholePaymentProcessor;

        internal static void ResetInstance()
        {
            _wholePaymentProcessor = null;
        }
        internal static WholePaymentProcessor Instance()
        {
            if (_wholePaymentProcessor == null) throw new GameEngineException("Set the processors first.");
            return _wholePaymentProcessor;
        }

        internal WholePaymentProcessor(Company company) : base(company)
        {
            mainIndexerProcessors = new Indexer();
            if (_wholePaymentProcessor != null) return;
            _wholePaymentProcessor = this;

            _paymentProcessorsAndActionsByDomains = new PaymentProcessorsAndActionsByDomains(this);
        }
        internal Company Company { get { return company; } }

        private List<Driver> Drivers { get; set; }

        internal CatalogMember GetOrCreatePaymentProcessor(Entity entity, ProcessorPaymentMethod paymentMethod, string iso4217Code, ProcessorTransaction transactionType, Driver implementation)
        {
            if (entity == null) throw new ArgumentException(nameof(entity));
            if (paymentMethod == null) throw new ArgumentException(nameof(paymentMethod));
            if (string.IsNullOrEmpty(iso4217Code)) throw new ArgumentException(nameof(iso4217Code));
            if (transactionType == null) throw new ArgumentException(nameof(transactionType));
            if (implementation == null) throw new ArgumentException(nameof(implementation));

            TransactionType fixedTransactionType;
            if (!Enum.TryParse(transactionType.Name.Replace(" ", "_"), out fixedTransactionType)) throw new GameEngineException($"TransactionType {transactionType.Name} is not a valid TransactionType");

            PaymentMethod fixedPaymentMethod;
            if (!Enum.TryParse(paymentMethod.Name, out fixedPaymentMethod)) throw new GameEngineException($"PaymentMethod {paymentMethod.Name} is not a valid PaymentMethod");

            ProcessorCoin coin = company.System.Coins.SearchByIsoCode(iso4217Code);
            var processor = mainIndexerProcessors.GetOrCreatePaymentProcessor(fixedTransactionType, coin.Coin, entity, fixedPaymentMethod, implementation);

            processor.Visible = true;
            processor.Enabled = true;

            return Add(processor);
        }

        internal CatalogMember Add(PaymentProcessor processor)
        {
            if (members.Contains(processor)) throw new GameEngineException($"The processor {processor.Name} is already added.");

            members.Add(processor);
            Values.Addon(processor);

            processor.Visible = true;
            processor.Enabled = true;

            CreateIndex(processor);

            return processor;
        }

        private void CreateIndex(PaymentProcessor processor)
        {
            if (IsAValidateDriver(processor.Driver))
            {
                if (indexedValidateProcessor.IsACollectionNeeded())
                    indexedValidateProcessor = indexedValidateProcessor.ChangeToMultipleIndexProcessor();

                indexedValidateProcessor.Addon(processor);
            }
            else if (IsAFragmentDriver(processor.Driver))
            {
                if (indexedFragmentProcessor.IsACollectionNeeded())
                    indexedFragmentProcessor = indexedFragmentProcessor.ChangeToMultipleIndexProcessor();

                indexedFragmentProcessor.Addon(processor);
            }
            else if (IsAnOtherDriver(processor.Driver))
            {
                if (otherProcessors.IsACollectionNeeded())
                    otherProcessors = otherProcessors.ChangeToMultipleIndexProcessor();

                otherProcessors.Addon(processor);
            }
            else if (IsAGradeDriver(processor.Driver))
            {
                if (indexedGradeProcessor.IsACollectionNeeded())
                    indexedGradeProcessor = indexedGradeProcessor.ChangeToMultipleIndexProcessor();

                indexedGradeProcessor.Addon(processor);
            }
        }

        internal override CatalogMember Add(int id, string name)
        {
            throw new NotImplementedException();
        }

        internal void SearchProcessor(int entityId, int paymentMethodId, int transactionTypeId, string iso4217Code, out PaymentProcessor paymentProcessor)
        {
            if (entityId <= 0) throw new GameEngineException($"{nameof(entityId)} must be greater than 0");
            if (paymentMethodId <= 0) throw new GameEngineException($"{nameof(paymentMethodId)} must be greater than 0");
            if (transactionTypeId <= 0) throw new GameEngineException($"{nameof(transactionTypeId)} must be greater than 0");
            if (string.IsNullOrEmpty(iso4217Code)) throw new ArgumentException(nameof(iso4217Code));

            paymentProcessor = null;
            foreach (PaymentProcessor processor in members)
            {
                if (processor.Entity.Id == entityId && processor.Group.Id == paymentMethodId && processor.Transactions.Any(transactionTypeId) && processor.Coin.Iso4217Code == iso4217Code && processor.Driver is ProcessorDriver)
                {
                    paymentProcessor = processor;
                    break;
                }
            }
        }

        internal IEnumerable<PaymentProcessor> SearchProcessors(int entityId, int paymentMethodId, int transactionTypeId, int coinId)
        {
            if (entityId <= 0) throw new GameEngineException($"{nameof(entityId)} must be greater than 0");
            if (paymentMethodId <= 0) throw new GameEngineException($"{nameof(paymentMethodId)} must be greater than 0");
            if (transactionTypeId <= 0) throw new GameEngineException($"{nameof(transactionTypeId)} must be greater than 0");
            if (coinId < 0) throw new GameEngineException($"{nameof(coinId)} must be greater or equal than 0");

            var transactionMember = (ProcessorTransaction)company.System.TransactionTypes.Find(transactionTypeId);
            if (!Enum.TryParse(transactionMember.Name, out TransactionType fixedTransactionType)) throw new GameEngineException($"TransactionType {transactionMember.Name} is not a valid TransactionType");

            var coinMember = (ProcessorCoin)company.System.Coins.Find(coinId);

            var paymentMember = (ProcessorPaymentMethod)company.System.PaymentMethods.Find(paymentMethodId);
            if (!Enum.TryParse(paymentMember.Name, out PaymentMethod fixedPaymentMethod)) throw new GameEngineException($"PaymentMethod {paymentMember.Name} is not a valid PaymentMethod");

            var processors = mainIndexerProcessors.GetPaymentProcessors(fixedTransactionType, coinMember.Coin.Iso4217Code, entityId, fixedPaymentMethod);
            if (processors == null || !processors.Any()) throw new GameEngineException($"No processors with {nameof(entityId)} '{entityId}' {nameof(paymentMethodId)} '{paymentMethodId}' {nameof(transactionTypeId)} '{transactionTypeId}' {nameof(coinId)} '{coinId}'");
            return processors.ToList();
        }

        internal IEnumerable<PaymentProcessor> SearchProcessorsWithDistinctKey()
        {
            var processors = new Dictionary<string, PaymentProcessor>();
            foreach (var processor in Values)
            {
                var processorFound = processors.GetValueOrDefault(processor.ProcessorKey);
                if (processorFound == null) processors.Add(processor.ProcessorKey, processor);
                else
                {
                    if (processor.Driver.Version > processorFound.Driver.Version)
                    {
                        processors[processor.ProcessorKey] = processor;
                    }
                }
            }
            return processors.Values.ToList();
        }

        internal PaymentProcessor SearchValidateProcesorBy(string iso4217Code)
        {
            //var processor = mainIndexerProcessors.AllProcessors.FirstOrDefault(x => x.Driver.TransactionTypeEnum == TransactionType.RetrieveInfo && x.Coin.Iso4217Code == iso4217Code && x.Driver.GetType() == typeof(town.connectors.drivers.fiero.Validate));
            //if (processor == null) throw new GameEngineException($"No processor found for type {t}");

            return indexedValidateProcessor.SearchBy(iso4217Code);
        }

        internal PaymentProcessor SearchGradeProcessorBy()
        {
            return indexedGradeProcessor.FirstOne();
        }

        internal PaymentProcessor SearchGradeProcessorBy(Type type)
        {
            return indexedGradeProcessor.SearchBy(type);
        }

        internal PaymentProcessor SearchBy(TransactionType transactionType, string iso4217Code)
        {
            return Values.SearchByTransactionType(transactionType, iso4217Code);
        }

        internal PaymentProcessor SearchBy(TransactionType transactionType, string iso4217Code, Type processorDriverType)
        {
            return Values.SearchBySpecificClass(transactionType, iso4217Code, processorDriverType);
        }

        internal PaymentProcessor SearchBy(Tenant_Actions processorDriverType)
        {
            return Values.SearchBySpecificClass(processorDriverType);
        }

        internal PaymentProcessor SearchFragmentProcessor()
        {
            return indexedFragmentProcessor.SearchBy(Tenant_Actions.Fragment);
        }

        internal IEnumerable<PaymentProcessor> SearchFragmentProcessors()
        {
            return indexedFragmentProcessor.SearchProcessorsBy(Tenant_Actions.Fragment);
        }

        internal PaymentProcessor SearchOtherProcessorBy(Type processorDriverType)
        {
            return otherProcessors.SearchBy(processorDriverType);
        }

        internal IEnumerable<CatalogMember> Entities()
        {
            var result = new HashSet<CatalogMember>();
            foreach (var processor in Values)
            {
                if (ExistsEnabledEntity(processor.Entity)) result.Add(processor.Entity);
            }
            return result.ToList();
        }

        internal IEnumerable<CatalogMember> Entities(ProcessorTransaction transactionType, ProcessorCoin coin)
        {
            if (transactionType == null) throw new ArgumentNullException(nameof(transactionType));
            if (coin == null) throw new ArgumentNullException(nameof(coin));

            var result = new HashSet<CatalogMember>();
            foreach (var processor in Values)
            {
                if (processor.Coin == coin.Coin && processor.Transactions.Contains(transactionType) && ExistsEnabledEntity(processor.Entity)) result.Add(processor.Entity);
            }
            return result.ToList();
        }

        internal IEnumerable<Entity> EntitiesForCashAndCreditCard(ProcessorTransaction transactionType)
        {
            if (transactionType == null) throw new ArgumentNullException(nameof(transactionType));

            var result = new HashSet<Entity>();
            var paymentMethodCash = SearchPaymentMethodByName(PaymentMethod.Cash.ToString());
            var paymentMethodCreditCard = SearchPaymentMethodByName(PaymentMethod.Creditcard.ToString());
            foreach (var processor in Values)
            {
                if ((processor.Group == paymentMethodCash || processor.Group == paymentMethodCreditCard) && processor.Transactions.Contains(transactionType) && ExistsEnabledEntity(processor.Entity))
                    result.Add(processor.Entity);
            }
            return result.ToList();
        }

        internal IEnumerable<CatalogMember> EntitiesPerformingDeposit(ProcessorCoin coin)
        {

            if (coin == null) throw new ArgumentNullException(nameof(coin));

            IEnumerable<PaymentProcessor> allDepositProcessor = mainIndexerProcessors.AllProcessors.Where(pro => pro.Driver.TransactionTypeEnum == TransactionType.Deposit);
            if (!allDepositProcessor.Any()) return Enumerable.Empty<Entity>().ToList();

            var result = new HashSet<CatalogMember>();
            foreach (var pro in allDepositProcessor)
            {
                if (pro.Coin == coin.Coin && ExistsEnabledEntity(pro.Entity))
                {
                    result.Add(pro.Entity);
                }
            }

            return result.ToList();
        }

        internal IEnumerable<CatalogMember> EntitiesPerformingWithdrawal(ProcessorCoin coin)
        {
            if (coin == null) throw new ArgumentNullException(nameof(coin));

            IEnumerable<PaymentProcessor> allWithdrawalProcessor = mainIndexerProcessors.AllProcessors.Where(pro => pro.Driver.TransactionTypeEnum == TransactionType.Withdrawal);
            if (!allWithdrawalProcessor.Any()) return Enumerable.Empty<Entity>().ToList();

            var result = new HashSet<CatalogMember>();
            foreach (var pro in allWithdrawalProcessor)
            {
                if (pro.Coin == coin.Coin && ExistsEnabledEntity(pro.Entity))
                {
                    result.Add(pro.Entity);
                }
            }

            return result.ToList();
        }

        bool ExistsEnabledEntity(Entity entityToSearch)
        {
            var result = company.System.Entities.Any(entity => entity == entityToSearch && entity.Visible && entity.Enabled);
            return result;
        }

        internal IEnumerable<CatalogMember> PaymentMethods(Entity entity)
        {
            if (entity == null) throw new ArgumentNullException(nameof(entity));

            var result = new HashSet<CatalogMember>();
            foreach (var processor in Values)
            {
                if (processor.Entity == entity)
                {
                    var paymentMethod = company.System.PaymentMethods.FirstOrDefault(method => method == processor.Group && method.Visible && method.Enabled);
                    if (paymentMethod != null) result.Add(paymentMethod);
                }
            }
            return result.ToList();
        }

        internal IEnumerable<ProcessorPaymentMethod> PaymentMethodsForCashAndCreditCard(Entity entity, ProcessorTransaction transactionType)
        {
            if (entity == null) throw new ArgumentNullException(nameof(entity));
            if (transactionType == null) throw new ArgumentNullException(nameof(transactionType));

            var result = new HashSet<ProcessorPaymentMethod>();
            var paymentMethodCash = SearchPaymentMethodByName(PaymentMethod.Cash.ToString());
            var paymentMethodCreditCard = SearchPaymentMethodByName(PaymentMethod.Creditcard.ToString());
            foreach (var processor in Values)
            {
                if (processor.Entity == entity && processor.Transactions.Contains(transactionType))
                {
                    var paymentMethods = company.System.PaymentMethods.Where(method => (method == paymentMethodCash || method == paymentMethodCreditCard) && method == processor.Group && method.Visible && method.Enabled);
                    foreach (var paymentMethod in paymentMethods)
                    {
                        result.Add((ProcessorPaymentMethod)paymentMethod);
                    }
                }
            }
            return result.ToList();
        }

        internal IEnumerable<CatalogMember> TransactionTypes(Entity entity, ProcessorPaymentMethod paymentMethod)
        {
            if (entity == null) throw new ArgumentNullException(nameof(entity));
            if (paymentMethod == null) throw new ArgumentNullException(nameof(paymentMethod));

            var result = new HashSet<CatalogMember>();
            foreach (var processor in Values)
            {
                if (processor.Entity == entity && processor.Group == paymentMethod)
                {
                    var transaction = company.System.TransactionTypes.FirstOrDefault(type => processor.Transactions.Contains(type) && type.Visible && type.Enabled);
                    if (transaction != null) result.Add(transaction);
                }
            }
            return result.ToList();
        }

        internal IEnumerable<CatalogMember> Coins()
        {
            var result = new HashSet<CatalogMember>();
            foreach (var processor in Values)
            {
                var coinFound = company.System.Coins.FirstOrDefault(coin => processor.Coin == ((ProcessorCoin)coin).Coin && coin.Visible && coin.Enabled);
                if (coinFound != null) result.Add(coinFound);
            }
            return result.ToList();
        }

        internal IEnumerable<CatalogMember> Coins(Entity entity, ProcessorPaymentMethod paymentMethod, ProcessorTransaction transactionType)
        {
            if (entity == null) throw new ArgumentNullException(nameof(entity));
            if (paymentMethod == null) throw new ArgumentNullException(nameof(paymentMethod));
            if (transactionType == null) throw new ArgumentNullException(nameof(transactionType));

            var result = new HashSet<CatalogMember>();
            foreach (var processor in Values)
            {
                if (processor.Entity == entity && processor.Group == paymentMethod && processor.Transactions.Contains(transactionType))
                {
                    var coinFound = company.System.Coins.FirstOrDefault(coin => processor.Coin == ((ProcessorCoin)coin).Coin && coin.Visible && coin.Enabled);
                    if (coinFound != null) result.Add(coinFound);
                }
            }
            return result.ToList();
        }

        internal IEnumerable<CatalogMember> Coins(ProcessorTransaction transactionType)
        {
            if (transactionType == null) throw new ArgumentNullException(nameof(transactionType));

            var result = new HashSet<CatalogMember>();
            foreach (var processor in Values)
            {
                if (processor.Transactions.Contains(transactionType))
                {
                    var coinFound = company.System.Coins.FirstOrDefault(coin => processor.Coin == ((ProcessorCoin)coin).Coin && coin.Visible && coin.Enabled);
                    if (coinFound != null) result.Add(coinFound);
                }
            }
            return result.ToList();
        }

        internal void ReorderEntities(ProcessorTransaction transactionType, ProcessorCoin coin, List<int> entityIdsToReorder)
        {
            if (transactionType == null) throw new ArgumentNullException(nameof(transactionType));
            if (coin == null) throw new ArgumentNullException(nameof(coin));
            if (entityIdsToReorder == null || entityIdsToReorder.Count == 0) throw new ArgumentNullException(nameof(coin));
            if (entityIdsToReorder.Count == 1) throw new GameEngineException($"It cannot reorder only one element");
            var anyDuplicate = entityIdsToReorder.GroupBy(id => id).Any(id => id.Count() > 1);
            if (anyDuplicate) throw new GameEngineException($"Any entity id is repeated");
            if (Entities().Count() != entityIdsToReorder.Count) throw new GameEngineException($"Amount of entities ids do not match with amount of entities found for {transactionType} and {coin}");

            Values.ReorderEntities(transactionType, coin.Coin, entityIdsToReorder);
            var processors = mainIndexerProcessors.AllProcessors.ToList();
            foreach (var processor in processors)
            {
                if (processor.ContainsTransactionType(transactionType))
                {
                    mainIndexerProcessors.ReorderEntities(transactionType, coin.Coin, entityIdsToReorder);
                }
            }
            _paymentProcessorsAndActionsByDomains.ReorderEnabledProcessors(transactionType, coin.Coin, entityIdsToReorder);
        }

        internal IEnumerable<Entity> EntitiesWithProviders(ProcessorTransaction transactionType)
        {
            if (transactionType == null) throw new ArgumentNullException(nameof(transactionType));

            var result = _paymentProcessorsAndActionsByDomains.EntitiesWithProviders(transactionType);
            return result.ToList();
        }

        internal IEnumerable<ProcessorPaymentMethod> PaymentMethodsWithProviders(Entity entity, ProcessorTransaction transactionType)
        {
            if (entity == null) throw new ArgumentNullException(nameof(entity));
            if (transactionType == null) throw new ArgumentNullException(nameof(transactionType));

            var result = _paymentProcessorsAndActionsByDomains.PaymentMethodsWithProviders(entity, transactionType);
            return result.ToList();
        }

        internal PaymentProcessor SearchProcessorWithHigherVersion(int entityId, int paymentMethodId, int transactionTypeId, int coinId)
        {
            if (entityId <= 0) throw new GameEngineException($"{nameof(entityId)} must be greater than 0");
            if (paymentMethodId <= 0) throw new GameEngineException($"{nameof(paymentMethodId)} must be greater than 0");
            if (transactionTypeId <= 0) throw new GameEngineException($"{nameof(transactionTypeId)} must be greater than 0");
            if (coinId < 0) throw new GameEngineException($"{nameof(coinId)} must be greater or equal than 0");

            var transactionMember = (ProcessorTransaction)company.System.TransactionTypes.Find(transactionTypeId);
            if (!Enum.TryParse(transactionMember.Name, out TransactionType fixedTransactionType)) throw new GameEngineException($"TransactionType {transactionMember.Name} is not a valid TransactionType");

            var coinMember = (ProcessorCoin)company.System.Coins.Find(coinId);

            var paymentMember = (ProcessorPaymentMethod)company.System.PaymentMethods.Find(paymentMethodId);
            if (!Enum.TryParse(paymentMember.Name, out PaymentMethod fixedPaymentMethod)) throw new GameEngineException($"PaymentMethod {paymentMember.Name} is not a valid PaymentMethod");

            //var processors = SearchProcessors(entityId, paymentMethodId, transactionTypeId, coinId);
            var processors = mainIndexerProcessors.GetPaymentProcessors(fixedTransactionType, coinMember.Coin.Iso4217Code, entityId, fixedPaymentMethod);
            if (!processors.Any()) throw new GameEngineException($"No processor found for {nameof(entityId)} '{entityId}' {nameof(paymentMethodId)} '{paymentMethodId}' {nameof(transactionTypeId)} '{transactionTypeId}' {nameof(coinId)} '{coinId}'");

            PaymentProcessor processorWithHigherVersion = processors.First();
            foreach (var paymentProcessor in processors.Skip(1))
            {
                if (paymentProcessor.Driver.Version > processorWithHigherVersion.Driver.Version)
                    processorWithHigherVersion = paymentProcessor;
            }

            return processorWithHigherVersion;
        }

        internal PaymentProcessor SearchProcessorWithHigherVersion(int agent, string domain, int entityId, PaymentMethod paymentMethod, TransactionType transactionType, string coinCode)
        {
            if (entityId <= 0) throw new GameEngineException($"{nameof(entityId)} must be greater than 0");
            if (string.IsNullOrEmpty(coinCode)) throw new ArgumentNullException(nameof(coinCode));

            var processors = PaymentProcessorsAndActionsByDomains.SearchForX(agent, domain);
            return processors.SearchByX(transactionType, coinCode, paymentMethod, entityId);
        }

        internal bool AllProcessorsWithTheSameVersion(int entityId, int paymentMethodId, int transactionTypeId, int coinId)
        {
            if (entityId <= 0) throw new GameEngineException($"{nameof(entityId)} must be greater than 0");
            if (paymentMethodId <= 0) throw new GameEngineException($"{nameof(paymentMethodId)} must be greater than 0");
            if (transactionTypeId <= 0) throw new GameEngineException($"{nameof(transactionTypeId)} must be greater than 0");
            if (coinId < 0) throw new GameEngineException($"{nameof(coinId)} must be greater or equal than 0");

            var transactionMember = (ProcessorTransaction)company.System.TransactionTypes.Find(transactionTypeId);
            if (!Enum.TryParse(transactionMember.Name, out TransactionType fixedTransactionType)) throw new GameEngineException($"TransactionType {transactionMember.Name} is not a valid TransactionType");

            var coinMember = (ProcessorCoin)company.System.Coins.Find(coinId);

            var paymentMember = (ProcessorPaymentMethod)company.System.PaymentMethods.Find(paymentMethodId);
            if (!Enum.TryParse(paymentMember.Name, out PaymentMethod fixedPaymentMethod)) throw new GameEngineException($"PaymentMethod {paymentMember.Name} is not a valid PaymentMethod");

            //var processors = SearchProcessors(entityId, paymentMethodId, transactionTypeId, coinId);
            var processors = mainIndexerProcessors.GetPaymentProcessors(fixedTransactionType, coinMember.Coin.Iso4217Code, entityId, fixedPaymentMethod);
            float version = processors.First().Driver.Version;
            foreach (var paymentProcessor in processors.Skip(1))
            {
                if (paymentProcessor.Driver.Version != version) return false;
            }

            return true;
        }

        internal bool AllProcessorsWithTheSameVersion(int agent, string domain, int entityId, PaymentMethod paymentMethod, TransactionType transactionType, string coinCode)
        {
            if (entityId <= 0) throw new GameEngineException($"{nameof(entityId)} must be greater than 0");

            var agentDomainIndex = PaymentProcessorsAndActionsByDomains.SearchForX(agent, domain);
            List<PaymentProcessor> paymentProcessors = agentDomainIndex.GetPaymentProcessors(transactionType, coinCode, entityId, paymentMethod);
            if (!paymentProcessors.Any())
            {
                var entityText = WholePaymentProcessor.Instance().Company.System.Entities.Find(entityId).Name;
                throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured for Entity Id: {entityId}({entityText}), {nameof(PaymentMethod)}: {paymentMethod}, {nameof(TransactionType)}: {transactionType} and Coin code: {coinCode}.");
            }

            List<PaymentProcessor> filterProcessors = paymentProcessors.FindAll(x => x.Visible && x.Enabled && x.Driver is ProcessorDriver);
            if (!filterProcessors.Any())
            {
                var entityText = WholePaymentProcessor.Instance().Company.System.Entities.Find(entityId).Name;
                throw new GameEngineException($"There is no {nameof(PaymentProcessor)} visible or enabled or object[ProcessorDriver] for Entity Id: {entityId}({entityText}), {nameof(PaymentMethod)}: {paymentMethod}, {nameof(TransactionType)}: {transactionType} and Coin code: {coinCode}.");
            }

            PaymentProcessor processorWithHigherVersion = filterProcessors.FirstOrDefault();
            float version = filterProcessors.First().Driver.Version;
            foreach (var paymentProcessor in filterProcessors.Skip(1))
            {
                if (paymentProcessor.Driver.Version != version) return false;
            }

            return true;
        }

        internal void EnableProcessor(int entityId, PaymentMethod paymentMethodId, TransactionType transactionType, string coinCode)
        {
            if (entityId <= 0) throw new GameEngineException($"{nameof(entityId)} must be greater than 0");
            if (string.IsNullOrEmpty(coinCode)) throw new GameEngineException($"{nameof(coinCode)} must be greater or equal than 0");

            company.System.DriverManagers.EnablePaymentProcessor(transactionType, coinCode, entityId, paymentMethodId);
        }

        internal void DisableProcessor(int entityId, PaymentMethod paymentMethodId, TransactionType transactionType, string coinCode)
        {
            if (entityId <= 0) throw new GameEngineException($"{nameof(entityId)} must be greater than 0");            
            if (string.IsNullOrEmpty(coinCode)) throw new ArgumentNullException(nameof(coinCode));

            company.System.DriverManagers.DisablePaymentProcessor(transactionType, coinCode, entityId, paymentMethodId);
        }

        internal void UpdateAlias(int entityId, int paymentMethodId, int transactionTypeId, int coinId, string alias)
        {
            if (entityId <= 0) throw new GameEngineException($"{nameof(entityId)} must be greater than 0");
            if (paymentMethodId <= 0) throw new GameEngineException($"{nameof(paymentMethodId)} must be greater than 0");
            if (transactionTypeId <= 0) throw new GameEngineException($"{nameof(transactionTypeId)} must be greater than 0");
            if (coinId < 0) throw new GameEngineException($"{nameof(coinId)} must be greater or equal than 0");
            if (string.IsNullOrEmpty(alias)) throw new ArgumentNullException(nameof(alias));

            var processorWithHigherVersion = SearchProcessorWithHigherVersion(entityId, paymentMethodId, transactionTypeId, coinId);
            processorWithHigherVersion.Alias = alias;
        }

        public class PaymentProcessor : CatalogMember
        {
            public PaymentProcessor(int id, string name, Entity entity, ProcessorPaymentMethod paymentMethod, ProcessorCoin coin, GroupOFTransactions transactions, Driver implementation) :
                base(id, name)
            {
                Entity = entity;
                Group = paymentMethod;
                ProcessorCoin = coin;
                Transactions = transactions;
                Driver = implementation;
                ProcessorKey = $"{Entity.Name}_{Group.Name}_{Transactions.TransactionsAsText}_{ProcessorCoin.Iso4217Code}";
                Alias = ProcessorKey;
            }

            internal CustomSettingsCollection CustomSettings { get; set; }
            public Entity Entity { get; }
            public ProcessorPaymentMethod Group { get; }
            public string PaymentMethodAsText => Group.Name;
            internal town.connectors.drivers.PaymentMethod PaymentMethodType { get { return Driver.PaymentMethod; } }
            internal string PaymentMethodAsString { get { return $"{Driver.PaymentMethod}"; } }
            internal string TransactionTypeAsString { get { return $"{Driver.TransactionTypeEnum}"; } }
            public Coin Coin => ProcessorCoin.Coin;
            public ProcessorCoin ProcessorCoin { get; }
            public GroupOFTransactions Transactions { get; }
            public Driver Driver { get; }
            public string DriverId => Driver.Id;
            public string Description => Driver.Description;
            public string Version => string.Format("{0:N2}", Driver.Version);
            public string CurrencyIso4217Code { get { return Coin.Iso4217Code; } }
            public string Alias { get; set; }
            public string ProcessorKey { get; }
            public string Channel => $"{Transactions.TransactionsAsText}";
            public string EventType => $"{Channel}.{Coin.Iso4217Code}";

            internal override bool Enabled
            {
                get
                {
                    return base.Enabled;
                }
                set
                {
                    rwDriverLock.EnterWriteLock();
                    try
                    {
                        base.Enabled = value;
                    }
                    finally
                    {
                        rwDriverLock.ExitWriteLock();
                    }
                }
            }

            public T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
            {
                string transactionId = null;
                try
                {
                    Loggers.GetIntance().GetOrCreateLogger(Driver.Id).Debug($"Now={now}\nProcessorKey={ProcessorKey}\nbody={recordSet}");
                    var transaction = APMHelper.StartTransaction("DRIVER_" + ProcessorKey, Entity.Name);
                    transactionId = APMHelper.CurrentTransactionId(transaction);
                    APMHelper.StartSpan(transactionId, transactionId);
                if (Driver is ProcessorDriver)
                {
                    var result = (Driver as ProcessorDriver).Execute<T>(now, recordSet);
                    if (!(result is Result)) throw new GameEngineException($"{nameof(ProcessorDriver)}s can only return {nameof(Result)} subclases");
                        APMHelper.EndSpan(transactionId);
                        if (WebHookClient.IsConfigured)
                        {
                            _ = Task.Run(() => {
                                _ = WebHookClient.Instance.SendWebHook(now, recordSet, EventType, Channel);
                            });
                        }
                        string jsonResult = JsonConvert.SerializeObject(result);
                        Loggers.GetIntance().GetOrCreateLogger(Driver.Id).Debug($"Finish: Now={now} ProcessorKey={ProcessorKey} driverResult={jsonResult}");
                        //return ExecuteResponse<T>.Success(result);
                    return result;
                }
                else if (Driver is TenantDriver)
                {
                        var result = (Driver as TenantDriver).Execute<T>(now, recordSet);
                        APMHelper.EndSpan(transactionId);
                        if (WebHookClient.IsConfigured)
                        {
                            _ = Task.Run(() => {
                                _ = WebHookClient.Instance.SendWebHook(now, recordSet, EventType, Channel);
                            });
                        }
                        string jsonResult = JsonConvert.SerializeObject(result);
                        Loggers.GetIntance().GetOrCreateLogger(Driver.Id).Debug($"Finish: Now={now} ProcessorKey={ProcessorKey} driverResult={jsonResult}");
                      
                        //return ExecuteResponse<T>.Success(result);
                        return result;
                }
                else
                {
                    throw new GameEngineException($"Driver {Driver.GetType().Name} is not configured.");
                }
            }
                catch (Exception ex)
                {
                    StringBuilder errorMessage = new StringBuilder();
                    errorMessage.AppendLine("GameEngineException:");
                    errorMessage.AppendLine($"Now={now}");
                    errorMessage.AppendLine($"Message={ex.Message}");
                    errorMessage.AppendLine($"CustomSetting RecordSet:");
                    errorMessage.AppendLine($"{recordSet.ToString()}");

                    Loggers.GetIntance().GetOrCreateLogger(Driver.Id).Error(errorMessage.ToString(), ex);
                    return default(T);
                    //return ExecuteResponse<T>.Failure(errorMessage.ToString());
                }
                finally
            {
                    if (transactionId != null) APMHelper.DisposeSpan(transactionId);

                    APMHelper.EndTransaction();
                }
            }

            public async Task<ExecuteResponse<T>> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
                {
                string transactionId = null;

                try
                {
                    Loggers.GetIntance().GetOrCreateLogger(Driver.Id).Debug($"ProcessorKey={ProcessorKey} body={recordSet}");
                    var transaction = APMHelper.StartTransaction("DRIVER_" + ProcessorKey, Entity.Name);
                    transactionId = APMHelper.CurrentTransactionId(transaction);
                    APMHelper.StartSpan(transactionId, transactionId);
                    if (Driver is ProcessorDriver processorDriver)
                    {
                        var result = await processorDriver.ExecuteAsync<T>(now, recordSet);
                        APMHelper.EndSpan(transactionId);
                        if (WebHookClient.IsConfigured)
                        {
                            _ = Task.Run(() => {
                                _ = WebHookClient.Instance.SendWebHookAsync(now, recordSet, EventType, Channel);
                            });
                        }
                        if (!(result is Result))
                        {
                            Loggers.GetIntance().GetOrCreateLogger(Driver.Id).Debug($"Failure: Now={now} ProcessorKey={ProcessorKey} body={recordSet}");
                            return ExecuteResponse<T>.Failure($"{nameof(ProcessorDriver)}s can only return {nameof(Result)} subclasses");
                        }
                        string jsonResult = JsonConvert.SerializeObject(result);
                        Loggers.GetIntance().GetOrCreateLogger(Driver.Id).Debug($"Finish: Now={now} ProcessorKey={ProcessorKey} driverResult={jsonResult}");
                        return ExecuteResponse<T>.Success(result);
                    }
                    else if (Driver is TenantDriver tenantDriver)
                {
                        var result = await tenantDriver.ExecuteAsync<T>(now, recordSet);
                        APMHelper.EndSpan(transactionId);
                        if (WebHookClient.IsConfigured)
                        {
                            _ = Task.Run(() => {
                                _ = WebHookClient.Instance.SendWebHookAsync(now, recordSet, EventType, Channel);
                            });
                        }
                        string jsonResult = JsonConvert.SerializeObject(result);
                        Loggers.GetIntance().GetOrCreateLogger(Driver.Id).Debug($"Finish: Now={now} ProcessorKey={ProcessorKey} driverResult={jsonResult}");
                        return ExecuteResponse<T>.Success(result);
                }
                else
                {
                        throw new Exception($"Driver {Driver.GetType().Name} is not configured.");
                }
            }
                catch (Exception ex)
                {
                    StringBuilder errorMessage = new StringBuilder();
                    errorMessage.AppendLine("GameEngineException:");
                    errorMessage.AppendLine($"Now={now}");
                    errorMessage.AppendLine($"Message={ex.Message}");
                    errorMessage.AppendLine($"CustomSetting RecordSet:");
                    errorMessage.AppendLine($"{recordSet}");
                   
                    Loggers.GetIntance().GetOrCreateLogger(Driver.Id).Error(errorMessage.ToString(), ex);
                    return ExecuteResponse<T>.Failure(errorMessage.ToString());
                }
                finally{

                    if (transactionId != null) APMHelper.DisposeSpan(transactionId);
                    
                    APMHelper.EndTransaction();
                }
            }

            public enum Status
            {
                Ok,
                Fail
            }

            public class ExecuteResponse<T>
            {
                public Status Status { get; set; }
                public T? Data { get; set; }
                public string? ErrorMessage { get; set; }

                public static ExecuteResponse<T> Success(T data) => new ExecuteResponse<T> { Status = Status.Ok, Data = data };
                public static ExecuteResponse<T> Failure(string error) => new ExecuteResponse<T> { Status = Status.Fail, ErrorMessage = error };
            }

            internal bool ContainsTransactionType(string transactionTypeName)
            {
                return Transactions.FirstOrDefault(x => x.Visible && x.Enabled && x.ToString() == transactionTypeName) != null;
            }
            internal bool ContainsTransactionType(ProcessorTransaction transaction)
            {
                return Transactions.Any(transaction);
            }

            internal RecordSet GetRecordSet()
            {
                rwDriverLock.EnterReadLock();
                try
                {
                    return Driver.CustomSettings.ThreadUnsafeGetRecordSet();
                }
                finally
                {
                    rwDriverLock.ExitReadLock();
                }
            }

            internal async Task<RecordSet> GetRecordSetAsync(string playerId)
            {
                if (string.IsNullOrWhiteSpace(playerId)) throw new ArgumentNullException(nameof(playerId));

                var result = this.GetRecordSet();

                if (CustomSettings.HasTownAttributes)
                {
                    var attributeResults = Enumerable.Empty<AttributeItem>();
                    rwDriverLock.EnterReadLock();
                    try
                    {
                        attributeResults = await CustomSettings.ThreadUnsafeGatherAttributesAsync(playerId);
                    }
                    finally
                    {
                        rwDriverLock.ExitReadLock();
                    }
                    if (attributeResults.Any())
                    {
                        foreach (var itemCS in attributeResults)
                        {
                            string key = itemCS.AttributeKey;
                            result.SetParameter(key, itemCS.AttributeValue);
                        }
                    }
                }

                return result;
            }

            internal void Prepare(DateTime now)
            {
                Driver.Prepare(now);
            }

            internal bool UseDriver(Type type)
            {
                return Driver.GetType() == type;
            }

            public void NotifyPay()
            {
                if (Transactions.Search(TransactionType.Sale.ToString())) throw new GameEngineException($"{TransactionType.Sale.ToString()} it's not allowed for {this.GetType().Name}.");

                throw new NotImplementedException();
            }
            public void NotifyWithDraw(bool itsThePresent, DateTime creationDate, int transactionId, string description, Currency disbursementAmount, string identificationDocumentNumber,
                string customerAccountNumber, Domain domain, TransactionFees fees, int storeId, string employeeName, string destinationAddress)
            {
                NotifyWithDraw(itsThePresent, creationDate, transactionId, description, disbursementAmount, identificationDocumentNumber, customerAccountNumber, domain, fees, storeId, employeeName, destinationAddress, default(Execution));
            }
            public void NotifyWithDraw(bool itsThePresent, DateTime creationDate, int transctionId, string description, Currency disbursementAmount, string identificationDocumentNumber,
                string customerAccountNumber, Domain domain, TransactionFees fees, int storeId, string employeeName, string destinationAddress, Execution execution)
            {
                if (!Transactions.Search(TransactionType.Withdrawal.ToString())) throw new GameEngineException($"{TransactionType.Withdrawal.ToString()} it's not allowed for {this.GetType().Name}.");
                if (CurrencyIso4217Code != disbursementAmount.CurrencyCode) throw new GameEngineException($"{nameof(PaymentProcessor)} doesn't support transactions in {disbursementAmount.CurrencyCode.ToString()}");
                if (string.IsNullOrEmpty(description)) throw new ArgumentNullException(nameof(description));
                if (domain == null) throw new ArgumentNullException(nameof(domain));
                if (fees == null) throw new ArgumentNullException(nameof(fees));

                if (itsThePresent)
                {
                    OperationMessage msn = new OperationMessage(
                        customerAccountNumber,
                        TransactionType.Withdrawal,
                        creationDate,
                        transctionId,
                        description,
                        disbursementAmount,
                        Group,
                        Driver.Id,
                        domain.Id,
                        domain.Url,
                        fees,
                        execution,
                        identificationDocumentNumber,
                        storeId,
                        employeeName,
                        destinationAddress
                        );

                    Integration.Kafka.Send(itsThePresent, Integration.Kafka.TopicForGuardianOperations, msn);
                }
            }

            internal bool ContainsKeyName(string keyName)
            {
                if (CustomSettings == null) throw new GameEngineException($"{nameof(CustomSettings)} is null");
                if (string.IsNullOrEmpty(keyName)) throw new ArgumentNullException(nameof(keyName));

                return CustomSettings.Values.ContainsKeyName(keyName);
            }

            internal CustomSetting ChangeAttributeValueStartingOnX(DateTime now, string key, string attributeValue, string user, DateTime dateToApplyTheChange, string description, bool enabled, bool canBeVisible)
            {

                if (!TryToGetAttribute(attributeValue, out string actorKey, out string attributeKey)) throw new GameEngineException($"Invalid {nameof(attributeValue)} format.");

                if (!TownSettings.TryToGetActorUrl(actorKey, out _)) throw new GameEngineException($"There is no actor '{actorKey}' found for attribute '{attributeKey}'");
                
                rwDriverLock.EnterWriteLock();
                try
                {
                    return CustomSettings.ChangeAttributeValueStartingOnX(now, key, attributeValue, user, dateToApplyTheChange, description, enabled, canBeVisible);
                }
                finally
                {
                    rwDriverLock.ExitWriteLock();
                }
            }


            internal CustomSetting ChangeTextValueStartingOnX(DateTime now, string key, string value, string user, DateTime dateToApplyTheChange, string description, bool enabled, bool canBeVisible)
            {
                if (value == null) throw new ArgumentException(nameof(value));
                if (dateToApplyTheChange == default(DateTime)) throw new ArgumentException(nameof(dateToApplyTheChange));
                if (string.IsNullOrEmpty(key)) throw new ArgumentException(nameof(key));
                if (string.IsNullOrEmpty(user)) throw new ArgumentException(nameof(user));
                if (string.IsNullOrEmpty(description)) throw new ArgumentException(nameof(description));
                if (dateToApplyTheChange < now) throw new GameEngineException($"{nameof(dateToApplyTheChange)} must be greater than {nameof(now)}");

                rwDriverLock.EnterWriteLock();
                try
                {
                    return CustomSettings.ChangeValueStartingOnX(now, key, value, user, dateToApplyTheChange, description, enabled, canBeVisible);
                }
                finally
                {
                    rwDriverLock.ExitWriteLock();
                }
            }

            internal CustomSetting ChangeSecretStartingOnX(DateTime now, string key, object value, string user, DateTime dateToApplyTheChange, string description, bool enabled)
            {
                if (value == null) throw new ArgumentException(nameof(value));
                if (now == default(DateTime)) throw new ArgumentException(nameof(dateToApplyTheChange));
                if (dateToApplyTheChange == default(DateTime)) throw new ArgumentException(nameof(dateToApplyTheChange));
                if (string.IsNullOrWhiteSpace(key)) throw new ArgumentException(nameof(key));
                if (string.IsNullOrEmpty(user)) throw new ArgumentException(nameof(user));
                if (string.IsNullOrEmpty(description)) throw new ArgumentException(nameof(description));

                rwDriverLock.EnterWriteLock();
                try
                {
                    return CustomSettings.ChangeSecretStartingOnX(now, key, value, user, dateToApplyTheChange, description, enabled);
                }
                finally
                {
                    rwDriverLock.ExitWriteLock();

                }
            }
            internal CustomSetting ChangeValueStartingOnX(DateTime now, string key, object value, string user, DateTime dateToApplyTheChange, string description, bool enabled, bool canBeVisible)
            {
                if (value == null) throw new ArgumentException(nameof(value));
                if (dateToApplyTheChange == default(DateTime)) throw new ArgumentException(nameof(dateToApplyTheChange));
                if (string.IsNullOrEmpty(key)) throw new ArgumentException(nameof(key));
                if (string.IsNullOrEmpty(user)) throw new ArgumentException(nameof(user));
                if (string.IsNullOrEmpty(description)) throw new ArgumentException(nameof(description));
                if (dateToApplyTheChange < now) throw new GameEngineException($"{nameof(dateToApplyTheChange)} must be greater than {nameof(now)}");

                rwDriverLock.EnterWriteLock();
                try
                {
                    return CustomSettings.ChangeValueStartingOnX(now, key, value, user, dateToApplyTheChange, description, enabled, canBeVisible);
                }
                finally
                {
                    rwDriverLock.ExitWriteLock();
                }
            }

            internal void DeleteAttributeX(DateTime now, string key, string user, string description, bool enabled)
            {
                if (string.IsNullOrWhiteSpace(key)) throw new ArgumentException(nameof(key));
                if (string.IsNullOrEmpty(user)) throw new ArgumentException(nameof(user));
                if (string.IsNullOrEmpty(description)) throw new ArgumentException(nameof(description));

                rwDriverLock.EnterWriteLock();
                try
                {
                    CustomSettings.DeleteAttributeX(now, key, user, description, enabled);
                }
                finally
                {
                    rwDriverLock.ExitWriteLock();
                }

            }
        }

        internal ProcessorTransaction SearchTransactionByName(string type)
        {
            if (string.IsNullOrWhiteSpace(type)) throw new ArgumentNullException(nameof(type));

            var transaction = company.System.TransactionTypes.SearchByName<ProcessorTransaction>(type);
            return transaction;
        }

        internal ProcessorPaymentMethod SearchPaymentMethodByName(string method)
        {
            if (string.IsNullOrWhiteSpace(method)) throw new ArgumentNullException(nameof(method));

            var result = company.System.PaymentMethods.SearchByName<ProcessorPaymentMethod>(method);
            return result;
        }

       

        struct SelectedDriver
        {
            public ProcessorPaymentMethod PaymentMethod { get; set; }
            public string CurrencyIso4217Code { get; set; }
            public bool IsProcessorDriver { get; set; }
            public ProcessorTransaction TransactionType { get; set; }
            public Entity Entity { get; set; }
            public Driver Implementation { get; set; }
            public SelectedDriver(ProcessorPaymentMethod PaymentMethod, string CurrencyIso4217Code, bool IsProcessorDriver, ProcessorTransaction TransactionType, Entity Entity, Driver Implementation)
            {
                this.PaymentMethod = PaymentMethod;
                this.CurrencyIso4217Code = CurrencyIso4217Code;
                this.IsProcessorDriver = IsProcessorDriver;
                this.TransactionType = TransactionType;
                this.Entity = Entity;
                this.Implementation = Implementation;
            }
        }

        private CustomSettingsCollection templateCustomSettings;
        internal bool Load(bool itIsThePresent, DateTime now, CustomSettingsCollection customSettingsGenesis)
        {
            if (customSettingsGenesis == null) throw new ArgumentNullException(nameof(customSettingsGenesis));

            string path = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
            string logs = "";
            Drivers = Driver.LoadAllConnectorsFrom(path, out logs);
            if (!string.IsNullOrWhiteSpace( logs)) Debug.WriteLine(logs);

            this.templateCustomSettings = customSettingsGenesis;
            Reload(now);

            return Values.Count() > 0;
        }

        public void Reload(DateTime now)
        {
            if (Drivers == null) throw new GameEngineException("Drivers is null");
            if (templateCustomSettings == null) throw new GameEngineException("CustomSettings is null");

            members.Clear();
            Values.Clear();
            indexedValidateProcessor = new SingleProcessor();
            indexedGradeProcessor = new SingleProcessor();
            indexedFragmentProcessor = new SingleProcessor();
            otherProcessors = new SingleProcessor();

            var selectedDrivers = new List<SelectedDriver>();
            foreach (var driver in Drivers)
            {
                foreach (var isoCode in driver.CurrencyIsoCodes)
                {
                    IEnumerable<CatalogMember> activeTransactions = company.System.TransactionTypes.Where(x => x.Visible && x.Enabled);
                    ProcessorTransaction processorTransactionCorregido = (ProcessorTransaction)activeTransactions.FirstOrDefault(x => {
                        if (driver.TransactionTypeEnum == null)
                        {
                            return x.Name.StartsWith($"{TransactionType.RetrieveInfo.ToString()}");
                        }
                        else
                        {
                            return x.Name == driver.TransactionTypeEnum.ToString();
                        }
                    });

                    selectedDrivers.Add(new SelectedDriver(
                        (ProcessorPaymentMethod)company.System.PaymentMethods.
                            FirstOrDefault(x => x.Visible && x.Enabled &&
                            x.Name == driver.PaymentMethod.ToString()),
                        (company.System.Coins.
                        FirstOrDefault(x => x.Visible && x.Enabled &&
                        (x as ProcessorCoin).Coin.Iso4217Code == isoCode) as ProcessorCoin)?.Coin.Iso4217Code,
                        driver is ProcessorDriver,
                        processorTransactionCorregido,
                        (Entity)company.System.Entities.
                            FirstOrDefault(x => x.Visible && x.Enabled &&
                            x.Name == driver.Entity),
                        driver)
                    );
                }
            }

            foreach (var driver in selectedDrivers)
            {

                if (driver.Entity == null
                    || driver.PaymentMethod == null
                    || driver.CurrencyIso4217Code == null
                    || driver.TransactionType == null
                    || driver.Implementation == null)
                    continue;

                bool deactivateProcessorAdded = false;
                if (driver.IsProcessorDriver)
                {
                    company.System.DriverManagers.SearchProcessor(driver.Entity.Id, driver.PaymentMethod.Id, driver.TransactionType.Id, driver.CurrencyIso4217Code, out PaymentProcessor processorFoundWithTheSameParams);
                    if (processorFoundWithTheSameParams != null)
                    {
                        if (driver.Implementation.Version >= processorFoundWithTheSameParams.Driver.Version) processorFoundWithTheSameParams.Enabled = false;
                        if (driver.Implementation.Version <= processorFoundWithTheSameParams.Driver.Version) 
                            deactivateProcessorAdded = true;
                    }
                }

                var processorAdded = (PaymentProcessor)company.System.DriverManagers.GetOrCreatePaymentProcessor(
                    driver.Entity,
                    driver.PaymentMethod,
                    driver.CurrencyIso4217Code,
                    driver.TransactionType,
                    driver.Implementation
                );
                if (deactivateProcessorAdded) processorAdded.Enabled = false;
                processorAdded.CustomSettings = new CustomSettingsCollection(company);
                processorAdded.CustomSettings.CopyFrom(templateCustomSettings);

                driver.Implementation.ConfigureThenPrepare(now, processorAdded.CustomSettings.Values);

            }

            if (_paymentProcessorsAndActionsByDomains == null) _paymentProcessorsAndActionsByDomains = new PaymentProcessorsAndActionsByDomains(this);

            foreach (var processor in mainIndexerProcessors.AllProcessors)
            {
                if (!processor.Enabled) continue;

                ProcessorCoin coinMember = (ProcessorCoin)company.System.Coins.FirstOrDefault(x => (x as ProcessorCoin).Coin.Iso4217Code == processor.CurrencyIso4217Code);
                bool coinIsEnabled = coinMember != null && coinMember.Enabled;
                bool coinIsVisible = coinMember != null && coinMember.Visible;

                ProcessorTransaction transactionMember = (ProcessorTransaction)company.System.TransactionTypes.FirstOrDefault(x => x.Name == processor.TransactionTypeAsString);
                bool transactionIsEnabled = transactionMember != null && transactionMember.Enabled;
                bool transactionIsVisible = transactionMember != null && transactionMember.Visible;

                ProcessorPaymentMethod paymentMethodMember = (ProcessorPaymentMethod)company.System.PaymentMethods.FirstOrDefault(x => x.Name == processor.PaymentMethodAsString);
                bool paymentMethodIsEnabled = paymentMethodMember != null && paymentMethodMember.Enabled;
                bool paymentMethodIsVisible = paymentMethodMember != null && paymentMethodMember.Visible;

                Entity entityMember = (Entity)company.System.Entities.FirstOrDefault(x => x.Name == processor.Entity.Name);
                bool entityIsEnabled = entityMember != null && entityMember.Enabled;
                bool entityIsVisible = entityMember != null && entityMember.Visible;

                processor.Enabled = coinIsEnabled && transactionIsEnabled && paymentMethodIsEnabled && entityIsEnabled;
                processor.Visible = coinIsVisible && transactionIsVisible && paymentMethodIsVisible && entityIsVisible;

                if (processor.Enabled && processor.Visible)
                {
                    if (!_paymentProcessorsAndActionsByDomains.Contains(processor)) _paymentProcessorsAndActionsByDomains.Enable(processor);
                }
            }
        }

        private bool IsAValidateDriver(Driver driver)
        {
            return driver is TenantDriver &&
                    (driver as TenantDriver).TenantAction == Tenant_Actions.Validate;
        }
        private bool IsAFragmentDriver(Driver driver)
        {
            return driver is TenantDriver &&
                    (driver as TenantDriver).TenantAction == Tenant_Actions.Fragment;
        }
        private bool IsAnOtherDriver(Driver driver)
        {
            return driver is TenantDriver &&
                    (driver as TenantDriver).TenantAction == Tenant_Actions.Others;
        }

        private bool IsAGradeDriver(Driver driver)
        {
            return driver is TenantDriver &&
                    (driver as TenantDriver).TenantAction == Tenant_Actions.Grade;
        }

        internal void TryGetValue(string id, out PaymentProcessor result)
        {
            result = Values.FirstOrDefault(x => x.Driver.Id == id);
        }

        internal PaymentProcessor SearchPaymentProcessor(TransactionType transactionType, string coinCode, int entityId, PaymentMethod method)
        {
            var result = mainIndexerProcessors.SearchPaymentProcessorX(transactionType, coinCode, entityId, method);
            return result;
        }

        internal PaymentProcessor AllPaymentProcessor(TransactionType transactionType, string coinCode, int entityId, PaymentMethod method)
        {
            return mainIndexerProcessors.AllPaymentProcessorX(transactionType, coinCode, entityId, method);
        }

        internal void EnablePaymentProcessor(TransactionType transactionType, string coinCode, int entityId, PaymentMethod method)
        {
            mainIndexerProcessors.EnablePaymentProcessor(transactionType, coinCode, entityId, method);
        }

        internal void DisablePaymentProcessor(TransactionType transactionType, string coinCode, int entityId, PaymentMethod method)
        {
            mainIndexerProcessors.DisablePaymentProcessor(transactionType, coinCode, entityId, method);
        }

        internal bool ExistPaymentProcessor(TransactionType transactionType, string coinCode, int entityId, PaymentMethod method)
        {
            return mainIndexerProcessors.ExistPaymentProcessor(transactionType, coinCode, entityId, method);
        }

        internal void AddEmptyParameter(DateTime now, string key, string user, string description, bool enabled)
        {
            AddEmptyParameter(now, key, user, description, enabled, false);
        }

        private static ReaderWriterLockSlim rwDriverLock = new ReaderWriterLockSlim();

        internal void AddEmptyParameter(DateTime now, string key, string user, string description, bool enabled, bool canBeVisible)
        {
            if (now == default(DateTime)) throw new ArgumentNullException(nameof(now));
            if (string.IsNullOrEmpty(key)) throw new ArgumentNullException(nameof(key));
            if (string.IsNullOrEmpty(user)) throw new ArgumentNullException(nameof(user));
            if (string.IsNullOrEmpty(description)) throw new ArgumentNullException(nameof(description));

            rwDriverLock.EnterWriteLock();
            try
            {
                foreach (var member in members)
                {
                    PaymentProcessor processor = (PaymentProcessor)member;
                    processor.CustomSettings.AddFixedParameter(now, key, user, description, enabled, canBeVisible);
                }
            }
            finally
            {
                rwDriverLock.ExitWriteLock();
            }
        }

        internal void AddEmptyAttribute(DateTime now, string key, string user, string description, bool enabled)
        {
            AddEmptyAttribute(now, key, user, description, enabled, false);
    }

        internal void AddEmptyAttribute(DateTime now, string key, string user, string description, bool enabled, bool canBeVisible)
        {
            if (now == default(DateTime)) throw new ArgumentNullException(nameof(now));
            if (string.IsNullOrEmpty(key)) throw new ArgumentNullException(nameof(key));
            if (string.IsNullOrEmpty(user)) throw new ArgumentNullException(nameof(user));
            if (string.IsNullOrEmpty(description)) throw new ArgumentNullException(nameof(description));

            rwDriverLock.EnterWriteLock();
            try
            {
                foreach (var member in members)
                {
                    PaymentProcessor processor = (PaymentProcessor)member;
                    processor.CustomSettings.AddAttribute(now, key, user, description, enabled, canBeVisible);
                }
            }
            finally
            {
                rwDriverLock.ExitWriteLock();
            }
        }

        private bool AttributeIsInUse(DateTime now, string key)
        {
            if (now == default(DateTime)) throw new ArgumentNullException(nameof(now));
            if (string.IsNullOrEmpty(key)) throw new ArgumentNullException(nameof(key));

            foreach (var member in members)
            {
                PaymentProcessor processor = (PaymentProcessor)member;
                if (processor.CustomSettings.HasTownAttributes)
                {
                    if (processor.CustomSettings.HasTownAttribute(key))
                    {
                        CustomSetting customSetting = processor.CustomSettings.Get(now, key);
                        if (customSetting.ConnectorsCustomSetting is TownAttributeCustomSetting townAttributeCustomSetting)
                        {
                            if(!townAttributeCustomSetting.IsActorKeyEmpty) return true;
                        }
                    }
                }
            }
            return false;
        }

        internal void DeleteAttributeX(DateTime now, string key)
        {
            if (now == default(DateTime)) throw new ArgumentNullException(nameof(now));
            if (string.IsNullOrEmpty(key)) throw new ArgumentNullException(nameof(key));

            bool canBeDeleteFromAllProcessor = !AttributeIsInUse(now, key);
            if (canBeDeleteFromAllProcessor)
            {
                foreach (var member in members)
                {
                    PaymentProcessor processor = (PaymentProcessor)member;
                    if (processor.CustomSettings.HasTownAttributes)
                    {
                        if (processor.CustomSettings.HasTownAttribute(key))
                        {
                            processor.CustomSettings.RemoveAttribute(now, key);
                        }
                    }
                }
            }
        }
    }

    [Puppet]
    public class CustomSettingsCollection : Objeto, IEnumerable<CustomSetting>
    {
        Dictionary<string, CustomSetting> customSettings = new Dictionary<string, CustomSetting>();
        internal IEnumerable<CustomSetting> CustomSettings 
        {
            get
            {
                return customSettings.Values.ToList();
            }
        }

        internal CustomSettings Values { get; }
        Company company;
        public CustomSettingsCollection(Company company)
        {
            this.company = company;
            Variables variable = new Variables();
            Values = new CustomSettings(variable);
        }

        public void CopyFrom(CustomSettingsCollection customSettingsCollection)
        {
            if (customSettingsCollection == null) throw new ArgumentNullException(nameof(customSettingsCollection));

            foreach (var customSetting in customSettingsCollection.customSettings.Values)
            {
                CustomSetting cs;
                //if (customSetting.IsVariable)
                if (customSetting.ConnectorsCustomSetting is VariableCustomSetting)
                {
                    cs = AddVariableParameter(customSetting.Key);
                }
                else if (customSetting.Type == nameof(Secret))
                {
                    cs = AddSecretParameter(customSetting.StartDate, customSetting.Key, customSetting.AsString);
                }
                else
                {
                    cs = AddFixedParameter(customSetting.StartDate, customSetting.Key, customSetting.AsString);
                }
                cs.Description = customSetting.Description;
                cs.Enabled = customSetting.Enabled;
                cs.ExternalVisibility = customSetting.ExternalVisibility;
            }
        }

        internal CustomSetting Get(DateTime now, string key)
        {
            if (now == default(DateTime)) throw new ArgumentNullException(nameof(now));
            if (string.IsNullOrEmpty(key)) throw new ArgumentNullException(nameof(key));

            Values.Get(now, key);
            var result = customSettings[key];
            return result;
        }

        internal CustomSetting AddTextParameter(DateTime now, string key, string value, string user, string description, bool enabled)
        {
           return AddFixedParameter(now, key, value,user, description, enabled);
        }

        internal CustomSetting AddFixedParameter(DateTime now, string key, object value)
        {
            return AddFixedParameter(now, key, value, "None", "None", true);
        }

        internal CustomSetting AddFixedParameter(DateTime now, string key, string user, string description, bool enabled, bool canBeVisible)
        {
            if (now == default(DateTime)) throw new ArgumentNullException(nameof(now));
            if (string.IsNullOrWhiteSpace(key)) throw new ArgumentNullException(nameof(key));
            if (string.IsNullOrEmpty(user)) throw new ArgumentNullException(nameof(user));
            if (string.IsNullOrEmpty(description)) throw new ArgumentNullException(nameof(description));
            if (Values.Exist(key)) throw new GameEngineException($"CustomSettings already contains setting with key '{key}'");

            try
            {
                var setting = Values.AddFixedParameter(now, key);
                var myCustomSetting = new CustomSetting(setting);
                myCustomSetting.Who = user;
                myCustomSetting.Description = description;
                myCustomSetting.Enabled = enabled;
                myCustomSetting.ExternalVisibility = canBeVisible;

                this.customSettings.Add(myCustomSetting.Key, myCustomSetting);
                return myCustomSetting;
            }
            catch (ConnectorException ex)
            {
                throw new GameEngineException(ex.Message);
            }
        }

        internal CustomSetting AddFixedParameter(DateTime now, string key, object value, string user, string description, bool enabled)
        {
            return AddFixedParameter(now, key, value, user, description, enabled, false);
        }

        internal CustomSetting AddFixedParameter(DateTime now, string key, object value, string user, string description, bool enabled, bool canBeVisible)
        {
            if (value == null) throw new ArgumentNullException(nameof(value));
            if (now == default(DateTime)) throw new ArgumentNullException(nameof(now));
            if (string.IsNullOrWhiteSpace(key)) throw new ArgumentNullException(nameof(key));
            if (string.IsNullOrEmpty(user)) throw new ArgumentNullException(nameof(user));
            if (string.IsNullOrEmpty(description)) throw new ArgumentNullException(nameof(description));

            if (Values.Exist(key)) throw new GameEngineException($"CustomSettings already contains setting with key '{key}'");

            if (value is Secret && canBeVisible) throw new GameEngineException($"Secret value can't be visible");

            try
            {
                var setting = Values.AddFixedParameter(now, key, value);
                var myCustomSetting = new CustomSetting(setting);
                myCustomSetting.Who = user;
                myCustomSetting.Description = description;
                myCustomSetting.Enabled = enabled;
                myCustomSetting.ExternalVisibility = canBeVisible;

                this.customSettings.Add(myCustomSetting.Key, myCustomSetting);
                return myCustomSetting;
            }
            catch (ConnectorException ex)
            {
                throw new GameEngineException(ex.Message);
            }
        }

        internal CustomSetting AddVariableParameter(string key)
        {
            if (string.IsNullOrWhiteSpace(key)) throw new ArgumentNullException(nameof(key));

            var setting = Values.AddVariableParameter(key);
            var myCustomSetting = new CustomSetting(setting);
            this.customSettings.Add(myCustomSetting.Key, myCustomSetting);
            return myCustomSetting;
        }

        internal CustomSetting AddSecretParameter(DateTime now, string key, object value)
        {
            return AddSecretParameter(now, key, value, "None", "None", false);
        }

        internal CustomSetting AddSecretParameter(DateTime now, string key, object value, string user, string description, bool enabled)
        {
            return AddSecretParameter(now, key, value, user, description, enabled, false);
        }

        internal CustomSetting AddSecretParameter(DateTime now, string key, object value, string user, string description, bool enabled, bool visible)
        {
            if (value == null) throw new ArgumentNullException(nameof(value));
            if (now == default(DateTime)) throw new ArgumentNullException(nameof(now));
            if (string.IsNullOrWhiteSpace(key)) throw new ArgumentNullException(nameof(key));
            if (string.IsNullOrEmpty(user)) throw new ArgumentNullException(nameof(user));
            if (string.IsNullOrEmpty(description)) throw new ArgumentNullException(nameof(description));

            try
            {
                var setting = Values.AddFixedParameter(now, key, new Secret(value.ToString()));
                var myCustomSetting = new CustomSetting(setting);
                myCustomSetting.Who = user;
                myCustomSetting.Description = description;
                myCustomSetting.Enabled = enabled;
                myCustomSetting.ExternalVisibility = visible;

                this.customSettings.Add(myCustomSetting.Key, myCustomSetting);
                return myCustomSetting;
            }
            catch(ConnectorException ex)
            {
                throw new GameEngineException(ex.Message);
            }
        }

        internal CustomSetting ChangeValueStartingOnX(DateTime now, string key, object value, string user, DateTime dateToApplyTheChange, string description, bool enabled, bool canBeVisible)
        {
            if (value == null) throw new ArgumentException(nameof(value));
            if (dateToApplyTheChange == default(DateTime)) throw new ArgumentException(nameof(dateToApplyTheChange));
            if (string.IsNullOrEmpty(key)) throw new ArgumentException(nameof(key));
            if (string.IsNullOrEmpty(user)) throw new ArgumentException(nameof(user));
            if (string.IsNullOrEmpty(description)) throw new ArgumentException(nameof(description));
            if (dateToApplyTheChange < now) throw new GameEngineException($"{nameof(dateToApplyTheChange)} must be greater than {nameof(now)}");

            if (!customSettings.ContainsKey(key)) throw new GameEngineException($"{nameof(customSettings)} does not contain setting with key '{key}'");
            if (!Values.Exist(key)) throw new GameEngineException($"{nameof(Values)} does not contain setting with key '{key}'");

            Values.ChangeValueStartingOn(dateToApplyTheChange, key, value, user);
            var result = Get(now, key);
            result.Description = description;
            result.Enabled = enabled;
            result.ExternalVisibility = canBeVisible;

            if (result.ConnectorsCustomSetting is TownAttributeCustomSetting) RefreshAttributesByActor();

            return result;
        }

        internal CustomSetting ChangeValueStartingOnX(DateTime now, string key, object value, string user, DateTime dateToApplyTheChange, string description, bool enabled)
        {
            if (value == null) throw new ArgumentException(nameof(value));
            if (dateToApplyTheChange == default(DateTime)) throw new ArgumentException(nameof(dateToApplyTheChange));
            if (string.IsNullOrEmpty(key)) throw new ArgumentException(nameof(key));
            if (string.IsNullOrEmpty(user)) throw new ArgumentException(nameof(user));
            if (string.IsNullOrEmpty(description)) throw new ArgumentException(nameof(description));
            if (dateToApplyTheChange < now) throw new GameEngineException($"{nameof(dateToApplyTheChange)} must be greater than {nameof(now)}");

            if (!customSettings.ContainsKey(key)) throw new GameEngineException($"{nameof(customSettings)} does not contain setting with key '{key}'");
            if (!Values.Exist(key)) throw new GameEngineException($"{nameof(Values)} does not contain setting with key '{key}'");

            Values.ChangeValueStartingOn(dateToApplyTheChange, key, value, user);
            var result = Get(now, key);
            result.Description = description;
            result.Enabled = enabled;

            if (result.ConnectorsCustomSetting is TownAttributeCustomSetting) RefreshAttributesByActor();

            return result;
        }

        internal CustomSetting ChangeTextValueStartingOnX(DateTime now, string key, string value, string user, DateTime dateToApplyTheChange, string description, bool enabled)
        {
            return ChangeValueStartingOnX(now, key, value, user, dateToApplyTheChange, description, enabled, false);
        }

        internal CustomSetting ChangeTextValueStartingOnX(DateTime now, string key, string value, string user, DateTime dateToApplyTheChange, string description,bool enabled, bool canBeVisible)
        {
            return ChangeValueStartingOnX(now, key, value, user, dateToApplyTheChange, description, enabled, canBeVisible);
        }

        internal CustomSetting ChangeAttributeValueStartingOnX(DateTime now, string key, string attributeValue, string user, DateTime dateToApplyTheChange, string description, bool enabled, bool canBeVisible)
        {
            if (!TryToGetAttribute(attributeValue, out string actorKey, out string attributeKey)) throw new GameEngineException($"Invalid {nameof(attributeValue)} format.");

            if (!TownSettings.TryToGetActorUrl(actorKey, out _)) throw new GameEngineException($"There is no actor '{actorKey}' found for attribute '{attributeKey}'");

            return ChangeValueStartingOnX(now, key, attributeValue, user, dateToApplyTheChange, description, enabled, canBeVisible);
        }

        internal CustomSetting ChangeAttributeValueStartingOnX(DateTime now, string key, string attributeValue, string user, DateTime dateToApplyTheChange, string description, bool enabled)
        {
            if (!TryToGetAttribute(attributeValue, out string actorKey, out string attributeKey)) throw new GameEngineException($"Invalid {nameof(attributeValue)} format.");

            if (!TownSettings.TryToGetActorUrl(actorKey, out _)) throw new GameEngineException($"There is no actor '{actorKey}' found for attribute '{attributeKey}'");

            return ChangeValueStartingOnX(now, key, attributeValue, user, dateToApplyTheChange, description, enabled);
        }

        [Obsolete]
        internal CustomSetting ChangeValueStartingOn(DateTime now, string key, object value, string user, DateTime dateToApplyTheChange)
        {
            if (value == null) throw new ArgumentException(nameof(value));
            if (dateToApplyTheChange == default(DateTime)) throw new ArgumentException(nameof(dateToApplyTheChange));
            if (string.IsNullOrEmpty(key)) throw new ArgumentException(nameof(key));

            if (! customSettings.ContainsKey(key)) throw new GameEngineException($"{nameof(customSettings)} does not contain setting with key '{key}'");
            if (!Values.Exist(key)) throw new GameEngineException($"{nameof(Values)} does not contain setting with key '{key}'");

            Values.ChangeValueStartingOn(dateToApplyTheChange, key, value, user);
            var result = Get(now, key);
            return result;
        }
        [Obsolete]
        internal CustomSetting ChangeValueStartingOn(bool itIsThePresent, DateTime dateToApplyTheChange, string key, object value, string employeeName)
        {
            if (value == null) throw new ArgumentException(nameof(value));
            if (dateToApplyTheChange == default(DateTime)) throw new ArgumentException(nameof(dateToApplyTheChange));
            if (string.IsNullOrWhiteSpace(key)) throw new ArgumentException(nameof(key));
            if (!customSettings.ContainsKey(key)) throw new GameEngineException($"{nameof(customSettings)} does not contain setting with key '{key}'");

            Values.ChangeValueStartingOn(dateToApplyTheChange, key, value, employeeName);
            var result = customSettings[key];
            if (itIsThePresent && (Integration.UseKafka || Integration.UseKafkaForAuto))
            {
                var currentTenant = company.System.Tenants.CurrentTenant;
                var currentStore = company.Sales.CurrentStore;
                CustomSettingMessage msg;
                if (value.GetType() == typeof(int))
                {
                    msg = new CustomSettingMessage(currentTenant.Id, currentStore.Id, currentTenant.Name, currentStore.Alias, key, CustomSettingType.Integer, dateToApplyTheChange.ToString("MM/dd/yyyy HH:mm:ss"), employeeName, value.ToString());
                }
                else if (value.GetType() == typeof(bool))
                {
                    msg = new CustomSettingMessage(currentTenant.Id, currentStore.Id, currentTenant.Name, currentStore.Alias, key, CustomSettingType.Boolean, dateToApplyTheChange.ToString("MM/dd/yyyy HH:mm:ss"), employeeName, value.ToString());
                }
                else
                {
                    msg = new CustomSettingMessage(currentTenant.Id, currentStore.Id, currentTenant.Name, currentStore.Alias, key, CustomSettingType.String, dateToApplyTheChange.ToString("MM/dd/yyyy HH:mm:ss"), employeeName, value.ToString());
                }
                msg.Description = result.Description;
                Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForCustomSettings, msg);
            }
            
            return result;
        }

        internal void DeleteAttributeX(DateTime now, string key, string user, string description, bool enabled)
        {
            if (string.IsNullOrWhiteSpace(key)) throw new ArgumentException(nameof(key));
            if (string.IsNullOrEmpty(user)) throw new ArgumentException(nameof(user));
            if (string.IsNullOrEmpty(description)) throw new ArgumentException(nameof(description));
            if (!customSettings.ContainsKey(key)) throw new GameEngineException($"{nameof(customSettings)} does not contain setting with key '{key}'");
            
            var result = customSettings[key];
            if(result.ConnectorsCustomSetting is not TownAttributeCustomSetting townAttributeCustomSetting) throw new GameEngineException($"CustomSetting type is not a {nameof(TownAttributeCustomSetting)}.");
            townAttributeCustomSetting.ClearAttibute();
            result.Description = description;
            result.Enabled = enabled;

            HasTownAttributes = AnyTownAttributes();
            RefreshAttributesByActor();

            company.System.DriverManagers.DeleteAttributeX(now, key);
        }

        internal CustomSetting ChangeSecretStartingOnX(DateTime now, string key, object value, string user, DateTime dateToApplyTheChange, string description, bool enabled)
        {
            if (value == null) throw new ArgumentException(nameof(value));
            if (now == default(DateTime)) throw new ArgumentException(nameof(dateToApplyTheChange));
            if (dateToApplyTheChange == default(DateTime)) throw new ArgumentException(nameof(dateToApplyTheChange));
            if (string.IsNullOrWhiteSpace(key)) throw new ArgumentException(nameof(key));
            if (string.IsNullOrEmpty(user)) throw new ArgumentException(nameof(user));
            if (string.IsNullOrEmpty(description)) throw new ArgumentException(nameof(description));
            if (!customSettings.ContainsKey(key)) throw new GameEngineException($"{nameof(customSettings)} does not contain setting with key '{key}'");

            Values.ChangeValueStartingOn(dateToApplyTheChange, key, new Secret(value.ToString()), user);
            //var result = customSettings[key];
            var result = Get(now, key);
            result.Description = description;
            result.Enabled = enabled;
            return result;
        }

        [Obsolete]
        internal CustomSetting ChangeSecretStartingOn(DateTime now, string key, object value, string user, DateTime dateToApplyTheChange)
        {
            if (value == null) throw new ArgumentException(nameof(value));
            if (now == default(DateTime)) throw new ArgumentException(nameof(dateToApplyTheChange));
            if (dateToApplyTheChange == default(DateTime)) throw new ArgumentException(nameof(dateToApplyTheChange));
            if (string.IsNullOrWhiteSpace(key)) throw new ArgumentException(nameof(key));
            if (!customSettings.ContainsKey(key)) throw new GameEngineException($"{nameof(customSettings)} does not contain setting with key '{key}'");

            Values.ChangeValueStartingOn(dateToApplyTheChange, key, new Secret(value.ToString()), user);
            //var result = customSettings[key];
            var result = Get(now, key);
            return result;
        }

        internal CustomSetting ChangeSecretStartingOn(bool itIsThePresent, DateTime dateToApplyTheChange, string key, object value, string employeeName)
        {
            if (value == null) throw new ArgumentException(nameof(value));
            if (dateToApplyTheChange == default(DateTime)) throw new ArgumentException(nameof(dateToApplyTheChange));
            if (string.IsNullOrWhiteSpace(key)) throw new ArgumentException(nameof(key));
            if (!customSettings.ContainsKey(key)) throw new GameEngineException($"{nameof(customSettings)} does not contain setting with key '{key}'");

            Values.ChangeValueStartingOn(dateToApplyTheChange, key, new Secret(value.ToString()), employeeName);
            var result = customSettings[key];
            if (itIsThePresent && (Integration.UseKafka || Integration.UseKafkaForAuto))
            {
                var currentTenant = company.System.Tenants.CurrentTenant;
                var currentStore = company.Sales.CurrentStore;
                var msg = new CustomSettingMessage(currentTenant.Id, currentStore.Id, currentTenant.Name, currentStore.Alias, key, CustomSettingType.Secret, dateToApplyTheChange.ToString("MM/dd/yyyy HH:mm:ss"), employeeName, value.ToString());
                msg.Description = result.Description;
                Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForCustomSettings, msg);
            }
            return result;
        }

        internal CustomSetting UpdateDescription(bool itIsThePresent, string key, string description)
        {
            if (string.IsNullOrWhiteSpace(key)) throw new ArgumentException(nameof(key));
            if (string.IsNullOrWhiteSpace(description)) throw new ArgumentException(nameof(description));
            if (!customSettings.ContainsKey(key)) throw new GameEngineException($"{nameof(customSettings)} does not contain setting with key '{key}'");

            var result = customSettings[key];
            result.Description = description;
            if (itIsThePresent && (Integration.UseKafka || Integration.UseKafkaForAuto))
            {
                var currentTenant = company.System.Tenants.CurrentTenant;
                var currentStore = company.Sales.CurrentStore;
                var msg = new CustomSettingMessage(currentTenant.Id, currentStore.Id, currentTenant.Name, currentStore.Alias, key, description);
                Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForCustomSettings, msg);
            }
            return result;
        }

        internal CustomSetting Enable(bool itIsThePresent, string key)
        {
            if (string.IsNullOrWhiteSpace(key)) throw new ArgumentException(nameof(key));
            if (!customSettings.ContainsKey(key)) throw new GameEngineException($"{nameof(customSettings)} does not contain setting with key '{key}'");

            var result = customSettings[key];
            result.Enabled = true;
            if (itIsThePresent && (Integration.UseKafka || Integration.UseKafkaForAuto))
            {
                var currentTenant = company.System.Tenants.CurrentTenant;
                var currentStore = company.Sales.CurrentStore;
                var msg = new CustomSettingMessage(currentTenant.Id, currentStore.Id, currentTenant.Name, currentStore.Alias, key, true);
                Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForCustomSettings, msg);
            }
            return result;
        }

        internal CustomSetting Disable(bool itIsThePresent, string key)
        {
            if (string.IsNullOrWhiteSpace(key)) throw new ArgumentException(nameof(key));
            if (!customSettings.ContainsKey(key)) throw new GameEngineException($"{nameof(customSettings)} does not contain setting with key '{key}'");

            var result = customSettings[key];
            result.Enabled = false;
            if (itIsThePresent && (Integration.UseKafka || Integration.UseKafkaForAuto))
            {
                var currentTenant = company.System.Tenants.CurrentTenant;
                var currentStore = company.Sales.CurrentStore;
                var msg = new CustomSettingMessage(currentTenant.Id, currentStore.Id, currentTenant.Name, currentStore.Alias, key, false);
                Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForCustomSettings, msg);
            }
            return result;
        }

        internal CustomSetting ChangeVisibility(string key, bool visible)
        {
            if (string.IsNullOrWhiteSpace(key)) throw new ArgumentException(nameof(key));
            if (!customSettings.ContainsKey(key)) throw new GameEngineException($"{nameof(customSettings)} does not contain setting with key '{key}'");

            var result = customSettings[key];
            if (result.Type == nameof(Secret)) throw new GameEngineException($"Secret value can't be visible");

            result.ExternalVisibility = visible;            
            return result;
        }

        internal bool IsValidNextDateToChange(DateTime now, DateTime date)
        {
            return now < date;
        }

        public IEnumerator<CustomSetting> GetEnumerator()
        {
            return customSettings.Values.GetEnumerator();
        }

        IEnumerator IEnumerable.GetEnumerator()
        {
            return customSettings.GetEnumerator();
        }

        internal static char ATTRIBUTE_SPLITTER_CHAR = '$';
        public static bool TryToGetAttribute(string actorAttributeKey, out string actorKey, out string attributeKey)
        {
            attributeKey = actorKey = string.Empty;

            if (string.IsNullOrWhiteSpace(actorAttributeKey)) throw new ArgumentNullException(nameof(actorAttributeKey));
            if (!actorAttributeKey.Contains(ATTRIBUTE_SPLITTER_CHAR)) return false;

            int position = actorAttributeKey.IndexOf(ATTRIBUTE_SPLITTER_CHAR);
            if (position == -1) return false;

            actorKey = actorAttributeKey.Substring(0, position);
            if (string.IsNullOrWhiteSpace(actorKey)) return false;

            position = actorKey.Length + 1;
            attributeKey = actorAttributeKey.Substring(position);
            if (string.IsNullOrWhiteSpace(attributeKey)) return false;

            return true;
        }

        internal CustomSetting AddAttribute(DateTime now, string key, string user, string description, bool enabled)
        {
            return AddAttribute(now, key, user, description, enabled, false);
        }

        internal CustomSetting AddAttribute(DateTime now, string key, string user, string description, bool enabled, bool canBeVisible)
        {
            if (string.IsNullOrWhiteSpace(key)) throw new ArgumentNullException(nameof(key));
            if (this.customSettings.ContainsKey(key)) throw new GameEngineException($"CustomSettings already contains setting with key '{key}'");

            var attributeCustomSetting = Values.AddAttributeParameter(now, key);

            var myCustomSetting = new CustomSetting(attributeCustomSetting);
            myCustomSetting.Who = user;
            myCustomSetting.Description = description;
            myCustomSetting.Enabled = enabled;
            myCustomSetting.ExternalVisibility = canBeVisible;

            this.customSettings.Add(myCustomSetting.Key, myCustomSetting);

            HasTownAttributes = AnyTownAttributes();
            RefreshAttributesByActor();

            return myCustomSetting;
        }

        private Dictionary<string, List<string>> listOfAttributesByActor = new Dictionary<string, List<string>>();

        private void RefreshAttributesByActor()
        {
            listOfAttributesByActor.Clear();
            var attributesSettings = GetAttributes();
            foreach (var attributeSetting in attributesSettings)
            {
                if (attributeSetting.ConnectorsCustomSetting is not TownAttributeCustomSetting townAttribute) throw new GameEngineException($"CustomSetting with key {attributeSetting.Key} is empty.");

                string actorKey = townAttribute.ActorKey;
                if (string.IsNullOrWhiteSpace(actorKey)) throw new GameEngineException($"ActorKey is empty in {attributeSetting.Key}.");

                if (TryToGetAttribute(actorKey, out string actor, out _))
                {
                    if (!listOfAttributesByActor.TryGetValue(actor, out List<string> attributes))
                    {
                        attributes = new List<string>();
                        listOfAttributesByActor.Add(actor, attributes);
                    }
                    attributes.Add(attributeSetting.Key);
                }
                else
                {
                    throw new GameEngineException($"Invalid {nameof(attributeSetting.Key)}.");
                }
            }
        }

        private List<AttributeItem> results = new List<AttributeItem>();

        internal async Task<List<AttributeItem>>  ThreadUnsafeGatherAttributesAsync(string playerId)
        {
            //Drivers: usar el playerId Somehow
            if (string.IsNullOrWhiteSpace(playerId)) throw new ArgumentNullException(nameof(playerId));

            results.Clear();
            foreach (var actor in listOfAttributesByActor.Keys)
            {
                string actorUrl = GetActorUrl(actor);
                string url = $"{actorUrl}/api/gatherAttribute?playerId={playerId}";
                List<string> attibuteSettingsString = listOfAttributesByActor[actor];

                var restClientToCashier = HttpRestClientConfiguration.GetInstance();
                var result = await restClientToCashier.PostAsync(url, attibuteSettingsString);

                AttributeBuilder attributeValues;
                if (result is OkObjectResult)
                {
                    OkObjectResult o = (OkObjectResult)result;
                    string json = o.Value.ToString();
                    attributeValues = JsonConvert.DeserializeObject<AttributeBuilder>(json);
                    if (attributeValues == null) throw new GameEngineException($@"Error:{attributeValues} can not null");
                    if (attributeValues.Attributes.Count == 0) throw new GameEngineException($@"Error:{attributeValues.Attributes} can not null");
                }
                else
                {
                    attributeValues = new AttributeBuilder();
                    foreach (var attributeSetting in attibuteSettingsString)
                    {
                        //Drivers: Tal vez si la peticion al actor falla, se debe agregar un mensaje de error en el log y se le setting el valor a string.Empty
                        attributeValues.AddAttribute(attributeSetting, string.Empty);
                    }
                }
                results.AddRange(attributeValues.Attributes);
            }

            return results;
        }
        private string GetActorUrl(string actorKey)
        {
            bool urlFound = TownSettings.TryToGetActorUrl(actorKey, out string urlResult);
            if (!urlFound) throw new GameEngineException($"There is no url for actor {actorKey} defined.");

            return urlResult;
            }

        internal IEnumerable<CustomSetting> GetAttributes()
        {
            List<CustomSetting> attributesSettings = new List<CustomSetting>();
            foreach (var customSetting in customSettings.Values)
            {
                if (customSetting.ConnectorsCustomSetting is TownAttributeCustomSetting townAttributeCustomSetting)
                {
                    if (!townAttributeCustomSetting.IsActorKeyEmpty) attributesSettings.Add(customSetting);
                }
            }
            return attributesSettings;
        }

        internal bool HasTownAttributes { get; private set; }

        internal bool AnyTownAttributes()
        {
            foreach (var customSetting in customSettings.Values)
            {
                if (customSetting.ConnectorsCustomSetting is TownAttributeCustomSetting)
                {
                    return true;
                }
            }
            return false;
        }

        internal bool HasTownAttribute(string key)
        {
            if (string.IsNullOrWhiteSpace(key)) throw new ArgumentNullException(nameof(key));

            foreach (var customSetting in customSettings.Values)
            {
                if (customSetting.ConnectorsCustomSetting is TownAttributeCustomSetting townCustomSetting)
                {
                    if (townCustomSetting.Key == key) return true;
                }
            }
            return false;
        }

        internal void RemoveAttribute(DateTime now, string key)
        {
            if (now == default(DateTime)) throw new ArgumentNullException(nameof(now));
            if (string.IsNullOrWhiteSpace(key)) throw new ArgumentNullException(nameof(key));
            if (!customSettings.ContainsKey(key)) throw new GameEngineException($"{nameof(customSettings)} does not contain setting with key '{key}'");

            customSettings.Remove(key);
            Values.RemoveAttribute(now, key);

            HasTownAttributes = AnyTownAttributes();
            RefreshAttributesByActor();
        }

        [Puppet]
        public class AttributeBuilder : Objeto
        {
            public List<AttributeItem> Attributes { get; set; } = new List<AttributeItem>();

            public void AddAttribute(string attribute, string value)
            {
                Attributes.Add(new AttributeItem { AttributeKey = attribute, AttributeValue = value });
            }
        }

        [Puppet]
        public class AttributeItem : Objeto
        {
            public string AttributeKey { get; set; }
            public string AttributeValue { get; set; }
        }
    }

    public class CustomSetting : Objeto
    {
        internal string Key => customSetting.Key;
        internal string AsString => customSetting.AsString;
        internal string Type
        {
            get
            {
                if (string.Equals(customSetting.Type, "Hilera", StringComparison.OrdinalIgnoreCase)) return nameof(String);
                return customSetting.Type;
            }
        }

        internal DateTime StartDate => customSetting.Now;
        internal DateTime NextDateToChange => customSetting.NextDateToChange();
        internal string NextValue => customSetting.NextValue().ToString();
        internal bool HasScheduledChange => customSetting.HasScheduledChange();
        internal bool Enabled
        {
            get
            {
                return customSetting.Enable;
            }
            set
            {
                customSetting.Enable = value;
            }
        }
        internal bool ExternalVisibility
        {
            get
            {
                return customSetting.ExternalVisibility;
            }
            set
            {
                customSetting.ExternalVisibility = value;
            }
        }
        internal string Description
        {
            get
            {
                return customSetting.Description;
            }
            set
            {
                customSetting.Description = value;
            }
        }
        internal string Who
        {
            get
            {
                return customSetting.Who;
            }
            set
            {
                customSetting.Who = value;
            }
        }

        private const int MAX_NUMBER_OF_ENTRIES = 5;
        internal Logs.Log Log { get; }
        private CustomSettings.CustomSetting customSetting;

        public CustomSetting(CustomSettings.CustomSetting customSetting)
        {
            if (customSetting == null) throw new ArgumentNullException(nameof(customSetting));

            this.customSetting = customSetting;
            Log = new Logs.Log(customSetting.Key, MAX_NUMBER_OF_ENTRIES);
        }

        internal CustomSettings.CustomSetting ConnectorsCustomSetting => customSetting;

        public void AddAnnotation(string message, string who, DateTime now)
        {
            if (string.IsNullOrWhiteSpace(message)) throw new ArgumentNullException(nameof(message));
            if (string.IsNullOrWhiteSpace(who)) throw new ArgumentNullException(nameof(who));

            Log.AddEntry(now, who, $"{who} set {message} at {now.ToString("dd/MM/yyyy hh:mm tt")}.");
        }

        public void AddAnnotation(string message, string who, DateTime now, DateTime dateToApply)
        {
            if (string.IsNullOrWhiteSpace(message)) throw new ArgumentNullException(nameof(message));
            if (string.IsNullOrWhiteSpace(who)) throw new ArgumentNullException(nameof(who));

            Log.AddEntry(now, who, $"{who} set {message} at {now.ToString("dd/MM/yyyy hh:mm tt")}. It will be applied at {dateToApply.ToString("dd/MM/yyyy hh:mm tt")}.");
        }
    }

}
