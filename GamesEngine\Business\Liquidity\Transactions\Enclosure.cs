﻿using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;

namespace GamesEngine.Business.Liquidity.Transactions
{
    internal abstract class Enclosure : Objeto
    {
        private readonly Transaction transaction;
        private readonly List<string> notes;
        private DateTime scheduledDate;

        internal Enclosure(Transaction transaction, int containerVersion)
        {
            if (transaction == null) throw new ArgumentNullException(nameof(transaction));
            if (containerVersion < 0) throw new ArgumentNullException(nameof(containerVersion));
            
            this.transaction = transaction;
            notes = new List<string>();
            scheduledDate = DateTime.MinValue;
            ContainerVersion = containerVersion;
        }

        protected int ContainerVersion { get; private set; }

        internal Transaction Transaction => transaction;

        internal DateTime ScheduledDate
        {
            get
            {
                if (scheduledDate == DateTime.MinValue) throw new GameEngineException("There is no scheduled date set.");
                return scheduledDate;
            }
        }

        internal bool HasScheduledDate => scheduledDate != DateTime.MinValue;

        internal IEnumerable<string> Notes => notes;

        internal void Reschedule(DateTime now, DateTime newDate)
        {
            if (newDate == DateTime.MinValue) throw new ArgumentNullException(nameof(newDate));
            if (newDate <= now) throw new ArgumentException("The new scheduled date must be in the future compared to now.", nameof(newDate));
            scheduledDate = newDate;
        }

        protected void AddNote(string note)
        {
            if (string.IsNullOrWhiteSpace(note)) throw new ArgumentNullException(nameof(note));
            notes.Add(note);
        }
    }
}
