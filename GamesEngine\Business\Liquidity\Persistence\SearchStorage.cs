﻿using Elastic.Clients.Elasticsearch;
using Elastic.Clients.Elasticsearch.Mapping;
using Elastic.Clients.Elasticsearch.QueryDsl;
using Elastic.Clients.Elasticsearch.Aggregations;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using static GamesEngine.Business.Liquidity.Persistence.DataModel;
using Puppeteer.EventSourcing;
using GamesEngine.Settings;

namespace GamesEngine.Business.Liquidity.Persistence
{
    public class SearchStorage : ISearchStorage
    {
        private readonly ElasticsearchClient _client;

        private const string BaseDepositTableName = "deposit";
        private const string BaseJarTableName = "jar";
        private const string BaseTankTableName = "tank";
        private const string BaseTankerTableName = "tanker";
        private const string BaseJarDetailTableName = "jardetail";
        private const string BaseTankDetailTableName = "tankdetail";
        private const string BaseTankerDetailTableName = "tankerdetail";
        private const string BaseWithdrawalTableName = "withdrawal";
        private const string BaseBottleTableName = "bottle";
        private const string BaseDispenserTableName = "dispenser";
        private const string BaseBottleDetailTableName = "bottledetail";
        private const string BaseDispenserDetailTableName = "dispenserdetail";
        private const string BaseInvoicePaymentTableName = "invoicepayment";

        public SearchStorage(string elasticsearchUri)
        {
            var settings = new ElasticsearchClientSettings(new Uri(elasticsearchUri))
                .ThrowExceptions();
            _client = new ElasticsearchClient(settings);
            
            try
            {
                var pingResponse = _client.Ping();
                if (!pingResponse.IsSuccess())
                {
                    var ex = new Exception($"Elasticsearch connection failed during initialization: {pingResponse.DebugInformation}");
                    Loggers.GetIntance().SearchEngine.Error(ex.Message, ex);
                    ErrorsSender.Send(ex, $"{nameof(SearchStorage)} initialization", $"elasticsearchUri:{elasticsearchUri}");
                }
            }
            catch (Exception ex)
            {
                var connectionEx = new Exception($"Failed to connect to Elasticsearch at {elasticsearchUri}: {ex.Message}", ex);
                Loggers.GetIntance().SearchEngine.Error(connectionEx.Message, connectionEx);
                ErrorsSender.Send(connectionEx, $"{nameof(SearchStorage)} initialization", $"elasticsearchUri:{elasticsearchUri}");
            }
        }

        private string GetDynamicIndexName(string baseName, string kind)
        {
            var sanitizedKind = new string(kind.Where(c => char.IsLetterOrDigit(c) || c == '_').ToArray()).ToLowerInvariant();
            return $"{baseName}_{sanitizedKind}";
        }

        public void CreateTablesIfNotExists(string kind)
        {
            var mappings = new Dictionary<string, TypeMapping>
            {
                { BaseDepositTableName, new TypeMapping { Properties = new Properties {
                    { "Id", new LongNumberProperty() },
                    { "DocumentNumber", new KeywordProperty() },
                    { "Amount", new DoubleNumberProperty() },
                    { "Date", new DateProperty() },
                    { "StoreId", new IntegerNumberProperty() },
                    { "AccountNumber", new KeywordProperty() },
                    { "DomainId", new IntegerNumberProperty() },
                    { "Address", new KeywordProperty() },
                    { "Created", new DateProperty() },
                    { "State", new KeywordProperty() },
                    { "ExternalId", new KeywordProperty() },
                    { "Rate", new DoubleNumberProperty() }
                } } },
                { BaseJarTableName, new TypeMapping { Properties = new Properties {
                    { "Version", new LongNumberProperty() },
                    { "Description", new KeywordProperty() },
                    { "Created", new DateProperty() }
                } } },
                { BaseJarDetailTableName, new TypeMapping { Properties = new Properties {
                    { "JarVersion", new LongNumberProperty() },
                    { "DepositId", new LongNumberProperty() },
                    { "Created", new DateProperty() }
                } } },
                { BaseTankTableName, new TypeMapping { Properties = new Properties {
                    { "Id", new LongNumberProperty() },
                    { "Name", new KeywordProperty() },
                    { "Description", new KeywordProperty() },
                    { "Created", new DateProperty() },
                    { "Version", new IntegerNumberProperty() }
                } } },
                { BaseTankDetailTableName, new TypeMapping { Properties = new Properties {
                    { "TankId", new LongNumberProperty() },
                    { "DepositId", new LongNumberProperty() },
                    { "Created", new DateProperty() }
                } } },
                { BaseTankerTableName, new TypeMapping { Properties = new Properties {
                    { "Id", new LongNumberProperty() },
                    { "Name", new KeywordProperty() },
                    { "Description", new KeywordProperty() },
                    { "Created", new DateProperty() },
                    { "Version", new IntegerNumberProperty() }
                } } },
                { BaseTankerDetailTableName, new TypeMapping { Properties = new Properties {
                    { "TankerId", new LongNumberProperty() },
                    { "DepositId", new LongNumberProperty() },
                    { "Created", new DateProperty() }
                } } },
                { BaseWithdrawalTableName, new TypeMapping { Properties = new Properties {
                    { "Id", new LongNumberProperty() },
                    { "DocumentNumber", new KeywordProperty() },
                    { "Amount", new DoubleNumberProperty() },
                    { "Date", new DateProperty() },
                    { "StoreId", new IntegerNumberProperty() },
                    { "AccountNumber", new KeywordProperty() },
                    { "DomainId", new IntegerNumberProperty() },
                    { "Address", new KeywordProperty() },
                    { "Created", new DateProperty() },
                    { "State", new KeywordProperty() },
                    { "ExternalId", new KeywordProperty() }
                } } },
                { BaseBottleTableName, new TypeMapping { Properties = new Properties {
                    { "Id", new LongNumberProperty() },
                    { "Description", new KeywordProperty() },
                    { "Created", new DateProperty() }
                } } },
                { BaseDispenserTableName, new TypeMapping { Properties = new Properties {
                    { "Id", new LongNumberProperty() },
                    { "Description", new KeywordProperty() },
                    { "Created", new DateProperty() }
                } } },
                { BaseBottleDetailTableName, new TypeMapping { Properties = new Properties {
                    { "WithdrawalId", new LongNumberProperty() },
                    { "BottleId", new LongNumberProperty() },
                    { "Created", new DateProperty() }
                } } },
                { BaseDispenserDetailTableName, new TypeMapping { Properties = new Properties {
                    { "WithdrawalId", new LongNumberProperty() },
                    { "DispenserId", new LongNumberProperty() },
                    { "Created", new DateProperty() }
                } } },
                { BaseInvoicePaymentTableName, new TypeMapping { Properties = new Properties {
                    { "invoiceId", new KeywordProperty() },
                    { "destinationAddress", new KeywordProperty() },
                    { "externalAtAddress", new KeywordProperty() },
                    { "paidAmount", new DoubleNumberProperty() },
                    { "paidAt", new DateProperty() }
                } } }
            };

            foreach (var baseName in mappings.Keys)
            {
                string indexName = GetDynamicIndexName(baseName, kind);

                var createResponse = _client.Indices.Create(indexName, c => c
                    .Mappings(m => m.Properties(mappings[baseName].Properties)));

                if (!createResponse.IsSuccess() && createResponse.ElasticsearchServerError?.Error.Type != "resource_already_exists_exception")
                {
                    var ex = new Exception($"Failed to create index {indexName}: {createResponse.DebugInformation}");
                    Loggers.GetIntance().SearchEngine.Error(ex.Message, ex);
                    ErrorsSender.Send(ex, $"{nameof(CreateTablesIfNotExists)} index creation", $"indexName:{indexName}");
                    throw ex;
                }
            }
        }

        public void DropAllTables(string kind)
        {
            var baseIndexNames = new List<string>
            {
                BaseDepositTableName,
                BaseJarTableName,
                BaseTankTableName,
                BaseTankerTableName,
                BaseJarDetailTableName,
                BaseTankDetailTableName,
                BaseTankerDetailTableName,
                BaseWithdrawalTableName,
                BaseBottleTableName,
                BaseDispenserTableName,
                BaseBottleDetailTableName,
                BaseDispenserDetailTableName,
                BaseInvoicePaymentTableName
            };

            foreach (var baseName in baseIndexNames)
            {
                string indexName = GetDynamicIndexName(baseName, kind);
                var deleteResponse = _client.Indices.Delete(indexName, d => d.IgnoreUnavailable(true));
                if (!deleteResponse.IsSuccess())
                {
                    var ex = new Exception($"Failed to delete index {indexName}: {deleteResponse.DebugInformation}");
                    Loggers.GetIntance().SearchEngine.Error(ex.Message, ex);
                    ErrorsSender.Send(ex, $"{nameof(DropAllTables)} index deletion", $"indexName:{indexName}");
                    throw ex;
                }
            }
        }

        public void CreateDeposit(string kind, Deposit deposit)
        {
            string depositIndex = GetDynamicIndexName(BaseDepositTableName, kind);
            var esDeposit = new EsDeposit
            {
                Id = deposit.Id,
                DocumentNumber = deposit.DocumentNumber,
                Amount = (double)deposit.Amount,
                Date = deposit.Date,
                StoreId = deposit.StoreId,
                AccountNumber = deposit.AccountNumber,
                DomainId = deposit.DomainId,
                Address = deposit.Address,
                Created = deposit.Created,
                State = deposit.State,
                ExternalId = deposit.ExternalId,
                Rate = (double)deposit.Rate
            };
            var indexResponse = _client.Index(esDeposit, i => i.Index(depositIndex).Id(deposit.Id));
            if (!indexResponse.IsSuccess())
            {
                var ex = new Exception($"Failed to index {depositIndex}: {indexResponse.DebugInformation} Data:{esDeposit}");
                Loggers.GetIntance().SearchEngine.Error(ex.Message, ex);
                ErrorsSender.Send(ex, $"{nameof(CreateDeposit)} deposit creation", $"indexName:{depositIndex}, data:{esDeposit}");
                throw ex;
            }
        }

        public void CreateJar(string kind, long version, string description, DateTime created, long? previousJar)
        {
            string jarIndex = GetDynamicIndexName(BaseJarTableName, kind);
            var esJar = new EsJar
            {
                Version = version,
                Description = description,
                Created = created
            };
            var indexResponse = _client.Index(esJar, i => i.Index(jarIndex).Id(version));
            if (!indexResponse.IsSuccess())
            {
                var ex = new Exception($"Failed to index {jarIndex}: {indexResponse.DebugInformation} Data:{esJar}");
                Loggers.GetIntance().SearchEngine.Error(ex.Message, ex);
                ErrorsSender.Send(ex, $"{nameof(CreateJar)} jar creation", $"indexName:{jarIndex}, data:{esJar}");
                throw ex;
            }
        }

        public void CreateTank(string kind, Tank tank)
        {
            string tankIndex = GetDynamicIndexName(BaseTankTableName, kind);
            var esTank = new EsTank
            {
                Id = tank.Id,
                Name = tank.Name,
                Description = tank.Description,
                Created = tank.Created,
                Version = tank.Version
            };
            var indexResponse = _client.Index(esTank, i => i.Index(tankIndex).Id(tank.Id));
            if (!indexResponse.IsSuccess())
            {
                var ex = new Exception($"Failed to index {tankIndex}: {indexResponse.DebugInformation} Data:{esTank}");
                Loggers.GetIntance().SearchEngine.Error(ex.Message, ex);
                ErrorsSender.Send(ex, $"{nameof(CreateTank)} tank creation", $"indexName:{tankIndex}, data:{esTank}");
                throw ex;
            }
        }

        public void CreateTanker(string kind, Tanker tanker)
        {
            string tankerIndex = GetDynamicIndexName(BaseTankerTableName, kind);
            var esTanker = new EsTanker
            {
                Id = tanker.Id,
                Name = tanker.Name,
                Description = tanker.Description,
                Created = tanker.Created,
                Version = tanker.Version
            };
            var indexResponse = _client.Index(esTanker, i => i.Index(tankerIndex).Id(tanker.Id));
            if (!indexResponse.IsSuccess())
            {
                var ex = new Exception($"Failed to index {tankerIndex}: {indexResponse.DebugInformation} Data:{esTanker}");
                Loggers.GetIntance().SearchEngine.Error(ex.Message, ex);
                ErrorsSender.Send(ex, $"{nameof(CreateTanker)} tanker creation", $"indexName:{tankerIndex}, data:{esTanker}");
                throw ex;
            }
        }

        public void UpdateTank(string kind, long tankId, string propertyName, string newValue)
        {
            string indexName = GetDynamicIndexName(BaseTankTableName, kind);
            var response = _client.Update<EsTank, object>(indexName, tankId, u => u
                .Doc(new Dictionary<string, object> { { propertyName.ToLower(), newValue } })
                .Refresh(Refresh.True));

            if (!response.IsSuccess())
            {
                var ex = new Exception($"Failed to update tank {tankId} in {indexName}: {response.DebugInformation}");
                Loggers.GetIntance().SearchEngine.Error(ex.Message, ex);
                ErrorsSender.Send(ex, $"{nameof(UpdateTank)}", $"index:{indexName}, id:{tankId}");
            }
        }

        public void UpdateTanker(string kind, long tankerId, string propertyName, string newValue)
        {
            string indexName = GetDynamicIndexName(BaseTankerTableName, kind);
            var response = _client.Update<EsTanker, object>(indexName, tankerId, u => u
                .Doc(new Dictionary<string, object> { { propertyName.ToLower(), newValue } })
                .Refresh(Refresh.True));

            if (!response.IsSuccess())
            {
                var ex = new Exception($"Failed to update tanker {tankerId} in {indexName}: {response.DebugInformation}");
                Loggers.GetIntance().SearchEngine.Error(ex.Message, ex);
                ErrorsSender.Send(ex, $"{nameof(UpdateTanker)}", $"index:{indexName}, id:{tankerId}");
            }
        }

        public void CreateWithdrawal(string kind, Withdrawal withdrawal)
        {
            string withdrawalIndex = GetDynamicIndexName(BaseWithdrawalTableName, kind);
            var esWithdrawal = new EsWithdrawal
            {
                Id = withdrawal.Id,
                DocumentNumber = withdrawal.DocumentNumber,
                Amount = (double)withdrawal.Amount,
                Date = withdrawal.Date,
                StoreId = withdrawal.StoreId,
                AccountNumber = withdrawal.AccountNumber,
                DomainId = withdrawal.DomainId,
                Address = withdrawal.Address,
                Created = withdrawal.Created,
                State = withdrawal.State,
                ExternalId = withdrawal.ExternalId
            };
            var indexResponse = _client.Index(esWithdrawal, i => i.Index(withdrawalIndex).Id(withdrawal.Id));
            if (!indexResponse.IsSuccess())
            {
                var ex = new Exception($"Failed to index {withdrawalIndex}: {indexResponse.DebugInformation} Data:{esWithdrawal}");
                Loggers.GetIntance().SearchEngine.Error(ex.Message, ex);
                ErrorsSender.Send(ex, $"{nameof(CreateWithdrawal)} withdrawal creation", $"indexName:{withdrawalIndex}, data:{esWithdrawal}");
                throw ex;
            }
        }

        public void CreateBottle(string kind, Bottle bottle)
        {
            string bottleIndex = GetDynamicIndexName(BaseBottleTableName, kind);
            var esBottle = new EsBottle
            {
                Id = bottle.Id,
                Description = bottle.Description,
                Created = bottle.Created
            };
            var indexResponse = _client.Index(esBottle, i => i.Index(bottleIndex).Id(bottle.Id));
            if (!indexResponse.IsSuccess())
            {
                var ex = new Exception($"Failed to index {bottleIndex}: {indexResponse.DebugInformation} Data:{esBottle}");
                Loggers.GetIntance().SearchEngine.Error(ex.Message, ex);
                ErrorsSender.Send(ex, $"{nameof(CreateBottle)} bottle creation", $"indexName:{bottleIndex}, data:{esBottle}");
                throw ex;
            }
        }

        public void CreateDispenser(string kind, Dispenser dispenser)
        {
            string dispenserIndex = GetDynamicIndexName(BaseDispenserTableName, kind);
            var esDispenser = new EsDispenser
            {
                Id = dispenser.Id,
                Description = dispenser.Description,
                Created = dispenser.Created
            };
            var indexResponse = _client.Index(esDispenser, i => i.Index(dispenserIndex).Id(dispenser.Id));
            if (!indexResponse.IsSuccess())
            {
                var ex = new Exception($"Failed to index {dispenserIndex}: {indexResponse.DebugInformation} Data:{esDispenser}");
                Loggers.GetIntance().SearchEngine.Error(ex.Message, ex);
                ErrorsSender.Send(ex, $"{nameof(CreateDispenser)} dispenser creation", $"indexName:{dispenserIndex}, data:{esDispenser}");
                throw ex;
            }
        }

        public void CreateInvoicePayment(string kind, InvoicePayment payment)
        {
            string indexName = GetDynamicIndexName(BaseInvoicePaymentTableName, kind);
            var esPayment = new EsInvoicePayment
            {
                InvoiceId = payment.InvoiceId,
                DestinationAddress = payment.DestinationAddress,
                ExternalAtAddress = payment.ExternalAtAddress,
                PaidAmount = (double)payment.PaidAmount,
                PaidAt = payment.PaidAt
            };

            var indexResponse = _client.Index(esPayment, i => i.Index(indexName).Id(payment.InvoiceId));
            if (!indexResponse.IsSuccess())
            {
                var ex = new Exception($"Failed to index {indexName}: {indexResponse.DebugInformation} Data: {esPayment}");
                Loggers.GetIntance().SearchEngine.Error(ex.Message, ex);
                ErrorsSender.Send(ex, $"{nameof(CreateInvoicePayment)} invoice payment creation", $"indexName:{indexName}, data:{esPayment}");
                throw ex;
            }
        }

        public async Task<IEnumerable<InvoicePayment>> InvoicePaymentsAsync(string kind, string externalAtAddress, DateTime transactionDate)
        {
            var indexName = GetDynamicIndexName(BaseInvoicePaymentTableName, kind);

            var searchResponse = await _client.SearchAsync<EsInvoicePayment>(s => s
                .Index(indexName)
                .IgnoreUnavailable(true)
                .Query(q => q.Bool(b => b.Must(
                    must => must.Term(t => t.Field("externalAtAddress").Value(externalAtAddress)),
                    must => must.Range(r => r.DateRange(dr => dr.Field("paidAt").Gt(transactionDate)))
                )))
                .Sort(so => so.Field("paidAt", new FieldSort { Order = SortOrder.Desc }))
                .Size(100)
            );

            if (!searchResponse.IsValidResponse)
            {
                var ex = new Exception($"Search query for invoice payments failed: {searchResponse.DebugInformation} indexName:{indexName}, externalAtAddress:{externalAtAddress}, transactionDate:{transactionDate}");
                Loggers.GetIntance().SearchEngine.Error(ex.Message, ex);
                ErrorsSender.Send(ex, $"{nameof(InvoicePaymentsAsync)} search query", $"indexName:{indexName}, externalAtAddress:{externalAtAddress}, transactionDate:{transactionDate}");
                throw ex;
            }

            if (searchResponse.Total == 0)
            {
                return Enumerable.Empty<InvoicePayment>();
            }

            return searchResponse.Documents.Select(doc => new InvoicePayment
            {
                Kind = kind,
                InvoiceId = doc.InvoiceId,
                DestinationAddress = doc.DestinationAddress,
                ExternalAtAddress = doc.ExternalAtAddress,
                PaidAmount = (decimal)doc.PaidAmount,
                PaidAt = doc.PaidAt
            });
        }

        public async Task<TransactionSearchContainer> SearchTransactionsAsync(string kind, SearchFilter filter)
        {
            var depositIndexName = GetDynamicIndexName(BaseDepositTableName, kind);
            var withdrawalIndexName = GetDynamicIndexName(BaseWithdrawalTableName, kind);
            var indices = $"{depositIndexName},{withdrawalIndexName}";

            int size = (filter.AmountOfRows.HasValue && filter.AmountOfRows.Value > 0) ? filter.AmountOfRows.Value : 100;
            int from = (filter.InitialIndex.HasValue && filter.InitialIndex.Value > 0) ? filter.InitialIndex.Value * size : 0;

            var searchResponse = await _client.SearchAsync<JsonElement>(s => s
                .Index(indices)
                .Query(q => q
                    .Bool(b => b
                        .Must(BuildFilterClauses(filter))
                    )
                )
                .Sort(so => so.Field("date", new FieldSort { Order = SortOrder.Desc }))
                .From(from)
                .Size(size)
                .Aggregations(aggs => aggs
                    .Add("deposits_filter", a => a
                        .Filter(f => f
                            .Term(t => t.Field("_index").Value(depositIndexName))
                        )
                        .Aggregations(subAggs => subAggs
                            .Add("deposits_sum", sa => sa.Sum(sum => sum.Field("amount")))
                        )
                    )
                    .Add("withdrawals_filter", a => a
                        .Filter(f => f
                            .Term(t => t.Field("_index").Value(withdrawalIndexName))
                        )
                        .Aggregations(subAggs => subAggs
                            .Add("withdrawals_sum", sa => sa.Sum(sum => sum.Field("amount")))
                        )
                    )
                )
            );

            if (!searchResponse.IsValidResponse)
            {
                var ex = new Exception($"Search query failed: {searchResponse.DebugInformation} Indices: {indices}, filter:{filter}");
                Loggers.GetIntance().SearchEngine.Error(ex.Message, ex);
                ErrorsSender.Send(ex, $"{nameof(SearchTransactionsAsync)} search query", $"indices:{indices}, filter:{filter}");
                throw ex;
            }

            var depositsFilterAgg = searchResponse.Aggregations?.GetFilter("deposits_filter");
            var depositsSumAgg = depositsFilterAgg?.Aggregations?.GetSum("deposits_sum");
            var totalDeposits = (decimal)(depositsSumAgg?.Value ?? 0.0);

            var withdrawalsFilterAgg = searchResponse.Aggregations?.GetFilter("withdrawals_filter");
            var withdrawalsSumAgg = withdrawalsFilterAgg?.Aggregations?.GetSum("withdrawals_sum");
            var totalWithdrawals = (decimal)(withdrawalsSumAgg?.Value ?? 0.0);

            var serializerOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };

            var results = new List<TransactionSearchResult>();
            foreach (var hit in searchResponse.Hits)
            {
                var indexName = hit.Index;

                if (indexName.StartsWith(BaseDepositTableName))
                {
                    var deposit = hit.Source.Deserialize<EsDeposit>(serializerOptions);
                    results.Add(new TransactionSearchResult
                    {
                        Date = deposit.Date.ToString("MM/dd/yyyy HH:mm:ss"),
                        TransactionId = deposit.DocumentNumber,
                        ExternalId = deposit.ExternalId,
                        Type = "Deposit",
                        Deposit = (decimal)deposit.Amount,
                        Withdrawal = null,
                        State = deposit.State,
                        Address = deposit.Address
                    });
                }
                else if (indexName.StartsWith(BaseWithdrawalTableName))
                {
                    var withdrawal = hit.Source.Deserialize<EsWithdrawal>(serializerOptions);
                    results.Add(new TransactionSearchResult
                    {
                        Date = withdrawal.Date.ToString("MM/dd/yyyy HH:mm:ss"),
                        TransactionId = withdrawal.DocumentNumber,
                        ExternalId = withdrawal.ExternalId,
                        Type = "Withdrawal",
                        Deposit = null,
                        Withdrawal = (decimal)withdrawal.Amount,
                        State = withdrawal.State,
                        Address = withdrawal.Address
                    });
                }
            }

            return new TransactionSearchContainer
            {
                Transactions = results,
                TotalDeposits = totalDeposits,
                TotalWithdrawals = totalWithdrawals,
                TotalTransactions = searchResponse.Total
            };
        }

        private static Action<QueryDescriptor<JsonElement>>[] BuildFilterClauses(SearchFilter filter)
        {
            var clauses = new List<Action<QueryDescriptor<JsonElement>>>();

            if (!string.IsNullOrWhiteSpace(filter.Account))
            {
                clauses.Add(q => q.Match(m => m.Field("accountNumber").Query(filter.Account)));
            }

            if (!string.IsNullOrWhiteSpace(filter.TransactionId))
            {
                clauses.Add(q => q.Match(m => m
                    .Field("documentNumber")
                    .Query(filter.TransactionId)
                    .Operator(Operator.And)
                ));
            }

            if (!string.IsNullOrWhiteSpace(filter.State) && !filter.State.Equals("All", StringComparison.OrdinalIgnoreCase))
            {
                clauses.Add(q => q.Match(m => m.Field("state").Query(filter.State)));
            }

            if (filter.FromDate.HasValue || filter.ToDate.HasValue)
            {
                clauses.Add(q => q.Range(r => r
                    .DateRange(dr => dr
                        .Field("date")
                        .Gte(filter.FromDate)
                        .Lte(filter.ToDate)
                    )
                ));
            }

            if (!string.IsNullOrWhiteSpace(filter.ExternalId))
            {
                clauses.Add(q => q.Match(m => m.Field("externalId").Query(filter.ExternalId)));
            }

            if (!string.IsNullOrWhiteSpace(filter.Address))
            {
                clauses.Add(q => q.Match(m => m.Field("address").Query(filter.Address)));
            }

            return clauses.ToArray();
        }

        #region Document Classes
        public class EsDeposit
        {
            public long Id { get; set; }
            public string DocumentNumber { get; set; }
            public double Amount { get; set; }
            public DateTime Date { get; set; }
            public int StoreId { get; set; }
            public string AccountNumber { get; set; }
            public int DomainId { get; set; }
            public string Address { get; set; }
            public DateTime Created { get; set; }
            public string State { get; set; }
            public string ExternalId { get; set; }
            public double Rate { get; set; }

            public override string ToString()
            {
                return $"DocumentNumber: {DocumentNumber}, Amount: {Amount}, Rate: {Rate}, Date: {Date.ToString("MM/dd/yyyy")}, StoreId: {StoreId}, AccountNumber: {AccountNumber}, DomainId: {DomainId}, Address: {Address}, Created: {Created.ToString("MM/dd/yyyy")}, State: {State}";
            }
        }

        public class EsJar
        {
            public long Version { get; set; }
            public string Description { get; set; }
            public DateTime Created { get; set; }

            public override string ToString()
            {
                return $"Version: {Version}, Description: {Description}, Created: {Created.ToString("MM/dd/yyyy")}";
            }
        }

        public class EsTank
        {
            public long Id { get; set; }
            public string Name { get; set; }
            public string Description { get; set; }
            public DateTime Created { get; set; }
            public int Version { get; set; }

            public override string ToString()
            {
                return $"Id: {Id}, Name: {Name}, Description: {Description}, Version: {Version}, Created: {Created.ToString("MM/dd/yyyy")}";
            }
        }

        public class EsTanker
        {
            public long Id { get; set; }
            public string Name { get; set; }
            public string Description { get; set; }
            public DateTime Created { get; set; }
            public int Version { get; set; }

            public override string ToString()
            {
                return $"Id: {Id}, Name: {Name}, Description: {Description}, Version: {Version}, Created: {Created.ToString("MM/dd/yyyy")}";
            }
        }

        public class EsWithdrawal
        {
            public long Id { get; set; }
            public string DocumentNumber { get; set; }
            public double Amount { get; set; }
            public DateTime Date { get; set; }
            public int StoreId { get; set; }
            public string AccountNumber { get; set; }
            public int DomainId { get; set; }
            public string Address { get; set; }
            public DateTime Created { get; set; }
            public string State { get; set; }
            public string ExternalId { get; set; }

            public override string ToString()
            {
                return $"DocumentNumber: {DocumentNumber}, Amount: {Amount}, Date: {Date.ToString("MM/dd/yyyy")}, StoreId: {StoreId}, AccountNumber: {AccountNumber}, DomainId: {DomainId}, Address: {Address}, Created: {Created.ToString("MM/dd/yyyy")}, State: {State}";
            }
        }

        public class EsBottle
        {
            public long Id { get; set; }
            public string Description { get; set; }
            public DateTime Created { get; set; }

            public override string ToString()
            {
                return $"Id: {Id}, Description: {Description}, Created: {Created.ToString("MM/dd/yyyy")}";
            }
        }

        public class EsDispenser
        {
            public long Id { get; set; }
            public string Description { get; set; }
            public DateTime Created { get; set; }

            public override string ToString()
            {
                return $"Id: {Id}, Description: {Description}, Created: {Created.ToString("MM/dd/yyyy")}";
            }
        }

        public class EsDispenserDetail
        {
            public long WithdrawalId { get; set; }
            public long DispenserId { get; set; }
            public DateTime Created { get; set; }

            public override string ToString()
            {
                return $"WithdrawalId: {WithdrawalId}, DispenserId: {DispenserId}, Created: {Created.ToString("MM/dd/yyyy")}";
            }
        }

        public class EsInvoicePayment
        {
            public string InvoiceId { get; set; }
            public string DestinationAddress { get; set; }
            public string ExternalAtAddress { get; set; }
            public double PaidAmount { get; set; }
            public DateTime PaidAt { get; set; }

            public override string ToString()
            {
                return $"InvoiceId: {InvoiceId}, DestinationAddress: {DestinationAddress}, ExternalAtAddress: {ExternalAtAddress}, PaidAmount: {PaidAmount}, PaidAt: {PaidAt.ToString("MM/dd/yyyy")}";
            }
        }
        #endregion
    }
}
