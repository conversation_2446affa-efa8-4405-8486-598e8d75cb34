﻿using System;
using System.Net.Http;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using System.Text.Json;
using System.Globalization;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Elastic.Clients.Elasticsearch;
using GamesEngine.Settings;
using static GamesEngine.Business.Liquidity.Persistence.SearchStorage;
using Puppeteer.EventSourcing;

namespace GamesEngine.Business.Liquidity.ExternalServices
{
    public class NodeExplorerClient : INodeExplorerClient
    {
        private readonly HttpClient _httpClient;
        private readonly string _explorerBaseUrl;
        private const decimal SatsPerBtc = 100_000_000m;

        public NodeExplorerClient(HttpClient httpClient, IConfiguration configuration)
        {
            _httpClient = httpClient;
            _explorerBaseUrl = configuration.GetValue<string>("InvoicePayment:NodeExplorerUrl");

            if (string.IsNullOrEmpty(_explorerBaseUrl))
            {
                throw new ArgumentNullException("InvoicePayment:_explorerBaseUrl is not configured in appsettings.");
            }
        }

        public NodeExplorerClient(string _explorerBaseUrl)
        {
            if (string.IsNullOrEmpty(_explorerBaseUrl))
            {
                throw new ArgumentNullException(nameof(_explorerBaseUrl), "Node Explorer URL cannot be null or empty.");
            }
            _httpClient = new HttpClient();
            this._explorerBaseUrl = _explorerBaseUrl;
        }

        public async Task<NodeExplorerBalanceInfo> AddressBalanceInfoAsync(string address, string currencyCode)
        {
            if (string.IsNullOrEmpty(address)) throw new ArgumentNullException(nameof(address));
            if (string.IsNullOrEmpty(currencyCode)) throw new ArgumentNullException(nameof(currencyCode));

            var requestUri = $"{_explorerBaseUrl}/v1/cryptos/{currencyCode}/addresses/{address}/balance";
            var response = await _httpClient.GetAsync(requestUri);
            var responseBody = await response.Content.ReadAsStringAsync();
            if (!response.IsSuccessStatusCode)
            {
                var ex = new Exception($"Failed requesting address balance from Node Explorer at {requestUri}. Node Explorer response status: {response.StatusCode}, Body: {responseBody}");
                Loggers.GetIntance().SearchEngine.Error(ex.Message, ex);
                ErrorsSender.Send(ex, $"{nameof(AddressBalanceAsync)} deposit creation", ex.Message);
                throw ex;
            }
            return JsonSerializer.Deserialize<NodeExplorerBalanceInfo>(responseBody);
        }

        public async Task<decimal> AddressBalanceAsync(string address, string currencyCode)
        {
            var requestUri = $"{_explorerBaseUrl}/v1/cryptos/{currencyCode}/addresses/{address}/balance";

            var response = await _httpClient.GetAsync(requestUri);

            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                var ex = new Exception($"Failed requesting address balance from Node Explorer at {requestUri}. Node Explorer response status: {response.StatusCode}, Body: {responseBody}");
                Loggers.GetIntance().SearchEngine.Error(ex.Message, ex);
                ErrorsSender.Send(ex, $"{nameof(AddressBalanceAsync)} deposit creation", ex.Message);
                throw ex;
            }

            var balanceInfo = JsonSerializer.Deserialize<NodeExplorerBalanceInfo>(responseBody);

            long availableSatoshis = balanceInfo?.Available ?? 0;

            decimal balanceInBtc = availableSatoshis / SatsPerBtc;

            return balanceInBtc;
        }

        public class NodeExplorerBalanceInfo
        {
            [JsonPropertyName("confirmed")]
            public long Confirmed { get; set; }

            [JsonPropertyName("unconfirmed")]
            public long Unconfirmed { get; set; }

            [JsonPropertyName("available")]
            public long Available { get; set; }

            [JsonPropertyName("total")]
            public long Total { get; set; }
        }
    }
}
