﻿using GamesEngine;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Runtime.Serialization;

namespace LiquidityAPI.Controllers
{
    [ApiController]
    [Route("api/liquidity")]
    public class ConfirmationsByRangesController : AuthorizeController
    {
        [HttpGet("riskProfiles/{riskProfileName}/confirmationRanges")]
        [Authorize(Roles = "RiskProfiles")]
        public async Task<IActionResult> DepositRangesAsync(string riskProfileName)
        {
            if (string.IsNullOrWhiteSpace(riskProfileName)) return BadRequest($"{nameof(riskProfileName)} is required.");

            var nameEscaped = Validator.StringEscape(riskProfileName);

            var result = await LiquidityAPI.Liquidity.PerformQryAsync(HttpContext, $@"
            {{
                riskProfile = riskProfiles.GetRiskProfile('{nameEscaped}');
                for (range : riskProfile.ConfirmationRanges)
                {{
                    print range.MinAmount minAmount;
                    print range.MaxAmount maxAmount;
                    print range.ConfirmationsRequired confirmations;
                }}
            }}");

            return result;
        }

        [HttpPut("riskProfiles/{riskProfileName}/confirmationRanges")]
        [Authorize(Roles = "RiskProfiles")]
        public async Task<IActionResult> SetDepositRangesAsync(string riskProfileName, [FromBody] List<DepositRangeBody> ranges)
        {
            if (string.IsNullOrWhiteSpace(riskProfileName)) return BadRequest($"{nameof(riskProfileName)} is required.");
            if (ranges == null || !ranges.Any()) return BadRequest("At least one confirmation range is required.");
            foreach(var range in ranges)
            {
                if (range.Confirmations <= 0) return BadRequest("Each confirmation range must have a positive number of confirmations.");
            }

            var nameEscaped = Validator.StringEscape(riskProfileName);

            var minAmounts = ranges.Select(r => r.MinAmount).ToList();
            var maxAmounts = ranges.Select(r => r.MaxAmount ?? -1m).ToList();
            var confirmations = ranges.Select(r => r.Confirmations).ToList();

            var minAmountsScript = $"{{{string.Join(",", minAmounts)}}}";
            var maxAmountsScript = $"{{{string.Join(",", maxAmounts)}}}";
            var confirmationsScript = $"{{{string.Join(",", confirmations)}}}";

            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                Check(riskProfiles.ExistsProfileName('{nameEscaped}')) Error 'Risk profile {nameEscaped} does not exist';
            }}", $@"
            {{
                riskProfile = riskProfiles.GetRiskProfile('{nameEscaped}');
                riskProfile.SetConfirmationRangesFromLists({minAmountsScript}, {maxAmountsScript}, {confirmationsScript});
            }}
            ");

            return result;
        }

        [HttpGet("riskProfile/confirmations")]
        [Authorize(Roles = "RiskProfiles")]
        public async Task<IActionResult> ConfirmationsForAmountAsync([FromQuery] decimal amount, [FromQuery] string domainUrl)
        {
            if (amount < 0) return BadRequest("Amount cannot be negative.");
            if (string.IsNullOrWhiteSpace(domainUrl)) return BadRequest($"{nameof(domainUrl)} is required.");

            var domainUrlEscaped = Validator.StringEscape(domainUrl);
            var result = await LiquidityAPI.Liquidity.PerformQryAsync(HttpContext, $@"
            {{
                domain = company.Sales.DomainFrom('{domainUrlEscaped}');
                riskProfile = riskProfiles.GetRiskProfile(domain);
                confirmations = riskProfile.ConfirmationsForAmount({amount});
                Print confirmations 'confirmations';
            }}");

            return result;
        }

        public class RiskConfirmationsDTO
        {
            public int Confirmations { get; set; }
        }

        [DataContract(Name = "DepositRangeBody")]
        public class DepositRangeBody
        {
            [DataMember(Name = "minAmount")]
            public decimal MinAmount { get; set; }

            [DataMember(Name = "maxAmount")]
            public decimal? MaxAmount { get; set; }

            [DataMember(Name = "confirmations")]
            public int Confirmations { get; set; }
        }
    }
}
