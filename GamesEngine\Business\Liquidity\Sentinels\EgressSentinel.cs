﻿using GamesEngine.Business.Liquidity.Sentinels.Currency;
using GamesEngine.Business.Liquidity.Sentinels.Outbound;
using GamesEngine.Business.Liquidity.Transactions;
using GamesEngine.Finance;
using System;
using System.Collections.Generic;
using static GamesEngine.Business.Liquidity.Containers.Dispenser;
using static GamesEngine.Business.Liquidity.PaymentEngineDocks;

namespace GamesEngine.Business.Liquidity.Sentinels
{
    internal abstract class EgressSentinel : Sentinel
    {
        internal EgressSentinel() : base() { }        

        internal IEnumerable<SentinelTask> OutboundTasks => sentinelTasks.Tasks;

        internal decimal AmountInProgress
        {
            get
            {
                decimal amountInProcess = 0;
                foreach (var task in OutboundTasks)
                {
                    if (task is ConfimationsOutboundTask confirmationsTask)
                    {
                        amountInProcess += confirmationsTask.Dispenser.Amount;
                    }
                }
                return amountInProcess;
            }
        }
        internal ConfimationsOutboundTask AwaitWithdrawalConfirmations(bool itIsThePresent, DateTime now, DispenserReady dispenserReady, PaymentEngineDock paymentEngineDock)
        {
            if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now));
            if (dispenserReady == null) throw new ArgumentNullException(nameof(dispenserReady));
            if (paymentEngineDock == null) throw new ArgumentNullException(nameof(paymentEngineDock));

            var confimationsOutboundTask = sentinelTasks.AwaitWithdrawalConfirmations(now, dispenserReady, paymentEngineDock);
            confimationsOutboundTask.StartOutboundTask(itIsThePresent, now);
            return confimationsOutboundTask;
        }

        internal static EgressSentinel EgressSentinelByKind(Liquid liquid, string kind)
        {
            if (liquid == null) throw new ArgumentNullException(nameof(liquid), "Liquid cannot be null.");
            if (string.IsNullOrWhiteSpace(kind)) throw new ArgumentException("Kind cannot be null or empty.", nameof(kind));
            if (!Enum.TryParse(kind, out Currencies.CODES currencyCode)) throw new ArgumentException($"Invalid currency code: {kind}", nameof(kind));

            EgressSentinel result = null;
            switch (currencyCode)
            {
                case Currencies.CODES.BTC:
                    result = new BTCEgressSentinel();
                    break;
                case Currencies.CODES.ETH:
                    result = new ETHEgressSentinel();
                    break;
                case Currencies.CODES.LTC:
                    result = new LTCEgressSentinel();
                    break;
                default:
                    throw new ArgumentException($"Unsupported currency code: {kind}", nameof(kind));
            }
            return result;
        }

        internal void ConfirmWithdrawal(DispenserReady dispenserReady, Withdrawal withdrawal, string payoutId, string paymentDockId)
        {
            if (dispenserReady == null) throw new ArgumentNullException(nameof(dispenserReady));
            if (withdrawal == null) throw new ArgumentNullException(nameof(withdrawal));
            if (string.IsNullOrWhiteSpace(payoutId)) throw new ArgumentNullException(nameof(payoutId));

            var confimationsOutboundTask = FindTask(dispenserReady);
            if (confimationsOutboundTask is ConfimationsOutboundTask confirmationsTask)
            {
                confirmationsTask.ConfirmWithdrawal(withdrawal, payoutId, paymentDockId);
            }
            else
            {
                throw new GameEngineException("The outbound task associated with the provided DispenserReady is not a ConfimationsOutboundTask.");
            }
        }
    }
}
