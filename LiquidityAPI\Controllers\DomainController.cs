﻿using GamesEngine;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace LiquidityAPI.Controllers
{
    public class DomainController : AuthorizeController
    {
        [HttpGet("api/domains")]
        public async Task<IActionResult> DomainsAsync()
        {
            IActionResult result = await LiquidityAPI.Liquidity.PerformQryAsync(HttpContext, $@"
                {{
                    for (domains:company.Sales.AllDomains)
                    {{
                        domain = domains;
                        print domain.Id id;
                        print domain.Url url;
                        print domain.Alias alias;
                        print company.Sales.CurrentStore.IsEnabledOn(domain) enabled;
                    }}
                }}
                ");

            return result;
        }
    }
}
