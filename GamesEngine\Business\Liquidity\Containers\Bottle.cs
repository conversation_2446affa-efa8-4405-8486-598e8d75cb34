﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GamesEngine.Business.Liquidity.Transactions;
using static GamesEngine.Business.Liquidity.Containers.Dispenser;

namespace GamesEngine.Business.Liquidity.Containers
{
    internal abstract class Bottle : Container
    {
        protected List<Withdrawal> withdrawals;
        internal IEnumerable<Withdrawal> Withdrawals => withdrawals;

        internal Liquid Liquid { get; private set; }

        protected Bottle(int id, string name, string kind, Liquid liquid, DateTime created, List<Withdrawal> withdrawals) : base(id, kind,liquid, name, name, created)
        {
            if (string.IsNullOrWhiteSpace(kind)) throw new ArgumentException("Kind cannot be null or empty.", nameof(kind));
            if (liquid == null) throw new ArgumentNullException(nameof(liquid));
            if (created == DateTime.MinValue) throw new ArgumentException("Created date cannot be empty.", nameof(created));

            Liquid = liquid;
            this.withdrawals = withdrawals;
        }

        protected override Bottle Delegate()
        {
            throw new NotImplementedException();
        }

        internal class BottlePending : Bottle
        {
            private readonly IEnumerable<DispenserReady> _dispensers;

            internal BottlePending(int id, string name, string kind, Liquid liquid, DateTime created) : base(id, name, kind, liquid, created, new List<Withdrawal>())
            {
                if (id <= 0) throw new ArgumentException("Id must be greater than zero.", nameof(id));
                if (string.IsNullOrWhiteSpace(kind)) throw new ArgumentException("Kind cannot be null or empty.", nameof(kind));
                if (liquid == null) throw new ArgumentNullException(nameof(liquid));

                _dispensers = new List<DispenserReady>();
            }

            internal void AttachWithdrawal(Withdrawal withdrawal)
            {
                if (withdrawal == null) throw new ArgumentNullException(nameof(withdrawal));
                if (withdrawal.Amount <= 0) throw new ArgumentException("The withdrawal amount must be greater than zero.", nameof(withdrawal));
                withdrawals.Add(withdrawal);
                RecalculateAmount();
            }

            internal void AttachDispenser(DispenserReady dispenserReady)
            {
                if (dispenserReady == null) throw new ArgumentNullException(nameof(dispenserReady));
                if (_dispensers.Contains(dispenserReady)) throw new ArgumentException("The dispenser already exists in the bottle.", nameof(dispenserReady));
                if (dispenserReady.Kind != Kind) throw new ArgumentException("The dispenser kind does not match the bottle kind.", nameof(dispenserReady));
                if (dispenserReady.Liquid != Liquid) throw new ArgumentException("The dispenser liquid does not match the bottle liquid.", nameof(dispenserReady));

                _dispensers.Append(dispenserReady);
                RecalculateAmount();
            }

            internal BottleProcessing Process(bool itIsThePresent, DateTime now)
            {
                var result = new BottleProcessing(itIsThePresent, now, this);
                Liquid.Outlet.AddOrUpdateBottle(result);
                return result;
            }

            protected override DateTime CalculateMinDate()
            {
                throw new NotImplementedException();
            }

            protected override DateTime CalculateMaxDate()
            {
                throw new NotImplementedException();
            }
        }

        internal class BottleProcessing : Bottle
        {
            private BottlePending bottlePending;

            internal DateTime Processing { get; private set; }

            internal BottleProcessing(bool itIsThePresent, DateTime now, BottlePending bottlePending) : base(bottlePending.Id, bottlePending.Name, bottlePending.Kind, bottlePending.Liquid, bottlePending.CreatedAt, bottlePending.withdrawals)
            {
                if (bottlePending == null) throw new ArgumentNullException(nameof(bottlePending));
                this.bottlePending = bottlePending;

                Start(itIsThePresent, now);
            }

            private Task bottleTask;
            private void Start(bool itIsThePresent, DateTime now)
            {
                if (bottlePending == null) throw new ArgumentNullException(nameof(bottlePending));
                if (bottlePending.Amount <= 0) throw new ArgumentException("The amount must be greater than zero.", nameof(bottlePending));

                if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now), "Date cannot be empty.");
                if (bottleTask != null && !bottleTask.IsCompleted) throw new InvalidOperationException("The bottle is already being processed.");

                Bottle result = null;

                Processing = now;
                if (itIsThePresent)
                {
                    bottleTask = Task.Run(async () =>
                    {
                        await Task.Delay(TimeSpan.FromSeconds(10));//Rubicon: TODO: Implementar la lógica para procesar el bottle.
                                                                   //Rubicon: TODO: Implement logica como un task que se ejecute en segundo plano.
                        int resultado = 0; //Rubicon: TODO: Implementar la lógica para procesar el bottle.
                        DateTime now = DateTime.Now;
                        switch (resultado)
                        {
                            case 0:
                                string hash = "HASH_XYZ..."; //Rubicon: Todo: Implement the logic to generate the hash.
                                result = new BottleCompleted(this, hash, now);
                                break;
                            case 1:
                                result = new BottleFailed(this, now);
                                break;
                            case 2:
                                result = new BottleCanceled(this, now);
                                break;
                            default:
                                throw new InvalidOperationException("Invalid processing result.");
                        }
                        Liquid.Outlet.AddOrUpdateBottle(result);
                        bottleTask.Dispose();
                    });
                }
            }

            protected override DateTime CalculateMinDate()
            {
                throw new NotImplementedException();
            }

            protected override DateTime CalculateMaxDate()
            {
                throw new NotImplementedException();
            }
        }

        internal class BottleCompleted : Bottle
        {
            internal string Hash { get; private set; }

            internal DateTime Completed { get; private set; }

            internal BottleCompleted(BottleProcessing bottleProcessing, string hash, DateTime completed) : base(bottleProcessing.Id, bottleProcessing.Name, bottleProcessing.Kind, bottleProcessing.Liquid, bottleProcessing.CreatedAt, bottleProcessing.withdrawals)
            {
                if (bottleProcessing == null) throw new ArgumentNullException(nameof(bottleProcessing));
                if (string.IsNullOrWhiteSpace(hash)) throw new ArgumentException("Hash cannot be null or empty.", nameof(hash));
                if (completed == DateTime.MinValue) throw new ArgumentException("Completed date cannot be empty.", nameof(completed));

                Hash = hash;
                Completed = completed;
            }

            protected override DateTime CalculateMinDate()
            {
                throw new NotImplementedException();
            }

            protected override DateTime CalculateMaxDate()
            {
                throw new NotImplementedException();
            }
        }

        internal class BottleCanceled : Bottle
        {
            internal DateTime Canceled { get; private set; }

            internal BottleCanceled(BottleProcessing bottleProcessing, DateTime canceled) : base(bottleProcessing.Id, bottleProcessing.Name, bottleProcessing.Kind, bottleProcessing.Liquid, bottleProcessing.CreatedAt, bottleProcessing.withdrawals)
            {
                if (bottleProcessing == null) throw new ArgumentNullException(nameof(bottleProcessing));
                if (canceled == DateTime.MinValue) throw new ArgumentException("Canceled date cannot be empty.", nameof(canceled));

                Canceled = canceled;
            }

            protected override DateTime CalculateMinDate()
            {
                throw new NotImplementedException();
            }

            protected override DateTime CalculateMaxDate()
            {
                throw new NotImplementedException();
            }
        }

        internal class BottleFailed : Bottle
        {
            internal DateTime Failed { get; private set; }

            internal BottleFailed(BottleProcessing bottleProcessing, DateTime failed) : base(bottleProcessing.Id, bottleProcessing.Name, bottleProcessing.Kind, bottleProcessing.Liquid, bottleProcessing.CreatedAt, bottleProcessing.withdrawals)
            {
                if (bottleProcessing == null) throw new ArgumentNullException(nameof(bottleProcessing));
                if (failed == DateTime.MinValue) throw new ArgumentException("Failed date cannot be empty.", nameof(failed));
                Failed = failed;
            }

            protected override DateTime CalculateMinDate()
            {
                throw new NotImplementedException();
            }

            protected override DateTime CalculateMaxDate()
            {
                throw new NotImplementedException();
            }
        }
    }
}
