﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using Puppeteer.EventSourcing.Interprete.Libraries;
using Puppeteer.UnitTest;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UnitTestPuppeteer.Docs
{
    /// <summary>
    /// This test class is dedicated to demonstrating and verifying
    /// the variable scope rules within the Puppeteer DSL.
    /// </summary>
    [TestClass]
    public class VariableScopeTests
    {
        /// <summary>
        /// Demonstrates that variables defined at the top level of a script
        /// exist in a "global" scope for that script's execution. Inner code
        /// blocks `{}` can access these global variables.
        /// </summary>
        [TestMethod]
        public void TestScope_GlobalScopeAccess()
        {
            string script = @"
globalVar = 'I am global';
{
    Print globalVar 'innerAccess'; // Access global from inside a block.
}
Print globalVar 'outerAccess'; // Access global from outside.
";
            string expectedOutput = "{\"innerAccess\":\"I am global\",\"outerAccess\":\"I am global\"}";
            string actualOutput = Titere.Perform(script);
            Assert.AreEqual(expectedOutput, actualOutput, "Inner blocks should have access to the outer/global scope.");
        }

        /// <summary>
        /// Demonstrates that variables declared *inside* a code block `{}` are
        /// local to that block. They are destroyed when the block is exited
        /// and cannot be accessed from outside.
        /// </summary>
        [TestMethod]
        public void TestScope_BlockLocality()
        {
            string script = @"
{
    localVar = 'I am local to this block';
}
// Attempting to access 'localVar' here will fail because it no longer exists.
Print localVar 'outsideAccess';
";
            // We expect a LanguageException because 'localVar' is not defined in the outer scope.
            var ex = Assert.ThrowsException<LanguageException>(
                () => Titere.Perform(script),
                "Accessing a variable outside its block scope should throw an exception."
            );
            Assert.IsTrue(ex.Message.Contains("localVar no ha sido definida"), "Exception message should confirm the variable is undefined.");
        }

        /// <summary>
        /// Demonstrates that assigning a value to a variable name from within
        /// an inner block will *modify* an existing variable in an outer scope
        /// if one exists. It does not create a new, "shadowing" local variable.
        /// </summary>
        [TestMethod]
        public void TestScope_BlockModifiesOuterVariable()
        {
            string script = @"
outerVar = 10;
Print outerVar 'before';
{
    outerVar = 20; // This MODIFIES the outer variable.
    Print outerVar 'inside';
}
Print outerVar 'after'; // The change persists outside the block.
";
            string expectedOutput = "{\"before\":10,\"inside\":20,\"after\":20}";
            string actualOutput = Titere.Perform(script);
            Assert.AreEqual(expectedOutput, actualOutput, "Inner block should modify the outer variable, not shadow it.");
        }

        /// <summary>
        /// Demonstrates that if the iteration variable in a `For` loop does not
        /// already exist in an outer scope, it is treated as a new, local variable
        /// that exists *only within the loop's body*. It cannot be accessed
        /// after the loop has finished.
        /// </summary>
        [TestMethod]
        public void TestScope_ForLoopVariableIsLocalWhenNew()
        {
            string script = @"
sum = 0;
For (newLoopVar : {1, 2, 3}) {
    sum = sum + newLoopVar; // 'newLoopVar' is accessible here.
}
// Attempting to access 'newLoopVar' here will fail because it was local to the loop.
Print newLoopVar 'afterLoop';
";
            // We expect a LanguageException because 'newLoopVar' is not defined outside the loop.
            var ex = Assert.ThrowsException<LanguageException>(
                () => Titere.Perform(script),
                "A For loop variable that did not exist before the loop should be local to it."
            );
            Assert.IsTrue(ex.Message.Contains("newLoopVar no ha sido definida"));
        }

        /// <summary>
        /// Demonstrates that a `For` loop's iteration variable will reuse and
        /// modify an existing variable from an outer scope if it shares the same name.
        /// The variable is NOT local to the loop, and its value will be the last
        /// value from the iteration after the loop completes.
        /// </summary>
        [TestMethod]
        public void TestScope_ForLoopVariableModifiesOuterScope()
        {
            string script = @"
loopVar = 100; // Pre-existing variable.
Print loopVar 'beforeLoop';

sum = 0;
// This loop reuses the outer 'loopVar', overwriting its value.
For (loopVar : {1, 5, 10}) {
    sum = sum + loopVar;
}

// After the loop, 'loopVar' holds the LAST value from the iterated list.
Print loopVar 'afterLoop';
Print sum 'finalSum';
";
            // The actual behavior is that the outer 'loopVar' is modified.
            string expectedOutput = "{\"beforeLoop\":100,\"afterLoop\":10,\"finalSum\":16}";
            string actualOutput = Titere.Perform(script);
            Assert.AreEqual(expectedOutput, actualOutput, "For loop variable should modify an outer variable of the same name.");
        }
    }
}
