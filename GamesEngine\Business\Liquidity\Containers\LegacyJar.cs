﻿using GamesEngine.Business.Liquidity.Transactions;
using System;
using System.Collections.Generic;

namespace GamesEngine.Business.Liquidity.Containers
{
    internal class LegacyJar : Container
    {
        private Jar lastJar;

        internal LegacyJar(Jar jar) : base(jar.Version, jar.Kind, jar.Source.Liquid)
        {
            if (jar == null) throw new GameEngineException("The Jar is null.");

            lastJar = jar;
            RecalculateAmount();
        }

        internal Jar Jar => lastJar;

        internal int Version => lastJar.Version;

        protected override DateTime CalculateMaxDate() { 
            return DateTime.MaxValue;
        }

        protected override DateTime CalculateMinDate() {
            return DateTime.MinValue;
        }

        protected override LegacyJar Delegate()
        {
            throw new NotImplementedException();
        }

        // public Jar Jar { get { return lastJar; } }

        internal IEnumerable<Deposit> Deposits()
        {
            return lastJar.Deposits;
        }

        internal EnclosureDeposit FindEnclosureDeposit(int depositId)
        {
            if (depositId <= 0) throw new ArgumentNullException(nameof(depositId));
            return lastJar.FindEnclosureDeposit(depositId);
        }
    }

    internal class AuditRecord
    {
        internal DateTime Timestamp { get; }
        internal string Operation { get; }
        internal string User { get; }

        internal AuditRecord(string operation, string user)
        {
            Timestamp = DateTime.UtcNow;
            Operation = operation ?? throw new ArgumentNullException(nameof(operation));
            User = user ?? throw new ArgumentNullException(nameof(user));
        }
    }


}
