﻿using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamesEngine.Business.Liquidity.Sentinels
{

    internal abstract class Sentinel : Objeto
    {
        protected readonly SentinelTasks sentinelTasks;

        protected Sentinel()
        {
            sentinelTasks = new SentinelTasks();
        }

        internal SentinelTask FindTask(object objectKey)
        {
            if (objectKey == null) throw new ArgumentNullException(nameof(objectKey));
            return sentinelTasks.FindTask(objectKey);
        }

        internal bool HasTask(object objectKey)
        {
            if (objectKey == null) throw new ArgumentNullException(nameof(objectKey));
            return sentinelTasks.HasTask(objectKey);
        }

        internal void Detach(SentinelTask task)
        {
            if (task == null) throw new ArgumentNullException(nameof(task));
            sentinelTasks.Detach(task);
        }
    }
}
