﻿
using System.Collections.Generic;
using System;

namespace GamesEngine.RealTime
{
    internal class LRUCache<TKey, TValue>
    {
        private readonly int capacity;
        private readonly Dictionary<TKey, LinkedListNode<(TKey key, TValue value)>> cache;
        private readonly LinkedList<(TK<PERSON> key, TValue value)> lruList;

        public LRUCache(int capacity)
        {
            this.capacity = capacity;
            cache = new Dictionary<TKey, LinkedListNode<(TKey, TValue)>>(capacity);
            lruList = new LinkedList<(TKey, TValue)>();
        }

        public TValue Get(TKey key)
        {
            if (!cache.TryGetValue(key, out var node))
                throw new KeyNotFoundException();

            lruList.Remove(node);
            lruList.AddFirst(node);
            return node.Value.value;
        }

        public void Put(TKey key, TValue value)
        {
            if (cache.TryGetValue(key, out var node))
            {
                lruList.Remove(node);
            }
            else if (cache.Count >= capacity)
            {
                var lastNode = lruList.Last;
                if (lastNode != null)
                {
                    cache.Remove(lastNode.Value.key);
                    lruList.RemoveLast();
                }
            }
            var newNode = new LinkedListNode<(TKey, TValue)>((key, value));
            lruList.AddFirst(newNode);
            cache[key] = newNode;
        }

        public bool TryGet(TKey key, out TValue value)
        {
            if (cache.TryGetValue(key, out var node))
            {
                lruList.Remove(node);
                lruList.AddFirst(node);
                value = node.Value.value;
                return true;
            }
            value = default;
            return false;
        }

        public IEnumerable<TValue> GetValues()
        {
            foreach (var node in lruList)
                yield return node.value;
        }

        public int Count => cache.Count;
    }
}
