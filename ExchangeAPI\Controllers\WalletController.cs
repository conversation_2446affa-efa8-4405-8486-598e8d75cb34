﻿using Amazon.Runtime.Internal.Transform;
using Confluent.Kafka;
using Connectors.town.connectors.driver.transactions;
using GamesEngine;
using GamesEngine.Business;
using GamesEngine.Domains;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.Serialization;
using System.Threading.Tasks;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.fiero.processors.AuthTransaction;
using town.connectors.drivers.hades;
using static ExchangeAPI.Controllers.AdminController;
using static ExchangeAPI.Controllers.APIController;
using static ExchangeAPI.Controllers.WalletController;
using static GamesEngine.Business.WholePaymentProcessor;
using static GamesEngine.Finance.PaymentChannels;
using static GamesEngine.Settings.PaymentManager;
using static town.connectors.CustomSettings;
using static town.connectors.drivers.Result;
namespace ExchangeAPI.Controllers
{
    public class WalletController : AuthorizeController
    {
        [HttpPost("api/wallet/drafts/deposit")]
        [Authorize(Roles = "c5,player")]
        public async Task<IActionResult> AddWalletDepositDraftAsync([FromBody] WalletDepositCreationBody body, [FromHeader(Name = "domain-url")] string domain)
        {
            if (body == null) return BadRequest("Body is required");
            if (body.Amount <= 0) return BadRequest($"{nameof(body.Amount)} must be greater than 0");
            if (body.EntityId <= 0) return BadRequest($"{nameof(body.EntityId)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(body.ToCurrencyCode)) return BadRequest($"{nameof(body.ToCurrencyCode)} is required");
            if (string.IsNullOrWhiteSpace(body.FromCurrencyCode)) return BadRequest($"{nameof(body.FromCurrencyCode)} is required");
            if (string.IsNullOrWhiteSpace(body.ToIdentifier)) return BadRequest($"{nameof(body.ToIdentifier)} is required");
            if (!body.PaymentMethod.HasValue) return BadRequest($"{nameof(body.PaymentMethod)} is required");
            if (string.IsNullOrWhiteSpace(domain)) return BadRequest($"{nameof(domain)} is required");

            string path = "CR/Cartago"; //TODO: batch is fixed and it should be dynamic
            string employeeName = "FW";
            string depositor = (body.Depositor == null) ? "" : body.Depositor;
            string voucher = (body.Voucher == null) ? "" : body.Voucher;
            string voucherUrl = (body.VoucherUrl == null) ? "" : body.VoucherUrl;
            int agentId = (int)Security.UserAgent(HttpContext);


            var accountCommand = string.IsNullOrWhiteSpace(body.AccountNumber) ?
                    $"account = customer.FindAccountByCurrency('{body.FromCurrencyCode}');" :
                    $"account = customer.FindAccount('{body.AccountNumber}');";

            var result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
				existsCustomer = company.ExistsCustomerByIdentifier('{body.ToIdentifier}');
                Check(existsCustomer) Error 'Customer {body.ToIdentifier} does not exist';
				if (existsCustomer) 
				{{
					customer = company.CustomerByIdentifier('{body.ToIdentifier}');
					Check(customer.IsApproved()) Error 'Customer {body.ToIdentifier} is not approved';
				}}
				domain = company.Sales.DomainFrom('{domain}');
				isTransactionAllowed = domain.AllowedTransactions.ItsEnabled({TransactionType.Deposit});
				Check(isTransactionAllowed) Error 'Transaction {TransactionType.Deposit} is not enabled';
				existsDomainInMarketplace = company.Sales.CurrentStore.ExistsDomain(domain.Id);
				Check(existsDomainInMarketplace) Error 'Domain {domain} does not belong to marketplace';
			",
            $@"
			{{
				domain = company.Sales.DomainFrom('{domain}');
				customer = company.CustomerByIdentifier('{body.ToIdentifier}');
				{accountCommand}
				Eval('transactionNumber =' + marketplace.NewTransationNumber() + ';');

				drivers = company.System.DriverManagers.PaymentProcessorsAndActionsByDomains.SearchForX(domain.AgentId, domain.Url);
				processor = drivers.SearchByX({TransactionType.Deposit}, '{body.FromCurrencyCode}', {body.PaymentMethod.Value}, {body.EntityId});

				processorAccountId = guardian.Accounts().SearchByProcessor(processor.ProcessorKey).Id;
				transaction = marketplace.From(transactionNumber, account, '{path}', '{employeeName}', domain).Deposit(Now, itIsThePresent, Currency('{body.FromCurrencyCode}',{body.Amount}), '{employeeName}', '{depositor}', '{voucher}', '{voucherUrl}', '{body.Description}', processor, processorAccountId);
				print transactionNumber authorizationId;
				print transaction.BatchTransactions.TransactionsNumber batchNumber;
				print Now now;
				print company.Sales.CurrentStore.Id storeId;
				print domain.Id domainId;
			}}
			");

            if (!(result is OkObjectResult)) return result;

            var o = (OkObjectResult)result;
            var json = o.Value.ToString();
            var transactionResponse = JsonConvert.DeserializeObject<TransactionResponse>(json);



            bool itIsThePresent = true;
            DespositBody despositBody = new DespositBody(
                body.AccountNumber,
                body.ToIdentifier,
                body.Amount,
                $"{nameof(TransactionType.Deposit)}",
                DateTime.Now,
                string.Empty,
                employeeName,
                new GamesEngine.Domains.Domain(itIsThePresent, transactionResponse.DomainId, domain, Agents.TEST_BOOK),
                path,
                body.PaymentMethod.Value,
                body.EntityId,
                transactionResponse.StoreId,
                string.Empty,
                string.Empty,
                string.Empty,
                string.Empty,
                string.Empty,
                0,
                transactionResponse.AuthorizationId.ToString()
            );
            despositBody.SourceNumber = 17;//Rubicon: DEFAULT SOURCENUMBER
            despositBody.WithLock = true;
            DespositResponse despositResponse = await PaymentChannels.DepositAsync(itIsThePresent, agentId, domain, body.FromCurrencyCode, despositBody);
            if (despositResponse.Status != TransactionStatus.APPROVED)
            {
                return BadRequest($"Deposit draft with id {transactionResponse.AuthorizationId} could not be created. Status: {despositResponse.Status}");
            }

            if (despositResponse.AuthorizationId <= 0)
            {
                return BadRequest($"Deposit draft with id {transactionResponse.AuthorizationId} could not be created. AuthorizationId: {despositResponse.AuthorizationId}");
            }



            var resultAuth = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
				existsCustomer = company.ExistsCustomerByIdentifier('{body.ToIdentifier}');
                Check(existsCustomer) Error 'Customer {body.ToIdentifier} does not exist';
				if (existsCustomer) 
				{{
					customer = company.CustomerByIdentifier('{body.ToIdentifier}');
					Check(customer.IsApproved()) Error 'Customer {body.ToIdentifier} is not approved';
				}}
				domain = company.Sales.DomainFrom('{domain}');
				isTransactionAllowed = domain.AllowedTransactions.ItsEnabled({TransactionType.Deposit});
				Check(isTransactionAllowed) Error 'Transaction {TransactionType.Deposit} is not enabled';
				existsDomainInMarketplace = company.Sales.CurrentStore.ExistsDomain(domain.Id);
				Check(existsDomainInMarketplace) Error 'Domain {domain} does not belong to marketplace';
			",
            $@"
			{{
				transaction = marketplace.FindDraftTransaction({transactionResponse.AuthorizationId}, '{path}', '{employeeName}');
				transaction.AuthorizationId = {despositResponse.AuthorizationId};
			}}
			");

            if (!(resultAuth is OkObjectResult)) return resultAuth;


            DraftDeposit draftDeposit = new DraftDeposit
            {
                ConfirmedAmount = body.Amount,
                ConfirmedCurrency = body.FromCurrencyCode,
                AtAddress = body.ToIdentifier,
                ExternalReference = transactionResponse.AuthorizationId,
                EmployeeName = employeeName
            };

            MultipleProcessors drivers = WholePaymentProcessor.Instance().PaymentProcessorsAndActionsByDomains.SearchFor(agentId, domain);
            PaymentProcessor paymentProcessor = drivers.SearchByX(TransactionType.Deposit, body.ToCurrencyCode, PaymentMethod.Secrets, entityId: body.EntityId);
            ResponseInvoicePayment response = null;
            using (RecordSet recordSet = paymentProcessor.GetRecordSet())
            {
                recordSet.SetParameter("currency", body.FromCurrencyCode);
                recordSet.SetParameter("amount", body.Amount);
                recordSet.SetParameter("atAddress", body.ToIdentifier);
                recordSet.SetParameter("accountNumber", body.AccountNumber);
                recordSet.SetParameter("employeeName", employeeName);
                recordSet.SetParameter("description", body.Description);
                recordSet.SetParameter("entityId", body.EntityId);
                recordSet.SetParameter("domain", domain);
                recordSet.SetParameter("externalReference", transactionResponse.AuthorizationId);

                var executeResponse = await paymentProcessor.ExecuteAsync<ResponseInvoicePayment>(DateTime.Now, recordSet);

                if (executeResponse.Status == WholePaymentProcessor.PaymentProcessor.Status.Ok && executeResponse.Data != null)
                {
                    response = executeResponse.Data;
                }
                else
                {
                    return BadRequest($"Failed to execute deposit transaction: {executeResponse.ErrorMessage}");
                }
            }
            if (response == null || response.AuthorizationId <= 0) return BadRequest("Failed to create deposit transaction. Please check the input data and try again.");

            var qrCodeResponseData = await GetQrCodeDepositAsync(response.DepositId, body.ToCurrencyCode);
            if (qrCodeResponseData == null || qrCodeResponseData == QrCodeResponse.FAKE_QR_RESPONSE) return BadRequest("Failed to create deposit transaction. Please check the input data and try again.");

            var walletResponse = new WalletTransactionResponse();
            walletResponse.AuthorizationId = transactionResponse.AuthorizationId;
            walletResponse.DestinationAddress = qrCodeResponseData.Destination;
            walletResponse.PaymentLink = qrCodeResponseData.PaymentLink;
            walletResponse.Amount = qrCodeResponseData.Amount;
            walletResponse.Rate = qrCodeResponseData.Rate;

            return Ok(walletResponse);
        }

        [HttpPost("api/wallet/drafts/withdrawal")]
        [Authorize(Roles = "c9,player")]
        public async Task<IActionResult> AddWalletWithdrawalDraftAsync([FromBody] WalletWithdrawalCreationBody body, [FromHeader(Name = "domain-url")] string domain)
        {
            if (body == null) return BadRequest("Body is required");
            if (body.Amount <= 0) return BadRequest($"{nameof(body.Amount)} must be greater than 0");
            if (body.MinerFee < 0) return BadRequest($"{nameof(body.MinerFee)} must be greater or equal than 0");
            if (body.EntityId <= 0) return BadRequest($"{nameof(body.EntityId)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(body.FromCurrencyCode)) return BadRequest($"{nameof(body.FromCurrencyCode)} is required");
            if (string.IsNullOrWhiteSpace(body.ToCurrencyCode)) return BadRequest($"{nameof(body.ToCurrencyCode)} is required");
            if (string.IsNullOrWhiteSpace(body.FromIdentifier)) return BadRequest($"{nameof(body.FromIdentifier)} is required");
            if (!body.PaymentMethod.HasValue) return BadRequest($"{nameof(body.PaymentMethod)} is required");
            if (string.IsNullOrWhiteSpace(domain)) return BadRequest($"{nameof(domain)} is required");

            string path = "CR/Cartago"; //TODO: batch is fixed and it should be dynamic
            string employeeName = "FW";

            string accountCommand = string.IsNullOrWhiteSpace(body.accountNumber) ?
                $"account = customer.FindAccountByCurrency('{body.FromCurrencyCode}');" :
                $"account = customer.FindAccount('{body.accountNumber}');";

            var result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
				existsCustomer = company.ExistsCustomerByIdentifier('{body.FromIdentifier}');
                Check(existsCustomer) Error 'Customer {body.FromIdentifier} does not exist';
				if (existsCustomer) 
				{{
					customer = company.CustomerByIdentifier('{body.FromIdentifier}');
					Check(customer.IsApproved()) Error 'Customer {body.FromIdentifier} is not approved';
				}}
				domain = company.Sales.DomainFrom('{domain}');
				isTransactionAllowed = domain.AllowedTransactions.ItsEnabled({TransactionType.Withdrawal});
				Check(isTransactionAllowed) Error 'Transaction {TransactionType.Withdrawal} is not enabled';
				existsDomainInMarketplace = company.Sales.CurrentStore.ExistsDomain(domain.Id);
				Check(existsDomainInMarketplace) Error 'Domain {domain} does not belong to marketplace';
			",
            $@"
			{{
				domain = company.Sales.DomainFrom('{domain}');
				customer = company.CustomerByIdentifier('{body.FromIdentifier}');
				{accountCommand}
				Eval('transactionNumber =' + marketplace.NewTransationNumber() + ';');

				drivers = company.System.DriverManagers.PaymentProcessorsAndActionsByDomains.SearchForX(domain.AgentId, domain.Url);
				processor = drivers.SearchByX({TransactionType.Withdrawal}, '{body.FromCurrencyCode}', {body.PaymentMethod.Value}, {body.EntityId});
				
				processorAccount = guardian.Accounts().SearchByProcessor(processor.ProcessorKey);
				transaction = marketplace.From(transactionNumber, account, '{path}', '{employeeName}', domain);
				print transaction.Id transactionId;
				print account.Identificator account;
				print customer.AccountNumber customerNumber;
				print processorAccount.Id processorAccountId;
				print company.Sales.CurrentStore.Id storeId;
				print domain.AgentId agentId;
				print domain.Url domainUrl;
				print domain.Id domainId;
			}}
			");
            if (!(result is OkObjectResult))
            {
                return result;
            }
            var o = (OkObjectResult)result;
            var json = o.Value.ToString();
            var tempTransactionResponse = JsonConvert.DeserializeObject<TempTransactionResponse>(json);

           

            var useless = DateTime.Now.AddDays(DaysToBecomeUseless);
            var authorizationNumberFromLockBalance = await PaymentChannels.LockBalanceAsync(
                tempTransactionResponse.CustomerNumber,
                body.Amount,
                body.FromCurrencyCode,
                body.Description,
                tempTransactionResponse.TransactionId.ToString(),
                tempTransactionResponse.Account,
                employeeName,
                tempTransactionResponse.StoreId,
                useless,
                tempTransactionResponse.AgentId,
                tempTransactionResponse.DomainUrl,
                tempTransactionResponse.DomainId,
                body.PaymentMethod.Value,
                body.EntityId
            );

            if (authorizationNumberFromLockBalance == ASITenantDriver.FAKE_TICKET_NUMBER)
            {
                await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, $@"
				{{
					transaction = marketplace.SearchTemporaryDefinition({tempTransactionResponse.TransactionId}, '{path}', '{employeeName}').Remove();
				}}
				");
            }

            var feeScript = body.MinerFee == 0 ? $"NoFee('{body.FromCurrencyCode}')" : $"MinerFee(Currency('{body.FromCurrencyCode}',{body.MinerFee}))";
            string realAccount = body.RealAccount;
            var errorMessage = "Sorry, your withdrawal cannot be completed at this moment. Please try again. If the problem persists, please contact customer service.";
            result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
					Check({authorizationNumberFromLockBalance} != {ASITenantDriver.FAKE_TICKET_NUMBER}) Error '{errorMessage}';
				", $@"
				{{
					domain = company.Sales.DomainFrom('{domain}');
					drivers = company.System.DriverManagers.PaymentProcessorsAndActionsByDomains.SearchForX(domain.AgentId, domain.Url);
					processor = drivers.SearchByX({TransactionType.Withdrawal}, '{body.FromCurrencyCode}', {body.PaymentMethod.Value}, {body.EntityId});

					processorAccountId = guardian.Accounts().SearchByProcessor(processor.ProcessorKey).Id;
					transaction = marketplace.SearchTemporaryDefinition({tempTransactionResponse.TransactionId}, '{path}', '{employeeName}').Withdraw(Now, itIsThePresent, Currency('{body.FromCurrencyCode}',{body.Amount}), {authorizationNumberFromLockBalance}, '{employeeName}', '{realAccount}', '{body.Description}', {feeScript}, processor, processorAccountId);
					print transactionNumber authorizationId;
					print transaction.BatchTransactions.TransactionsNumber batchNumber;
					print Now now;
                    agent = marketplace.SearchAgent('{path}');
				    user = agent.SearchUser('{employeeName}');
				    print user.IsInRange(transaction.Amount) isAmountInAgentRange;
				}}");
            if (!(result is OkObjectResult))
            {
                return result;
            }
            o = (OkObjectResult)result;
            json = o.Value.ToString();
            var walletTransactionResponse = JsonConvert.DeserializeObject<WalletTransactionResponse>(json);

            bool areAllWithdrawalsManagedAsDisbursements = await RetrieveDisbursementConfigAsync(HttpContext);

            if (!areAllWithdrawalsManagedAsDisbursements && walletTransactionResponse.IsAmountInAgentRange)
            {
                var withdrawalBody = new GamesEngine.Exchange.WithdrawBody(
                    body.FromIdentifier,
                    body.RealAccount,
                    body.Amount,
                    $"{nameof(TransactionType.Withdrawal)}",
                    DateTime.Now,
                    body.RealAccount,
                    tempTransactionResponse.DomainId,
                    tempTransactionResponse.DomainUrl,
                    PaymentMethod.Secrets,
                    body.EntityId,
                    tempTransactionResponse.StoreId,
                    string.Empty,
                    string.Empty,
                    string.Empty,
                    string.Empty,
                    new GamesEngine.Domains.Domain(false, tempTransactionResponse.DomainId, tempTransactionResponse.DomainUrl, Agents.TEST_BOOK),
                    employeeName,
                    reference: tempTransactionResponse.TransactionId.ToString()
                );

                int agentId = (int)Security.UserAgent(HttpContext);
                var itIsThePresent = true;
                WithdrawReponse withdrawalResponseResult = await PaymentChannels.WithDrawAsync(itIsThePresent, agentId, tempTransactionResponse.DomainUrl, body.ToCurrencyCode, withdrawalBody);
                if (withdrawalResponseResult.Status != TransactionStatus.APPROVED)
                {
                    return BadRequest($"Withdrawal draft with id {tempTransactionResponse.TransactionId} could not be created. Status: {withdrawalResponseResult.Status}");
                }
                if (withdrawalResponseResult.AuthorizationId <= 0)
                {
                    return BadRequest($"Withdrawal draft with id {tempTransactionResponse.TransactionId} could not be created. AuthorizationId: {withdrawalResponseResult.AuthorizationId}");
                }
            }


            return Ok(walletTransactionResponse);
        }

        internal static async Task<QrCodeResponse> GetQrCodeDepositAsync(int depositId, string currency)
        {

            try
            {
                if (depositId <= 0) throw new ArgumentNullException(nameof(depositId));
                if (string.IsNullOrWhiteSpace(currency)) throw new ArgumentNullException(nameof(currency));

                var endpoint = $"/town/v1/transaction/deposit/{depositId}/qrcode/{currency}";
                var qrResponse = await ProxyEndpointsSettings.GetAsync(endpoint);

                if (string.IsNullOrWhiteSpace(qrResponse))
                {
                    throw new GameEngineException($"Failed to create deoist with reference:{depositId}.");
                }

                var qrResult = JsonConvert.DeserializeObject<QrCodeResponse>(qrResponse);
                return qrResult;
            }
            catch (Exception ex)
            {

                return QrCodeResponse.FAKE_QR_RESPONSE;
            }
        }

        internal static async Task<RateResponse> GetRateAsync(string fromCurrency, string toCurrency)
        {

            try
            {
                if (string.IsNullOrWhiteSpace(fromCurrency)) throw new ArgumentNullException(nameof(fromCurrency));
                if (string.IsNullOrWhiteSpace(toCurrency)) throw new ArgumentNullException(nameof(toCurrency));

                var endpoint = $"/town/v1/rate/{fromCurrency}/to/{toCurrency}";
                var rteResponse = await ProxyEndpointsSettings.GetAsync(endpoint);


                var rateResult = JsonConvert.DeserializeObject<RateResponse>(rteResponse);
                return rateResult;
            }
            catch (Exception ex)
            {

                return RateResponse.FAKE_RATE_RESPONSE;
            }
        }

        internal static async Task<LastAddressResponse> GetLastWithdrawalAddressessAsync(string currencyCode, string accountNumber)
        {

            try
            {
                if (string.IsNullOrWhiteSpace(currencyCode)) throw new ArgumentNullException(nameof(currencyCode));
                if (string.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));
                var endpoint = $"/town/v1/{currencyCode}/accounts/{accountNumber}/withdrawal/addresses";
                var lastWitdrawalAddresses = await ProxyEndpointsSettings.GetAsync(endpoint);


                var lastWitdrawalAddressesResult = JsonConvert.DeserializeObject<LastAddressResponse>(lastWitdrawalAddresses);
                return lastWitdrawalAddressesResult;
            }
            catch (Exception ex)
            {

                return LastAddressResponse.FAKE_LAST_ADDRESS_RESPONSE;
            }
        }

        internal static async Task<bool> RetrieveDisbursementConfigAsync(HttpContext context)
        {
            var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(context, $@"
				{{
					print marketplace.AreAllWithdrawalsManagedAsDisbursements areAllWithdrawalsManagedAsDisbursements;
				}}
				");

            if (!(result is OkObjectResult))
            {
                throw new GameEngineException($@"Error:{((ObjectResult)result).Value.ToString()}");
            }

            var o = (OkObjectResult)result;
            var json = o.Value.ToString();
            var disbursementConfig = JsonConvert.DeserializeObject<DisbursementConfig>(json);

            return disbursementConfig.AreAllWithdrawalsManagedAsDisbursements;
        }

        [HttpPost("api/wallet/drafts/transfer")]
        [Authorize(Roles = "c13,player")]
        public async Task<IActionResult> AddTransferDraftAsync([FromBody] WalletTransferCreationBody body, [FromHeader(Name = "domain-url")] string domain)
        {
            if (body == null) return BadRequest("Body is required");
            if (body.Amount <= 0) return BadRequest($"{nameof(body.Amount)} must be greater than 0");
            if (body.EntityId <= 0) return BadRequest($"{nameof(body.EntityId)} must be greater than 0");
            //if (!body.PaymentMethod.HasValue) return BadRequest($"{nameof(body.PaymentMethod)} is required");
            if (string.IsNullOrWhiteSpace(body.FromCurrencyCode)) return BadRequest($"{nameof(body.FromCurrencyCode)} is required");
            if (string.IsNullOrWhiteSpace(body.ToCurrencyCode)) return BadRequest($"{nameof(body.ToCurrencyCode)} is required");
            if (string.IsNullOrWhiteSpace(body.FromIdentifier)) return BadRequest($"{nameof(body.FromIdentifier)} is required");
            if (string.IsNullOrWhiteSpace(body.ToIdentifier)) return BadRequest($"{nameof(body.ToIdentifier)} is required");
            if (string.IsNullOrWhiteSpace(body.RealAccount)) body.RealAccount = "";
            if (string.IsNullOrWhiteSpace(domain)) return BadRequest($"{nameof(domain)} is required");

            string path = "CR/Cartago"; //TODO: batch is fixed and it should be dynamic
            string employeeName = "FW";
            string batchPath = path + '/' + employeeName;

            string accountCommand = string.IsNullOrWhiteSpace(body.FromAccountNumber) ?
                $"account = customer.FindAccountByCurrency('{body.FromCurrencyCode}');" :
                $"account = customer.FindAccount('{body.FromAccountNumber}');";

            var result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
					existPaymentProcessor = company.System.DriverManagers.ExistPaymentProcessor({TransactionType.Transfer}, '{body.FromCurrencyCode}', {body.EntityId}, {body.PaymentMethod.Value});
					Check(existPaymentProcessor) Error 'Payment processor does not exist';

					existsCustomer = company.ExistsCustomerByIdentifier('{body.FromIdentifier}');
					Check(existsCustomer) Error 'Customer {body.FromIdentifier} does not exist';
					if (existsCustomer) 
					{{
						customer = company.CustomerByIdentifier('{body.FromIdentifier}');
						Check(customer.IsApproved()) Error 'Customer {body.FromIdentifier} is not approved';
					}}

					domain = company.Sales.DomainFrom('{domain}');
					isTransactionAllowed = domain.AllowedTransactions.ItsEnabled({TransactionType.Transfer});
					Check(isTransactionAllowed) Error 'Transaction {TransactionType.Transfer} is not enabled';
					existsDomainInMarketplace = company.Sales.CurrentStore.ExistsDomain(domain.Id);
					Check(existsDomainInMarketplace) Error 'Domain {domain} does not belong to marketplace';

					existsBatch = marketplace.Batches.ExistAgentBatch('{batchPath}');
					Check(existsBatch) Error 'Agent batch {batchPath} does not exist.';
					if (existsBatch)
					{{
						agentBatch = marketplace.SearchAgentBatch('{batchPath}');
						Check(agentBatch.BatchTransactions.HasAvailable(Currency('{body.FromCurrencyCode}',{body.Amount}))) Error 'Agent batch {batchPath} does not have enough funds for {body.FromCurrencyCode}{body.Amount}.';
					}}

					existsAgent = marketplace.ExistsAgent('{path}');
					Check(existsAgent) Error 'Agent {path} does not exist.';
					if (existsAgent)
					{{
						agent = marketplace.SearchAgent('{path}');
						user = agent.SearchUser('{employeeName}');
						Check(user.IsInRange(Currency('{body.FromCurrencyCode}',{body.Amount}))) Error 'Agent {path} does not have enough funds for {body.FromCurrencyCode}{body.Amount}.';
					}}
				", $@"
				{{
					domain = company.Sales.DomainFrom('{domain}');
					customer = company.CustomerByIdentifier('{body.FromIdentifier}');
					{accountCommand}
					Eval('transactionNumber =' + marketplace.NewTransationNumber() + ';');
					
					drivers = company.System.DriverManagers.PaymentProcessorsAndActionsByDomains.SearchForX(domain.AgentId, domain.Url);
					processor = drivers.SearchByX({TransactionType.Transfer}, '{body.FromCurrencyCode}', {body.PaymentMethod.Value}, {body.EntityId});

					transaction = marketplace.From(transactionNumber, account, '{path}', '{employeeName}', domain);
					print transaction.Id transactionId;
					print account.Identificator account;
					processorAccount = guardian.Accounts().SearchByProcessor(processor.ProcessorKey);
					print processorAccount.Id processorAccountId;
					print customer.AccountNumber customerNumber;
					print company.Sales.CurrentStore.Id storeId;
					print domain.AgentId agentId;
					print domain.Url domainUrl;
					print domain.Id domainId;
					print transaction.Processor.PaymentMethodAsString paymentMethodType;
				}}
			");
            if (!(result is OkObjectResult))
            {
                return result;
            }
            var o = (OkObjectResult)result;
            var json = o.Value.ToString();
            TempTransactionResponse tempTransactionResponse = JsonConvert.DeserializeObject<TempTransactionResponse>(json);

            var useless = DateTime.Now.AddDays(DaysToBecomeUseless);
            var authorizationNumberFromLockBalance = await PaymentChannels.LockBalanceAsync(
                tempTransactionResponse.CustomerNumber,
                body.Amount,
                body.FromCurrencyCode,
                body.Description,
                tempTransactionResponse.TransactionId.ToString(),
                tempTransactionResponse.Account,
                employeeName,
                tempTransactionResponse.StoreId,
                useless,
                tempTransactionResponse.AgentId,
                tempTransactionResponse.DomainUrl,
                tempTransactionResponse.DomainId,
                tempTransactionResponse.PaymentMethodType.Value,
                body.EntityId
            );

            if (authorizationNumberFromLockBalance == ASITenantDriver.FAKE_TICKET_NUMBER)
            {
                await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, $@"
					{{
						transaction = marketplace.SearchTemporaryDefinition({tempTransactionResponse.TransactionId}, '{path}', '{employeeName}').Remove();
					}}
					");
            }

            accountCommand = string.IsNullOrWhiteSpace(body.ToAccountNumber) ?
                $"toAccount = targetCustomer.FindAccountByCurrency('{body.ToCurrencyCode}');" :
                $"toAccount = targetCustomer.FindAccount('{body.ToAccountNumber}');";

            var errorMessage = "Sorry, your transfer cannot be completed at this moment. Please try again. If the problem persists, please contact customer service.";
            result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
					Check({authorizationNumberFromLockBalance} != {ASITenantDriver.FAKE_TICKET_NUMBER}) Error '{errorMessage}';
				", $@"
				{{	
					targetCustomer = company.CustomerByIdentifier('{body.ToIdentifier}');
					{accountCommand}
					
					domain = company.Sales.DomainFrom('{domain}');

					drivers = company.System.DriverManagers.PaymentProcessorsAndActionsByDomains.SearchForX(domain.AgentId, domain.Url);
					processor = drivers.SearchByX({TransactionType.Transfer}, '{body.FromCurrencyCode}', {body.PaymentMethod.Value}, {body.EntityId});

					processorAccountId = guardian.Accounts().SearchBy(processor).Id;
					transaction = marketplace.SearchTemporaryDefinition({tempTransactionResponse.TransactionId}, '{path}', '{employeeName}').TransferTo(Now, itIsThePresent, Currency('{body.FromCurrencyCode}',{body.Amount}), {authorizationNumberFromLockBalance}, toAccount, '{employeeName}', '{body.RealAccount}', '{body.Description}', processor, processorAccountId);
					print transactionNumber authorizationId;
					print transaction.BatchTransactions.TransactionsNumber batchNumber;
					print Now now;
				}}
			");
            if (!(result is OkObjectResult))
            {
                return result;
            }
            o = (OkObjectResult)result;
            json = o.Value.ToString();
            DraftTransactionResponse idResponse = JsonConvert.DeserializeObject<DraftTransactionResponse>(json);

            return Ok(idResponse);
        }

        [HttpGet("api/wallet/exchangerate")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> ExchangeRateAsync(string fromCurrency, string toCurrency)
        {
            if (string.IsNullOrEmpty(fromCurrency)) return BadRequest("The 'fromCurrency' parameter is required.");
            if (string.IsNullOrEmpty(toCurrency)) return BadRequest("The 'toCurrency' parameter is required.");

            var response = await GetRateAsync(fromCurrency, toCurrency);

            if (response == RateResponse.FAKE_RATE_RESPONSE)
            {
                return BadRequest($"Could not retrieve the exchange rate from ${fromCurrency} to ${toCurrency}. Please try again later.");
            }

            return Ok(response);
        }

        [HttpGet("api/wallet/{currencyCode}/accounts/{accountNumber}/lastWithdrawalAddresses")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> LastWithdrawalAddressesAsync(string currencyCode, string accountNumber)
        {
            if (string.IsNullOrEmpty(currencyCode)) return BadRequest("The 'currencyCode' parameter is required.");
            if (string.IsNullOrEmpty(accountNumber)) return BadRequest("The 'accountNumber' parameter is required.");

            var response = await GetLastWithdrawalAddressessAsync(currencyCode, accountNumber);

            if (response == LastAddressResponse.FAKE_LAST_ADDRESS_RESPONSE)
            {
                return BadRequest($"Could not retrieve the address from {currencyCode} to {accountNumber}. Please try again later.");
            }

            return Ok(response);
        }



        [DataContract(Name = "WalletDepositCreationBody")]
        public class WalletDepositCreationBody
        {
            [DataMember(Name = "fromCurrencyCode")]
            public string FromCurrencyCode { get; set; }
            [DataMember(Name = "toCurrencyCode")]
            public string ToCurrencyCode { get; set; }
            [DataMember(Name = "amount")]
            public decimal Amount { get; set; }
            [DataMember(Name = "toIdentifier")]
            public string ToIdentifier { get; set; }
            [DataMember(Name = "accountNumber")]
            public string AccountNumber { get; set; }
            [DataMember(Name = "description")]
            public string Description { get; set; }
            [DataMember(Name = "voucher")]
            public string Voucher { get; set; }
            [DataMember(Name = "voucherurl")]
            public string VoucherUrl { get; set; }
            [DataMember(Name = "depositor")]
            public string Depositor { get; set; }

            [DataMember(Name = "sendersName")]
            public string SendersName { get; set; }
            [DataMember(Name = "country")]
            public string Country { get; set; }
            [DataMember(Name = "state")]
            public string State { get; set; }
            [DataMember(Name = "city")]
            public string City { get; set; }
            [DataMember(Name = "controlNum")]
            public string ControlNum { get; set; }
            [DataMember(Name = "providerId")]
            public int ProviderId { get; set; }
            [DataMember(Name = "paymentMethod")]
            public PaymentMethod? PaymentMethod { get; set; }
            [DataMember(Name = "entityId")]
            public int EntityId { get; set; }
        }

        [DataContract(Name = "IdWalletDepositResponse")]
        public class WalletTransactionResponse
        {
            [DataMember(Name = "authorizationId")]
            public long AuthorizationId { get; set; }
            [DataMember(Name = "batchNumber")]
            public int BatchNumber { get; set; }
            [DataMember(Name = "now")]
            public string Now { get; set; }

            [DataMember(Name = "destinationAddress")]
            public string DestinationAddress { get; set; }


            [DataMember(Name = "paymentLink")]
            public string PaymentLink { get; set; }
            [DataMember(Name = "amount")]
            public string Amount { get; internal set; }
            [DataMember(Name = "rate")]
            public string Rate { get; internal set; }
            [DataMember(Name = "isAmountInAgentRange")]
            public bool IsAmountInAgentRange { get; internal set; }
        }


        [DataContract(Name = "TransactionResponse")]
        public class TransactionResponse
        {
            [DataMember(Name = "authorizationId")]
            public int AuthorizationId { get; set; }
            [DataMember(Name = "batchNumber")]
            public int BatchNumber { get; set; }
            [DataMember(Name = "now")]
            public string Now { get; set; }

            [DataMember(Name = "domainId")]
            public int DomainId { get; set; }

            [DataMember(Name = "storeId")]
            public int StoreId { get; set; }

        }

        [DataContract(Name = "WalletWithdrawalCreationBody")]
        public class WalletWithdrawalCreationBody
        {
            [DataMember(Name = "accountNumber")]
            public string accountNumber { get; set; }

            [DataMember(Name = "fromCurrencyCode")]
            public string FromCurrencyCode { get; set; }
            [DataMember(Name = "amount")]
            public decimal Amount { get; set; }
            [DataMember(Name = "fromIdentifier")]
            public string FromIdentifier { get; set; }
            [DataMember(Name = "description")]
            public string Description { get; set; }
            [DataMember(Name = "realAccount")]
            public string RealAccount { get; set; }
            [DataMember(Name = "minerFee")]
            public decimal MinerFee { get; set; }
            [DataMember(Name = "paymentMethod")]
            public PaymentMethod? PaymentMethod { get; set; }
            [DataMember(Name = "entityId")]
            public int EntityId { get; set; }
            [DataMember(Name = "toCurrencyCode")]
            public string ToCurrencyCode { get; set; }
        }

        [DataContract(Name = "WalletTransferCreationBody")]
        public class WalletTransferCreationBody
        {

            [DataMember(Name = "toIdentifier")]
            public string ToIdentifier { get; set; }

            [DataMember(Name = "fromIdentifier")]
            public string FromIdentifier { get; set; }

            [DataMember(Name = "fromCurrencyCode")]
            public string FromCurrencyCode { get; set; }
            [DataMember(Name = "toCurrencyCode")]
            public string ToCurrencyCode { get; set; }
            [DataMember(Name = "amount")]
            public decimal Amount { get; set; }
            [DataMember(Name = "description")]
            public string Description { get; set; }
            [DataMember(Name = "realAccount")]
            public string RealAccount { get; set; }

            [DataMember(Name = "fromAccountNumber")]
            public string FromAccountNumber { get; set; }

            [DataMember(Name = "toAccountNumber")]
            public string ToAccountNumber { get; set; }
            [DataMember(Name = "paymentMethod")]
            public PaymentMethod? PaymentMethod { get; set; }
            [DataMember(Name = "entityId")]
            public int EntityId { get; set; }
        }

        [DataContract(Name = "WithdrawBody")]
        public class WithdrawBodyWallet
        {
            [DataMember(Name = "amount")]
            public decimal Amount { get; set; }
            [DataMember(Name = "destination")]
            public string Destination { get; set; }
            [DataMember(Name = "externalReference")]
            public string ExternalReference { get; set; }
            [DataMember(Name = "atAddress")]
            public string AtAddress { get; set; }
        }

        [DataContract(Name = "WithdrawResponse")]
        public class WithdrawResponse
        {
            [DataMember(Name = "withdrawalId")]
            public int WithdrawalId { get; set; }

        }

        [DataContract(Name = "QrCodeResponse")]
        public class QrCodeResponse
        {
            [DataMember(Name = "invoiceId")]
            public string InvoiceId { get; set; }

            [DataMember(Name = "destination")]
            public string Destination { get; set; }

            [DataMember(Name = "paymentLink")]
            public string PaymentLink { get; set; }

            [DataMember(Name = "amount")]
            public string Amount { get; set; }
            [DataMember(Name = "rate")]
            public string Rate { get; set; }

            public static QrCodeResponse FAKE_QR_RESPONSE = new QrCodeResponse
            {
                InvoiceId = string.Empty,
                Destination = string.Empty,
                PaymentLink = string.Empty
            };
        }

        [DataContract(Name = "LastAddressResponse")]
        public class LastAddressResponse
        {
            [DataMember(Name = "addresses")]
            public List<string> Addresses { get; set; }

            public static LastAddressResponse FAKE_LAST_ADDRESS_RESPONSE = new LastAddressResponse
            {
                Addresses = new List<string>()
            };
        }

        [DataContract(Name = "RateResponse")]
        public class RateResponse
        {

            [DataMember(Name = "rate")]
            public string Rate { get; set; }

            public static RateResponse FAKE_RATE_RESPONSE = new RateResponse
            {
                Rate = string.Empty,
            };
        }

        [DataContract(Name = "DraftDepositResponse")]
        public class DraftDepositResponse
        {
            [DataMember(Name = "depositId")]
            public int DepositId { get; set; }
            [DataMember(Name = "authorizationId")]
            public int AuthorizationId { get; set; }

            public static DraftDepositResponse FAKE_RESPONSE = new DraftDepositResponse
            {
                DepositId = -1,
                AuthorizationId = -1,
            };

        }

        [DataContract(Name = "DisbursementConfig")]
        public class DisbursementConfig
        {
            [DataMember(Name = "areAllWithdrawalsManagedAsDisbursements")]
            public bool AreAllWithdrawalsManagedAsDisbursements { get; set; }

        }

    }
}
