﻿using GamesEngine.Business.Liquidity.Transactions;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using static GamesEngine.Business.Liquidity.Containers.Tanker;
using static GamesEngine.Business.Liquidity.Containers.Tanker.TankerDispatched;

namespace GamesEngine.Business.Liquidity.Sentinels.Inbound
{
    internal class TankerInboundTask : InboundTask
    {
        internal TankerSealed Tanker { get; private set; }

        internal TankerInboundTask(TankerSealed tanker)
        {
            if (tanker == null) throw new ArgumentNullException(nameof(tanker));
            Tanker = tanker;
        }

        internal CancellationTokenSource Cancellation { get; private set; } = new();

        internal void Undisburden()
        {
            if (Cancellation.IsCancellationRequested) return;
            Cancellation.Cancel();
        }

        private IEnumerable<Deposit> currentDeposits;
        internal override void StartInboundTask(bool itIsThePresent)
        {
            if (Cancellation.IsCancellationRequested) Cancellation = new CancellationTokenSource();

            task = Task.Run(async () =>
            {
                try
                {
                    currentDeposits = Tanker.Deposits;
                    if (!currentDeposits.Any()) throw new GameEngineException($"The Tanker {Tanker.Id} has no deposits to download.");
                    await RefreshDepositCurrentBalancesAsync();

                    int depositIndex = 0;
                    while (!Cancellation.IsCancellationRequested)
                    {
                        bool depositChange = false;
                        var currentDeposit = currentDeposits.ElementAt(depositIndex);
                        if (depositChainAmounts.TryGetValue(currentDeposit, out var amountInChain))
                        {
                            try
                            {
                                var utxoAddress = currentDeposit.Destination;
                                var addressInformation = await PaymentManager.NodeExplorerClient.AddressBalanceInfoAsync(utxoAddress, currentDeposit.Kind);

                                bool totalChanged = amountInChain.Total != addressInformation.Total;
                                bool availableChanged = amountInChain.Available != addressInformation.Available;
                                depositChange = totalChanged || availableChanged;
                            }
                            catch
                            {
                                Loggers.GetIntance().Sentinel.Debug($"Failed to get address information for deposit {currentDeposit.Id} at index {depositIndex}.");
                            }
                        }

                        if (depositChange)
                        {
                            await RefreshDepositCurrentBalancesAsync();
                            if (Integration.UseKafka)
                            {
                                using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, Integration.Kafka.TopicForLiquidityEvents))
                                {
                                    DispatchTankerMessage message = new DispatchTankerMessage(Tanker.Id, Tanker.Kind);
                                    buffer.Send(message);
                                }
                            }
                            await Cancellation.CancelAsync();
                            break;
                        }

                        depositIndex++;
                        if (depositIndex >= currentDeposits.Count()) depositIndex = 0;// Reset to the first deposit
                        await Task.Delay(3000);
                    }
                }
                catch(Exception e)
                {
                    Loggers.GetIntance().Sentinel.Error($"Error in TankerInboundTask for Tanker {Tanker.Id} - {Tanker.Name}.", e);
                }
            });
        }

        private ConcurrentDictionary<Deposit, (long Total, long Available)> depositChainAmounts = new();
        private async Task RefreshDepositCurrentBalancesAsync()
        {
            if (currentDeposits != null)
            {
                foreach (var deposit in currentDeposits)
                {
                    if (Cancellation.IsCancellationRequested) break;

                    if (!depositChainAmounts.TryGetValue(deposit, out var currentAmountInChain))
                    {
                        currentAmountInChain.Total = 0;
                        currentAmountInChain.Available = 0;
                        depositChainAmounts.TryAdd(deposit, currentAmountInChain);
                    }

                    try
                    {
                        var addressInformation = await PaymentManager.NodeExplorerClient.AddressBalanceInfoAsync(deposit.Destination, deposit.Kind);
                        currentAmountInChain.Total = addressInformation.Total;
                        currentAmountInChain.Available = addressInformation.Available;
                        depositChainAmounts[deposit] = currentAmountInChain;

                        decimal currentTotalBTC = SatochisToBTC(currentAmountInChain.Total);
                        bool depositFundsStillTheSame = currentTotalBTC == deposit.Amount;
                        if (depositFundsStillTheSame)
                        {
                            // DEPOSIT FUNDS HAS NOT CHANGED, IS HAPPY PATH.
                            decimal remainingBalance = SatochisToBTC(currentAmountInChain.Available);
                            if (remainingBalance == 0)
                            {
                                // Happy path, the deposit amount matches the current balance in the chain.
                            }
                            else if (remainingBalance > 0)
                            {
                                // The deposit amount is less than the current balance in the chain.
                                // This means that the deposit has been partially spent or there are additional funds
                                //tankerCanBeArchived = false;
                            }
                            else if (remainingBalance < 0)
                            {
                                // The deposit amount is greater than the current balance in the chain.
                                // This means that the deposit has been fully spent or there are no additional funds.
                                //tankerCanBeArchived = false;
                            }
                        }
                        else
                        {
                            //tankerCanBeArchived = false;
                            // DEPOSIT FUNDS HAS CHANGED, IS NOT THE SAME AS WAS BEFORE.                            
                        }
                    }
                    catch (Exception ex)
                    {
                        Loggers.GetIntance().Sentinel.Error($"Failed to get address information for deposit {deposit.Id}.", ex);
                    }
                }
            }
        }

        private static decimal SatochisToBTC(long satochis)
        {
            if (satochis < 0) throw new ArgumentException("Satochis cannot be negative.", nameof(satochis));
            return satochis / 100_000_000M; // 1 BTC = 100,000,000 Satoshis
        }

        internal class DepositChainStatus : Objeto
        {
            internal int Id { get; private set; }
            internal string Destination { get; private set; }
            internal decimal Total { get; private set; }
            internal decimal Available { get; private set; }
            internal DepositChainStatus(int id, string destination, long total, long available)
            {
                Id = id;
                Destination = destination;
                Total = SatochisToBTC(total);
                Available = SatochisToBTC(available);
            }
        }

        internal IEnumerable<DepositChainStatus> DepositChainStatuses
        {
            get
            {
                List<DepositChainStatus> results = new List<DepositChainStatus>();
                foreach (var deposit in depositChainAmounts)
                {
                    results.Add(new DepositChainStatus(deposit.Key.Id, deposit.Key.Destination, deposit.Value.Total, deposit.Value.Available));
                }
                return results;
            }
        }

        public override string ToString()
        {
            int totalDeposits = 0;
            if (currentDeposits != null)
            {
                totalDeposits = currentDeposits.Count();
            }
            return $"Task for Tanker {Tanker.Id} - {Tanker.Name} with {totalDeposits} deposits and {depositChainAmounts.Count} chain amounts.";
        }
    }
}
