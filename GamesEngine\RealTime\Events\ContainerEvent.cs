﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamesEngine.RealTime.Events
{
    internal abstract class ContainerChangeEvent : PlatformEvent
    {
        public int Id { get; set; }

        internal ContainerChangeEvent(DateTime timeStamp, int id, PlatformEventType eventType) : base(timeStamp, eventType)
        {
            if (id <= 0) throw new ArgumentOutOfRangeException(nameof(id), "Tank ID must be greater than zero.");

            Id = id;
        }

        internal ContainerChangeEvent(int id, PlatformEventType eventType) : base(eventType)
        {
            if (id <= 0) throw new ArgumentOutOfRangeException(nameof(id), "Tank ID must be greater than zero.");

            Id = id;
        }
    }
}
