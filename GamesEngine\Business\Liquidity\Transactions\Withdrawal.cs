﻿using GamesEngine.Domains;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using System;

namespace GamesEngine.Business.Liquidity.Transactions
{
    internal class Withdrawal : Transaction
    {
        internal decimal Amount { get; private set; }
        internal decimal ReceivedAmount { get; private set; }
        
        internal int Reference { get; private set; }

        internal string ExternalReference { get; private set; }

        internal string Destination { get; private set; }

        internal string AtAddress { get; private set; }

        internal string PullPaymentId { get; private set; }

        internal int Authorization { get; private set; }
        internal int StoreId { get; private set; }
        internal Domain Domain { get; private set; }

        public Withdrawal(int id, string pullPaymentId, int authorization, decimal amount, DateTime createdAt, string destination, int reference, string atAddress, Domain domain, int storeId, string externalReference):base(id, createdAt)
        {
            if (id <= 0) throw new ArgumentNullException(nameof(id));
            if (string.IsNullOrWhiteSpace(pullPaymentId)) throw new ArgumentNullException(nameof(pullPaymentId));
            if (authorization <= 0) throw new ArgumentNullException(nameof(authorization));
            if (amount <= 0) throw new ArgumentNullException(nameof(amount));
            if (createdAt == DateTime.MinValue) throw new ArgumentNullException(nameof(createdAt));
            if (string.IsNullOrWhiteSpace(destination)) throw new ArgumentNullException(nameof(destination));
            if (reference <= 0) throw new ArgumentNullException(nameof(reference));
            if (string.IsNullOrWhiteSpace(atAddress)) throw new ArgumentNullException(nameof(atAddress));
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (storeId == 0) throw new ArgumentNullException(nameof(storeId));
            if (string.IsNullOrWhiteSpace(externalReference)) throw new ArgumentNullException(nameof(externalReference));

            PullPaymentId = pullPaymentId;
            Authorization = authorization;
            Amount = amount;
            Destination = destination;
            Reference = reference;
            AtAddress = atAddress;
            Domain = domain;
            StoreId = storeId;
            ExternalReference = externalReference;
        }

        public Withdrawal(int id, string pullPaymentId, int authorization, decimal amount, DateTime createdAt, string destination, int reference, string atAddress, Domain domain, int storeId, string externalReference, decimal rate, decimal receivedAmount) : base(id, createdAt, rate)
        {
            if (id <= 0) throw new ArgumentNullException(nameof(id));
            if (string.IsNullOrWhiteSpace(pullPaymentId)) throw new ArgumentNullException(nameof(pullPaymentId));
            if (authorization <= 0) throw new ArgumentNullException(nameof(authorization));
            if (amount <= 0) throw new ArgumentNullException(nameof(amount));
            if (createdAt == DateTime.MinValue) throw new ArgumentNullException(nameof(createdAt));
            if (string.IsNullOrWhiteSpace(destination)) throw new ArgumentNullException(nameof(destination));
            if (reference <= 0) throw new ArgumentNullException(nameof(reference));
            if (string.IsNullOrWhiteSpace(atAddress)) throw new ArgumentNullException(nameof(atAddress));
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (storeId == 0) throw new ArgumentNullException(nameof(storeId));
            if (string.IsNullOrWhiteSpace(externalReference)) throw new ArgumentNullException(nameof(externalReference));

            PullPaymentId = pullPaymentId;
            Authorization = authorization;
            Amount = amount;
            Destination = destination;
            Reference = reference;
            AtAddress = atAddress;
            Domain = domain;
            StoreId = storeId;
            ExternalReference = externalReference;
            ReceivedAmount = receivedAmount;
        }
    }
}
