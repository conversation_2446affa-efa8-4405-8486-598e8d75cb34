using GamesEngine.Business.Liquidity;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;

namespace GamesEngineTests.Unit_Tests.Business;

[TestClass]
public class MessagesModelTests
{
    private const char PROPERTIES_SEPARATOR = '\uFFFA';

    [TestMethod]
    public void Deserialize_WithProvidedMessageString_ParsesAllFieldsCorrectly()
    {
        // Arrange
        var messageParts = new string[] { "A", "100025", "BTC", "addr_bb49b976", "tb1qunl3gvscjmfzvcpufp2xqpf7d3yft4hxm7cfun", "5", "109108.1", "1469", "JAR_BTC_V1469", "1", "2025", "9", "1", "11", "16", "27" };
        var serializedMessage = string.Join(PROPERTIES_SEPARATOR, messageParts);

        var expectedDepositId = 100025;
        var expectedKind = "BTC";
        var expectedAddress = "addr_bb49b976";
        var expectedTransactionId = "tb1qunl3gvscjmfzvcpufp2xqpf7d3yft4hxm7cfun";
        var expectedAmount = 5m;
        var expectedRate = 109108.1m;
        var expectedJarVersion = 1469;
        var expectedAccount = "JAR_BTC_V1469";
        var expectedDomainId = 1;
        var expectedDraftDate = new DateTime(2025, 9, 1, 11, 16, 27);

        // Act
        // This is the action under test: constructing the message from the string.
        var message = new DraftDepositMessage(serializedMessage);

        // Assert
        // Verify that every single property was parsed correctly.
        Assert.AreEqual(expectedDepositId, message.DepositId);
        Assert.AreEqual(expectedKind, message.Kind);
        Assert.AreEqual(expectedAddress, message.Address);
        Assert.AreEqual(expectedTransactionId, message.TransactionId);
        Assert.AreEqual(expectedAmount, message.Amount);
        Assert.AreEqual(expectedRate, message.Rate);
        Assert.AreEqual(expectedJarVersion, message.JarVersion);
        Assert.AreEqual(expectedAccount, message.Account);
        Assert.AreEqual(expectedDomainId, message.DomainId);
        Assert.AreEqual(expectedDraftDate, message.DraftDate);
    }

    [TestMethod]
    public void Serialize_And_Deserialize_RoundTrip_ProducesIdenticalObjectAndString()
    {
        // Arrange
        var originalMessage = new DraftDepositMessage(
            depositId: 100025,
            kind: "BTC",
            address: "addr_bb49b976",
            transactionId: "tb1qunl3gvscjmfzvcpufp2xqpf7d3yft4hxm7cfun",
            amount: 5m,
            createdAt: new DateTime(2025, 9, 1, 11, 16, 27),
            jarVersion: 1469,
            account: "JAR_BTC_V1469",
            domainId: 1,
            rate: 109108.1m
        );

        var expectedSerializedMessage = $"A{PROPERTIES_SEPARATOR}100025{PROPERTIES_SEPARATOR}BTC{PROPERTIES_SEPARATOR}addr_bb49b976{PROPERTIES_SEPARATOR}tb1qunl3gvscjmfzvcpufp2xqpf7d3yft4hxm7cfun{PROPERTIES_SEPARATOR}5{PROPERTIES_SEPARATOR}109108.1{PROPERTIES_SEPARATOR}1469{PROPERTIES_SEPARATOR}JAR_BTC_V1469{PROPERTIES_SEPARATOR}1{PROPERTIES_SEPARATOR}2025{PROPERTIES_SEPARATOR}9{PROPERTIES_SEPARATOR}1{PROPERTIES_SEPARATOR}11{PROPERTIES_SEPARATOR}16{PROPERTIES_SEPARATOR}27";

        // Act
        // 1. Serialize the object into a string.
        var serializedString = originalMessage.Serialize();
        // 2. Deserialize that string back into a new object.
        var deserializedMessage = new DraftDepositMessage(serializedString);

        // Assert
        // 1. Verify that the serialization produced the exact string we expected.
        Assert.AreEqual(expectedSerializedMessage, serializedString);

        // 2. Verify that the new, deserialized object is identical to the original.
        Assert.AreEqual(originalMessage.DepositId, deserializedMessage.DepositId);
        Assert.AreEqual(originalMessage.Kind, deserializedMessage.Kind);
        Assert.AreEqual(originalMessage.Address, deserializedMessage.Address);
        Assert.AreEqual(originalMessage.TransactionId, deserializedMessage.TransactionId);
        Assert.AreEqual(originalMessage.Amount, deserializedMessage.Amount);
        Assert.AreEqual(originalMessage.Rate, deserializedMessage.Rate);
        Assert.AreEqual(originalMessage.JarVersion, deserializedMessage.JarVersion);
        Assert.AreEqual(originalMessage.Account, deserializedMessage.Account);
        Assert.AreEqual(originalMessage.DomainId, deserializedMessage.DomainId);
        Assert.AreEqual(originalMessage.DraftDate, deserializedMessage.DraftDate);
    }
}
