﻿using System;
using System.Globalization;
using System.Text;
using System.Threading;
using Elastic.Apm.AspNetCore;
using Elastic.Apm.NetCoreAll;
using ExchangeAPI.Logic;
using ExternalServices;
using GamesEngine;
using GamesEngine.MessageQueuing;
using GamesEngine.Custodian;
using GamesEngine.Settings;
using GamesEngineMocks;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using Puppeteer.EventSourcing;

namespace ExchangeAPI
{
    public class Startup
    {
        public Startup(IWebHostEnvironment env, IConfiguration configuration)
        {
            var builder = new ConfigurationBuilder()
                .SetBasePath(env.ContentRootPath)
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile("customization/appsettings.accounting.json", optional: true)
                .AddJsonFile("customization/appsettings.security.json", optional: true)
                .AddJsonFile("customization/appsettings.kafka.json", optional: true)
                .AddJsonFile("customization/appsettings.error_emails.json", optional: true)
                .AddJsonFile("customization/appsettings.apm.json", optional: true)
                .AddJsonFile("secrets/appsettings.secrets.json", optional: true);
            Configuration = builder.Build();
            Environment = env;
        }

        public IConfiguration Configuration { get; }
        public IWebHostEnvironment Environment { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            int minWorker, minIOC;
            ThreadPool.GetMinThreads(out minWorker, out minIOC);
            int maxWorker, maxIOC;
            ThreadPool.GetMaxThreads(out maxWorker, out maxIOC);

            Console.WriteLine($@" minWorker:{minWorker} minIOC:{minIOC} maxWorker:{maxWorker} maxIOC:{maxIOC}");

            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "Exchange API", Version = "v1" });
            });

            /*Security*/
            Security.Configure(services, Configuration);
            /*Security*/

            /*WebHookConfiguration*/
            WebHookClient.Configure(Configuration);
            /*WebHookConfiguration*/

            /*TownSettings*/
            TownSettings.Configure(Configuration);
            /*TownSettings*/

            /*ProxyEndpointsSettings*/
            ProxyEndpointsSettings.Configure(Configuration);
            /*ProxyEndpointsSettings*/

            services.AddMvc(options => options.EnableEndpointRouting = false).AddNewtonsoftJson();

            if (string.IsNullOrEmpty(Configuration.GetValue<string>("CashierUrl"))) throw new Exception("CashierUrl its requeried in the appsettings.");
            Settings.CashierUrl = Configuration.GetValue<string>("CashierUrl");

            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
            var biIntegration = Configuration.GetSection("BIIntegration");

            AccountingSettings.NeedsUniqueIdentifierForPaymentHub = "STAGE_NEED_IT";

            Integration.Configure(KafkaMessage.Prefix.withExchangePrefix, biIntegration);

            ErrorsSender.Configure(Configuration.GetSection("ErrorsSender"), Environment.IsProduction());
            Integration.ConfigureAPM(Configuration);

            var messageOriginId = Configuration.GetValue<string>("MessageOriginId");
            if (string.IsNullOrEmpty(messageOriginId) && !int.TryParse(messageOriginId, out _)) throw new Exception("MessageOriginId its requeried in the appsettings.");
            MessageCatalog.AssingMessageOriginId(Convert.ToInt32(messageOriginId));
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (Integration.UseAPM)
            {
                app.UseElasticApm(Configuration);
            }

            app.UseSwagger();
            app.UseSwaggerUI(c =>
            {
                c.SwaggerEndpoint("/swagger/v1/swagger.json", "Exchange API V1");
                c.RoutePrefix = string.Empty;
            });

            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }
            else
            {
                if (String.IsNullOrEmpty(Integration.Db.DBSelected))
                {
                    throw new Exception("Db configuration its required.");
                }
                if (!Integration.UseKafka)
                {
                    throw new Exception("This project must use kafka integration to show data.");
                }
            }
            var sectionDairy = Configuration.GetSection("DBDairy");
            var mySQL = sectionDairy.GetSection("ConnectionStrings").GetValue<string>("MySQL");
            var sqlServer = sectionDairy.GetSection("ConnectionStrings").GetValue<string>("SQLServer");
            var dbSelected = sectionDairy.GetValue<string>("DBSelected");
            var scriptBeforeRecovering = Configuration.GetValue<string>("ActionsBeforeRecovering");

            Settings.DbSelected = dbSelected;
            Settings.ScriptBeforeRecovering = scriptBeforeRecovering;
            Settings.MySQL = mySQL;
            Settings.SqlServer = sqlServer;

            if (dbSelected == DatabaseType.MySQL.ToString())
            {
                ExchangeManagerAPI.ExchangeManager.EventSourcingStorage(DatabaseType.MySQL, mySQL, scriptBeforeRecovering, AccountingSettings.NeedsUniqueIdentifierForPaymentHub);
            }
            else if (dbSelected == DatabaseType.SQLServer.ToString())
            {
                ExchangeManagerAPI.ExchangeManager.EventSourcingStorage(DatabaseType.SQLServer, sqlServer, scriptBeforeRecovering, AccountingSettings.NeedsUniqueIdentifierForPaymentHub);
            }
            else if (!String.IsNullOrWhiteSpace(dbSelected))
            {
                throw new Exception($"There is no connection for {dbSelected}");
            }
            else
            {
                Integration.Kafka.OffSetResetToLatest();
                int numberOfTheMockConfigured = Convert.ToInt32(Configuration["MockToStart"]);
                
                var DOTNET_RUNNING_IN_CONTAINER = bool.Parse(System.Environment.GetEnvironmentVariable("DOTNET_RUNNING_IN_CONTAINER") ?? "false");
                if (DOTNET_RUNNING_IN_CONTAINER)
                {
                    ExchangeManagerAPI.ExchangeManager.EventSourcingStorage(DatabaseType.MySQL, mySQL, scriptBeforeRecovering, AccountingSettings.NeedsUniqueIdentifierForPaymentHub);
                }

                RunMock(numberOfTheMockConfigured);
            }
            DriverController.RestActor = ExchangeManagerAPI.ExchangeManager;
            Integration.ExchangeUsers = new ExchangeUsers();
            Integration.ExchangeRates = new ExchangeRates();
            Integration.Deposits = new Deposits();
            Integration.Withdrawals = new Withdrawals();
            Integration.Transfers = new Transfers();
            Integration.Domains = new Domains();

            /*Security*/
            app.UseAuthentication();
            /*Security*/

            app.UseMvc();

            if (Integration.UseKafka)
            {
                new Consumers().CreateConsumerForTopics();
            }

            ForwardedHeadersOptions options = new ForwardedHeadersOptions();
            options.ForwardedHeaders = ForwardedHeaders.XForwardedFor;
            app.UseForwardedHeaders(options);

            var result = ExchangeManagerAPI.ExchangeManager.PerformQry($@"
                {{
					accounts = guardian.Accounts();
                    print accounts.NextAccountId() accountId;
					for (processors:guardian.PaymentProcessorsWithoutAccounts())
					{{
						processor = processors;
						print processor.Entity.Name entity;
						print processor.PaymentMethodAsText paymentMethod;
						print processor.Transactions.TransactionsAsText transactionType;
						print processor.CurrencyIso4217Code currencyCode;
					}}
                }}
                ");

            if (!(result is OkObjectResult)) throw new GameEngineException($"{result}");
            OkObjectResult o = (OkObjectResult)result;

            string json = o.Value.ToString();
            var processorsWithoutAccounts = JsonConvert.DeserializeObject<ProcessorsWithoutAccounts>(json);

            var commandsToCreateAccounts = new StringBuilder();
            if (processorsWithoutAccounts != null && processorsWithoutAccounts.Processors?.Count > 0)
            {
                int accountId = processorsWithoutAccounts.AccountId;
                foreach (var processorWithoutAccounts in processorsWithoutAccounts.Processors)
                {
                    commandsToCreateAccounts.Append("accounts.Create(false, ").
                        Append(accountId).Append(", '").
                        Append(processorWithoutAccounts.ProcessorKey).Append("', '").
                        Append(processorWithoutAccounts.ProcessorAccount(accountId)).Append("', '").
                        Append(processorWithoutAccounts.CurrencyCode).Append("');");
                    accountId++;
                }
                result = ExchangeManagerAPI.ExchangeManager.PerformCmd($@"
				accounts = guardian.Accounts();
                {commandsToCreateAccounts}
                ");

                if (!(result is OkObjectResult)) throw new GameEngineException($"{result}");
            }
        }

        void RunMock(int index = -1)
        {
            switch (index)
            {
                case 0:
                    ExchangeMocks.OnlyBasicStuff(ExchangeManagerAPI.ExchangeManager.Actor);
                    break;
                case 1:
                    ExchangeMocks.CustomerWith1000BTC(ExchangeManagerAPI.ExchangeManager.Actor);
                    break;
                case 2:
                    ExchangeMocks.ArtemisLottoMock(ExchangeManagerAPI.ExchangeManager.Actor);
                    break;
                case 3:
                    ExchangeMocks.ZeusKenoMock(ExchangeManagerAPI.ExchangeManager.Actor);
                    break;
                case 4:
                    ExchangeMocks.ConsignmentMock(ExchangeManagerAPI.ExchangeManager.Actor);
                    break;
                default:
                    throw new Exception($"There is no mock for {Integration.MockToStart}");
            }
        }
    }

}
