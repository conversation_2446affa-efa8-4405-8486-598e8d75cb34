﻿using GamesEngine;
using GamesEngine.Business;
using GamesEngine.Domains;
using GamesEngine.Finance;
using GamesEngine.Gameboards;
using GamesEngine.Gameboards.Lotto;
using GamesEngine.Games.Lotto;
using GamesEngine.MessageQueuing;
using GamesEngine.Settings;
using GamesEngine.Time;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using static GamesEngine.Gameboards.Lotto.Ticket;
using static GamesEngine.Games.Lotto.Lottery;

namespace GamesEngine.Games.Lotto
{
	class SenderOfHistoricalPicks : SenderOfHistorical
    {
        
        internal SenderOfHistoricalPicks(Lottery lottery, bool itIsThePresent) : base(lottery, itIsThePresent)
        {
            if (lottery == null) throw new ArgumentNullException(nameof(lottery));

            grading = new MessagesBuffer<TypedMessage>(Integration.Kafka.TopicForLottoGrading, RuntimeSettings.BLOCK_SIZE_LOTTO, itIsThePresent);
        }

        internal override void WriteLoserData(DateTime date, Ticket ticket)
        {
            if (!itIsThePresent) return;

            if (ticket == null) throw new ArgumentNullException(nameof(ticket));
            if (!(ticket.IsLoser() || ticket.IsRegraded())) throw new GameEngineException($"Ticket is in {ticket.Prizing} and it is supposed to be loser ticket");
            if (ticket.TicketNumber != Ticket.DEFAULT_UNASSIGNED_TICKET_NUMBER)
            {
                var profit = !ticket.IsRegraded() ? ticket.Profit() : 0m;

                LoserInfo info = new LoserInfo(
                    company: this.lottery.Company,
                    accountNumber: ticket.Player.AccountNumber, 
                    ticket: ticket.AsString(), 
                    countOfTickets: ticket.Count, 
                    amount: ticket.BetAmount(), 
                    selectionMode: ticket.SelectionMode, 
                    action: ticket.Grading, 
                    drawingName: string.Empty,
                    creation: ticket.CreationDate, 
                    orderNumber: ticket.Order.Number,
                    ticketNumber: ticket.TicketNumber, 
                    subticketsAndWagerNumbers: ticket.SubticketsAndWagerNumbersAsString(),
                    profit: profit,
                    prizesVersion: ticket.Prizes.VersionNumber,
                    domainId: ticket.DomainId,
                    domainUrl: string.Empty,
                    currencyId: Coinage.Coin(ticket.Order.CurrencyCode).Id
                );

                grading.Add(info);
                ticket.IsMarkedAsSent = true;
            }
        }

        internal void WriteLoserData(LoserInfo commonInfo, Ticket ticket)
        {
            if (!itIsThePresent) return;

            if (ticket == null) throw new ArgumentNullException(nameof(ticket));
            if (! ticket.IsLoser()) throw new GameEngineException($"Ticket is in {ticket.Prizing} and it is supposed to be loser ticket");
            var ticketNumber = ticket.TicketNumber;
            if (ticketNumber != Ticket.DEFAULT_UNASSIGNED_TICKET_NUMBER)
            {
                LoserInfo info = new LoserInfo(commonInfo);
                info.SetValuesFromTicket(ticket);

                grading.Add(info);
                ticket.IsMarkedAsSent = true;
            }
        }

        internal override void WriteWinnerData(DateTime date, Ticket ticket)
        {
            if (!itIsThePresent) return;

            if (ticket == null) throw new ArgumentNullException(nameof(ticket));
            if (! ticket.IsWinner()) throw new GameEngineException($"Ticket is in {ticket.Prizing} and it is supposed to be winner ticket");
            if (ticket.TicketNumber != Ticket.DEFAULT_UNASSIGNED_TICKET_NUMBER)
            {
                var wonAmount = ticket.WonAmount();
                var profit = ticket.Profit();

                WinnerInfo info = new WinnerInfo(
                    company: this.lottery.Company,
                    prize: wonAmount,
                    accountNumber: ticket.Player.AccountNumber, 
                    ticket: ticket.AsString(), 
                    countOfTickets: ticket.Count, 
                    amount: ticket.BetAmount(), 
                    selectionMode: ticket.SelectionMode, 
                    action: ticket.Grading, 
                    drawingName: string.Empty,
                    creation: ticket.CreationDate,
                    orderNumber: ticket.Order.Number,
                    ticketNumber: ticket.TicketNumber, 
                    subticketsAndWagerNumbers: ticket.SubticketsAndWagerNumbersAsString(),
                    profit:profit,
                    prizesVersion: ticket.Prizes.VersionNumber,
                    domainId: ticket.DomainId,
                    domainUrl: string.Empty,
                    currencyId: Coinage.Coin(ticket.Order.CurrencyCode).Id
                );

                grading.Add(info);
                ticket.IsMarkedAsSent = true;
            }
        }

        internal override void WriteNoActionData(DateTime date, Ticket ticket)
        {
            if (!itIsThePresent) return;

            if (ticket == null) throw new ArgumentNullException(nameof(ticket));
            if (! ticket.IsNoAction()) throw new GameEngineException($"Ticket is in {ticket.Grading} and it is supposed to be {GameboardStatus.NOACTION} when it is marked as no action");
            if (ticket.TicketNumber != Ticket.DEFAULT_UNASSIGNED_TICKET_NUMBER)
            {
                var info = new NoActionInfo(
                    company: this.lottery.Company,
                    accountNumber: ticket.Player.AccountNumber, 
                    ticket: ticket.AsString(), 
                    countOfTickets: ticket.Count, 
                    amount: ticket.BetAmount(), 
                    selectionMode: ticket.SelectionMode, 
                    action: ticket.Grading, 
                    drawingName: string.Empty,
                    creation: ticket.CreationDate, 
                    orderNumber: ticket.Order.Number,
                    ticketNumber: ticket.TicketNumber, 
                    subticketsAndWagerNumbers: ticket.SubticketsAndWagerNumbersAsString(), 
                    prizesVersion: ticket.Prizes.VersionNumber,
                    domainId: ticket.DomainId,
                    domainUrl: string.Empty,
                    currencyId: Coinage.Coin(ticket.Order.CurrencyCode).Id
                );

                grading.Add(info);
                ticket.IsMarkedAsSent = true;
            }
        }

        internal void WriteGeneralTicketData(int uniqueDrawingId, int drawingId, DateTime drawDate, string draw, int fireball, string drawingDescription, IdOfLottery IdOfLottery, TypeNumberSequence position, IEnumerable<Domain> domains)
		{
            if (drawingId <= 0) throw new GameEngineException("Invalid draw id");
            if (string.IsNullOrWhiteSpace(draw)) throw new ArgumentNullException(nameof(draw));
            if (string.IsNullOrWhiteSpace(drawingDescription)) throw new ArgumentNullException(nameof(drawingDescription));
            if (domains == null) throw new GameEngineException("Domains should contains at least one element");

            WinnerResultsInfo winnerDrawings = new WinnerResultsInfo(GradeTicketType.WINNER_RESULTS, draw, fireball, drawingId, drawDate.Year, drawDate.Month, drawDate.Day, drawDate.Hour, drawDate.Minute);
            this.grading.Add(winnerDrawings);
            DrawingMessage drawingMsg = new DrawingMessage(GradeTicketType.DRAWINGS, drawingId, uniqueDrawingId, drawingDescription, IdOfLottery, position);
            this.grading.Add(drawingMsg);
            foreach (var domain in domains)
			{
                var domainsMsg = new DomainBIMessage(domain.Id, domain.Url, domain.Alias);
                this.grading.Add(domainsMsg);
            }
            
        }

        internal void StartSending(string stateAbb, DateTime drawDate, IdOfLottery idOfLottery, int drawingId, string gradedBy, string draw, int fireball)
        {
            var headerInfo = new TicketsStreamStartingMessage(GradeTicketType.STARTING_TICKET, stateAbb, drawDate, idOfLottery, drawingId, gradedBy, draw, fireball);
            this.grading.Add(headerInfo);
        }

        internal void EndSending(string stateAbb, DateTime drawDate, IdOfLottery idOfLottery)
        {
            var headerInfo = new TicketsStreamEndingMessage(GradeTicketType.ENDING_TICKET, stateAbb, drawDate, idOfLottery);
            this.grading.Add(headerInfo);
        }

	}

    public abstract class TicketPicksMessage : TicketMessage
    {
        private string stateAbb;
        private int countOfTickets;
        private Ticket.Selection selectionMode;
        private string drawingName;
        private string subticketsAndWagerNumbers;


        public TicketPicksMessage(string message) : base(message)
        {
            
        }

        internal TicketPicksMessage(string stateAbb, int hour, int minute, int year, int month, int day, string accountNumber, string ticket, int countOfTickets, decimal amount, Ticket.Selection selectionMode, 
            GameboardStatus action, int drawingId, TypeNumberSequence position, string drawingName, DateTime creation, int orderNumber, int ticketNumber, string subticketsAndWagerNumbers, int prizesVersion, int domainId, string domainUrl, 
            int currencyId, GradeTicketType gradeType)
            : base(gradeType)
        {
            this.stateAbb = stateAbb;
            this.Hour = hour;
            this.Minute = minute;
            this.Year = year;
            this.Month = month;
            this.Day = day;
            this.AccountNumber = accountNumber;
            this.Ticket = ticket;
            this.countOfTickets = countOfTickets;
            this.Amount = amount;
            this.selectionMode = selectionMode;
            this.Action = action;
            this.DrawingId = drawingId;
            this.Position = position;
            this.drawingName = drawingName;
            this.Creation = creation;
            OrderNumber = orderNumber;
            this.TicketNumber = ticketNumber;
            this.subticketsAndWagerNumbers = subticketsAndWagerNumbers;
            this.PrizesVersion = prizesVersion;
            DomainId = domainId;
            DomainUrl = domainUrl;
            CurrencyId = currencyId;
        }

        public TicketPicksMessage(string accountNumber, string ticket, int countOfTickets, decimal amount, Ticket.Selection selectionMode,
            GameboardStatus action, string drawingName, DateTime creation, int orderNumber, int ticketNumber, string subticketsAndWagerNumbers, int prizesVersion, int domainId, string domainUrl, int currencyId, 
            GradeTicketType gradeType)
            : base(gradeType)
        {
            this.AccountNumber = accountNumber;
            this.Ticket = ticket;
            this.countOfTickets = countOfTickets;
            this.Amount = amount;
            this.selectionMode = selectionMode;
            this.Action = action;
            this.drawingName = drawingName;
            this.Creation = creation;
            OrderNumber = orderNumber;
            this.TicketNumber = ticketNumber;
            this.subticketsAndWagerNumbers = subticketsAndWagerNumbers;
            this.PrizesVersion = prizesVersion;
            DomainId = domainId;
            DomainUrl = domainUrl;
            CurrencyId = currencyId;
        }

        protected void CompleteInfo(string stateAbb, DateTime drawDate, int drawingId)
        {
            this.StateAbb = stateAbb;
            this.Year = drawDate.Year;
            this.Month = drawDate.Month;
            this.Day = drawDate.Day;
            this.Hour = drawDate.Hour;
            this.Minute = drawDate.Minute;
            this.DrawingId = drawingId;
        }

        protected override void Deserialize(string [] serializatedMessage, out int fieldOrder)
        {
            base.Deserialize(serializatedMessage, out fieldOrder);

            DomainId = int.Parse(serializatedMessage[fieldOrder++]);
            this.countOfTickets = int.Parse(serializatedMessage[fieldOrder++]);
            this.AccountNumber = serializatedMessage[fieldOrder++];
            this.Ticket = serializatedMessage[fieldOrder++];
            this.Amount = decimal.Parse(serializatedMessage[fieldOrder++]);
            this.selectionMode = (Ticket.Selection)int.Parse(serializatedMessage[fieldOrder++]);
            this.Action = (GameboardStatus)int.Parse(serializatedMessage[fieldOrder++]);
            this.Creation = new DateTime(
                int.Parse(serializatedMessage[fieldOrder++]),
                int.Parse(serializatedMessage[fieldOrder++]),
                int.Parse(serializatedMessage[fieldOrder++]),
                int.Parse(serializatedMessage[fieldOrder++]),
                int.Parse(serializatedMessage[fieldOrder++]),
                int.Parse(serializatedMessage[fieldOrder++]),
                int.Parse(serializatedMessage[fieldOrder++])
                );
            OrderNumber = int.Parse(serializatedMessage[fieldOrder++]);
            this.TicketNumber = int.Parse(serializatedMessage[fieldOrder++]);
            this.subticketsAndWagerNumbers = serializatedMessage[fieldOrder++];
            this.PrizesVersion = int.Parse(serializatedMessage[fieldOrder++]);
            CurrencyId = int.Parse(serializatedMessage[fieldOrder++]);
        }

        public string StateAbb
        {
            set
            {
                this.stateAbb = value;
            }
            get
            {
                return this.stateAbb;
            }
        }

        public int CountOfTickets
        {
            set
            {
                this.countOfTickets = value;
            }
            get
            {
                return this.countOfTickets;
            }
        }

        public Ticket.Selection SelectionMode
        {
            set
            {
                this.selectionMode = value;
            }
            get
            {
                return this.selectionMode;
            }
        }

        public string DrawingName
        {
			set
			{
                this.drawingName = value;
			}
            get
            {
                return this.drawingName;
            }
        }

        public string SubticketsAndWagerNumbers
        {
            set
            {
                this.subticketsAndWagerNumbers = value;
            }
            get
            {
                return this.subticketsAndWagerNumbers;
            }
        }

        internal IdOfLottery IdOfLottery
        {
            get
            {
                var type = Ticket.Substring(0, 2);
                var converted = Enum.TryParse(type, out IdOfLottery idOfType);
                if (!converted) throw new GameEngineException($"There is no type {type} for ticket");
                return idOfType;
            }
        }

        public bool SelectionWasInputs
        {
            get
            {
                return SelectionMode == Selection.MultipleInputSingleAmount || selectionMode == Selection.SingleInputMultipleAmount;
            }
        }

        internal override string GameType()
        {
            var ticketId = TicketType;
            switch (ticketId)
            {
                case TicketType.P2S:
                    return "TicketPick2Straight";
                case TicketType.P3S:
                    return "TicketPick3Straight";
                case TicketType.P4S:
                    return "TicketPick4Straight";
                case TicketType.P5S:
                    return "TicketPick5Straight";
                case TicketType.P2B:
                    return "TicketPick2Boxed";
                case TicketType.P3B:
                    return "TicketPick3Boxed";
                case TicketType.P4B:
                    return "TicketPick4Boxed";
                case TicketType.P5B:
                    return "TicketPick5Boxed";
                case TicketType.PBS:
                    return "TicketPowerBallSingle";
                case TicketType.PBP:
                    return "TicketPowerBallPowerPlay";
                default:
                    throw new Exception($"The ticket id {ticketId} is not valid.");
            }
        }

        internal override string GameTypeLottery()
        {
            var ticketId = TicketType;
            switch (ticketId)
            {
                case TicketType.P2S:
                case TicketType.P2B:
                    return "LotteryPick2";
                case TicketType.P3S:
                case TicketType.P3B:
                    return "LotteryPick3";
                case TicketType.P4S:
                case TicketType.P4B:
                    return "LotteryPick4";
                case TicketType.P5S:
                case TicketType.P5B:
                    return "LotteryPick5";
                case TicketType.PBS:
                case TicketType.PBP:
                    return "LotteryPowerball";
                default:
                    throw new Exception($"The ticket id {ticketId} is not valid.");
            }
        }

        internal override decimal TicketAmount()
        {
            return Amount * CountOfTickets;
        }

        internal string SelectionModeAsString()
        {
            return SelectionMode.ToString();
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(DomainId).
            AddProperty(this.CountOfTickets);
            AddProperty(this.AccountNumber).
            AddProperty(this.Ticket).
            AddProperty(this.Amount).
            AddProperty((int)this.SelectionMode).
            AddProperty((int)this.Action).
            AddProperty(this.Creation).
            AddProperty(this.Creation.Millisecond).
            AddProperty(OrderNumber).
            AddProperty(this.TicketNumber.ToString()).
            AddProperty(this.SubticketsAndWagerNumbers).
            AddProperty(this.PrizesVersion).
            AddProperty(CurrencyId);
        }

        internal IEnumerable<SubTicket<IPick>> SubTickets()
        {
            if (Ticket == null) throw new ArgumentNullException(nameof(Ticket));
            if (Ticket.Equals("")) throw new ArgumentNullException(nameof(Ticket));

            var deserializedSubTickets = IsPowerball() ? DeserializePowerballNumbers(Ticket) : DeserializeSubTickets(Ticket);
            if (deserializedSubTickets == null || !deserializedSubTickets.Any()) throw new GameEngineException("There is no deserialized SubTickets.");
            else return deserializedSubTickets;
        }

        private IEnumerable<SubTicket<IPick>> DeserializePowerballNumbers(string text)
        {
            var singleNumber = new StringBuilder();
            var pairs = new string[6];
            var pairIndex = 0;
            var numbersWithoutHeader = text.Skip(3);
            foreach (var character in numbersWithoutHeader)
            {
                if (character.Equals(',') || character.Equals(']'))
                {
                    pairs[pairIndex] = singleNumber.ToString();
                    pairIndex++;
                    singleNumber.Clear();
                }
                if ((character >= '0' && character <= '9'))
                {
                    singleNumber.Append(character);
                }
            }

            var powerball = new PowerBall(pairs[0], pairs[1], pairs[2], pairs[3], pairs[4], pairs[5]);
            return powerball.SubTickets();
        }

        private IEnumerable<SubTicket<IPick>> DeserializeSubTickets(string text)
        {
            if (string.IsNullOrWhiteSpace(text)) throw new ArgumentNullException(nameof(text));
            if (text.Length < 5) throw new GameEngineException($"The length in {text} is less than required.");
            if (text[0] != 'P') throw new GameEngineException($"Invalid format in {text}. It must start with letter 'P'.");
            if (text[2] != 'B' && text[2] != 'S') throw new GameEngineException($"Serialized number {text} does not contain the type of pick in index 2.");

            var pick = (int)Char.GetNumericValue(text[1]);
            var picks = new List<IPick>();
            bool areMultipleNumber = false;
            var singleNumber = new StringBuilder();
            var pattern = new string[pick];
            var patternIndex = 0;
            var numbersWithoutHeader = text.Skip(3);
            foreach (var character in numbersWithoutHeader)
            {
                if (character.Equals('{'))
                {
                    areMultipleNumber = true;
                }
                if (character.Equals(']'))
                {
                    pattern[patternIndex] = singleNumber.ToString();
                    singleNumber.Clear();
                    patternIndex = 0;
                    switch (pick)
                    {
                        case 2:
                            {
                                var pick2 = new Pick2(pattern[0], pattern[1]);
                                picks.Add(pick2);

                                break;
                            }
                        case 3:
                            {
                                var pick3 = new Pick3(pattern[0], pattern[1], pattern[2]);
                                picks.Add(pick3);

                                break;
                            }
                        case 4:
                            {
                                var pick4 = new Pick4(pattern[0], pattern[1], pattern[2], pattern[3]);
                                picks.Add(pick4);

                                break;
                            }
                        case 5:
                            {
                                var pick5 = new Pick5(pattern[0], pattern[1], pattern[2], pattern[3], pattern[4]);
                                picks.Add(pick5);

                                break;
                            }
                        default:
                            {
                                throw new GameEngineException($"Invalid {nameof(pick)} {pick}");
                            }
                    }
                }
                if (character.Equals('}'))
                {
                    areMultipleNumber = false;
                }
                if (character.Equals(','))
                {
                    if (!areMultipleNumber)
                    {
                        pattern[patternIndex] = singleNumber.ToString();
                        patternIndex++;
                        singleNumber.Clear();
                    }
                }
                if ((character >= '0' && character <= '9') || character == '*')
                {
                    singleNumber.Append(character);
                }
            }

            var result = picks.SelectMany(x => x.SubTickets());
            return result;
        }

        internal IEnumerable<CompletedWager> Wagers()
        {
            var wagers = new List<CompletedWager>();
            var subticketsAndWagerNumbers = SubticketsAndWagerNumbers.Replace("(", "").Split(")", StringSplitOptions.RemoveEmptyEntries);
            var lastSubticketsAndWagerNumbers = subticketsAndWagerNumbers[subticketsAndWagerNumbers.Length - 1];
            var isPattern = lastSubticketsAndWagerNumbers.StartsWith("-") || lastSubticketsAndWagerNumbers.StartsWith(",") || subticketsAndWagerNumbers[0].Contains("*");
            if (isPattern)
            {
                if (SelectionWasInputs) throw new GameEngineException($"Ticket using number pattern cannot be selected with input.");
                var strSubticketsAndWagerNumbers = SubticketsAndWagerNumbers.Trim('(').Trim(')');
                var lastIndex = strSubticketsAndWagerNumbers.LastIndexOf(',');
                var subticketNumber = strSubticketsAndWagerNumbers.Substring(0, lastIndex);
                var wagerNumber = strSubticketsAndWagerNumbers.Substring(lastIndex + 1);

                var wager = new CompletedWager(this, subticketNumber, int.Parse(wagerNumber));
                wagers.Add(wager);
            }
            else
            {
                foreach (var subticketAndWagerNumber in subticketsAndWagerNumbers)
                {
                    var arrayOfSubticketAndWagerNumber = subticketAndWagerNumber.Split(",");
                    var wager = new CompletedWager(this, arrayOfSubticketAndWagerNumber[0], int.Parse(arrayOfSubticketAndWagerNumber[1]));
                    wagers.Add(wager);
                }
            }
            return wagers;
        }

        internal bool WasCreatedByPattern()
        {
            var subticketsAndWagerNumbers = SubticketsAndWagerNumbers.Replace("(", "").Split(")", StringSplitOptions.RemoveEmptyEntries);
            var lastSubticketsAndWagerNumbers = subticketsAndWagerNumbers[subticketsAndWagerNumbers.Length - 1];
            var isPattern = lastSubticketsAndWagerNumbers.StartsWith("-") || lastSubticketsAndWagerNumbers.StartsWith(",") || subticketsAndWagerNumbers[0].Contains("*");
            return isPattern;
        }

        internal bool IsPowerball()
        {
            var isPowerball = TicketType == TicketType.PBS || TicketType == TicketType.PBP;
            return isPowerball;
        }

        internal decimal PowerplayCost()
        {
            var isPowerballSingle = TicketType == TicketType.PBS;
            if (isPowerballSingle) return 0;
            return Amount;
        }
    }

    internal sealed class WagerWinnerInfo
    {
        private readonly WinnerInfo winnerInfo;
        internal WinnerInfo WinnerInfo
        {
            get
            {
                return winnerInfo;
            }
        }

        private readonly CompletedWager wager;
        internal CompletedWager Wager
        {
            get
            {
                return wager;
            }
        }

        private decimal prize;
        internal decimal Prize
        {
            get
            {
                return prize;
            }
        }

        internal WagerWinnerInfo(WinnerInfo winnerInfo, CompletedWager wager)
        { 
            if (winnerInfo == null) throw new ArgumentNullException(nameof(winnerInfo));
            if (wager == null) throw new ArgumentNullException(nameof(wager));

            this.winnerInfo = winnerInfo;
            this.wager = wager;
        }

        internal void UpdatePrize(decimal prize)
        {
            if (prize <= 0) throw new GameEngineException($"{nameof(prize)} {prize} must be greater than zero");
            this.prize = prize;
        }
    }

    public sealed class WinnerInfo : TicketPicksMessage
    {
        private string draw;
        private string gradedBy;
        private decimal prize;
        public int AffiliateId { get; set; }

        public WinnerInfo(string message) : base(message)
        {
            
        }

        internal WinnerInfo(Company company, string draw, int? fireball, string gradedBy, decimal prize, string stateAbb, int hour, int minute, int year, int month, int day, string accountNumber, string ticket, 
            int countOfTickets, decimal amount, Ticket.Selection selectionMode, GameboardStatus action, int drawingId, int uniqueDrawingId, TypeNumberSequence position, string drawingName, DateTime creation, int orderNumber, int ticketNumber, 
            string subticketsAndWagerNumbers, decimal profit, int prizesVersion, int domainId, string domainUrl, int currencyId) : 
            base(stateAbb, hour, minute, year, month, day, accountNumber, ticket, countOfTickets, amount, selectionMode, action, drawingId, position, drawingName, creation, orderNumber, ticketNumber, subticketsAndWagerNumbers, 
                prizesVersion, domainId, domainUrl, currencyId, GradeTicketType.WINNER_TICKET)
        {
            this.Company = company;
            this.draw = draw;
            Fireball = fireball == null ? LotteryDraw.WITHOUT_FIREBALL : (int)fireball;
            this.gradedBy = gradedBy;
            this.prize = prize;
            this.profit = profit;
            ValidateFields();
            UniqueDrawingId = uniqueDrawingId;
        }

        internal WinnerInfo(Company company, decimal prize, string accountNumber, string ticket,
            int countOfTickets, decimal amount, Ticket.Selection selectionMode, GameboardStatus action, string drawingName, DateTime creation, int orderNumber, int ticketNumber,
            string subticketsAndWagerNumbers, decimal profit, int prizesVersion, int domainId, string domainUrl, int currencyId) :
            base(accountNumber, ticket, countOfTickets, amount, selectionMode, action, drawingName, creation, orderNumber, ticketNumber,
                subticketsAndWagerNumbers, prizesVersion, domainId, domainUrl, currencyId, GradeTicketType.WINNER_TICKET)
        {
            this.Company = company;
            this.prize = prize;
            this.profit = profit;
        }

        internal void CompleteInfo(string stateAbb, DateTime drawDate, int drawingId, string gradedBy, string draw, int fireball)
        {
            base.CompleteInfo(stateAbb, drawDate, drawingId);
            this.draw = draw;
            Fireball = fireball;
            this.gradedBy = gradedBy;
        }

        protected override void Deserialize(string [] serializatedMessage, out int fieldOrder)
		{
            base.Deserialize(serializatedMessage, out fieldOrder);
            this.Company = null;
            this.prize = decimal.Parse(serializatedMessage[fieldOrder++]);
            profit = decimal.Parse(serializatedMessage[fieldOrder++]);
        }

        private void ValidateFields()
        {
            if (String.IsNullOrWhiteSpace(this.draw)) throw new ArgumentNullException("Draw can not be null");
            if (String.IsNullOrWhiteSpace(this.gradedBy)) throw new ArgumentNullException("Graded by can not be null");
            if (prize < 0) throw new GameEngineException("Prize must be upper than zero");
        }

        public string Draw
        {
            get
            {
                return this.draw;
            }
        }

        public string WinnerDigits()
        {
            if (((TicketType == TicketType.P2S || TicketType == TicketType.P2B) && draw.Length == 2) || 
                ((TicketType == TicketType.P3S || TicketType == TicketType.P3B) && draw.Length == 3) ||
                ((TicketType == TicketType.P4S || TicketType == TicketType.P4B) && draw.Length == 4) ||
                (TicketType == TicketType.P5S || TicketType == TicketType.P5B)) 
                return draw;

            switch (TicketType)
            {
                case TicketType.P2S:
                case TicketType.P2B:
                    return Position == TypeNumberSequence.BEGINNING ? draw.Substring(0, 2) : draw.Substring(3, 2);
                case TicketType.P3S:
                case TicketType.P3B:
                    return draw.Substring(2, 3);
                case TicketType.P4S:
                case TicketType.P4B:
                    return draw.Substring(1, 4);
                default:
                    throw new GameEngineException($"Invalid ticket type {TicketType}");
            }
        }

        internal int Fireball { get; set; }

        internal bool BelongsToFireBallDraw => Fireball != LotteryDraw.WITHOUT_FIREBALL;

        public string GradedBy
        {
            get
            {
                return this.gradedBy;
            }
        }

        public decimal Prize
        {
            get
            {
                return this.prize;
            }
        }

        internal decimal PrizeToWinForOneDollar
        {
            get
            {
                return Prize / Amount;
            }
        }

        private decimal profit;
        public decimal Profit
        {
            get
            {
                return profit;
            }
        }

        internal bool WasPurchasedForFree
        {
            get
            {
                var result = profit == -prize;
                return result;
            }
        }

        internal bool HasSequenceOfNumbers
        {
            get
            {
                var result = !Draw.Contains(LotteryDraw.DEFAULT_VALUE_FOR_EMPTY_WINNER_NUMBER);
                return result;
            }
        }

        internal decimal WonAmount()
        {
            return Amount * Prize;
        }

        internal bool IsWinner()
        {
            return Prize > 0;
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
                AddProperty(this.Prize).
                AddProperty(profit);
        }

        internal IEnumerable<WagerWinnerInfo> WinnerWagers()
        {
            var winnerInfoWagers = new List<WagerWinnerInfo>();
            var subticketsAndWagerNumbers = SubticketsAndWagerNumbers.Replace("(", "").Split(")", StringSplitOptions.RemoveEmptyEntries);
            var lastSubticketsAndWagerNumbers = subticketsAndWagerNumbers[subticketsAndWagerNumbers.Length - 1];
            var isPattern = lastSubticketsAndWagerNumbers.StartsWith("-") || lastSubticketsAndWagerNumbers.StartsWith(",") || subticketsAndWagerNumbers[0].Contains("*");
            if (isPattern)
            {
                if (SelectionWasInputs) throw new GameEngineException($"Ticket using number pattern cannot be selected with input.");
                var strSubticketsAndWagerNumbers = SubticketsAndWagerNumbers.Trim('(').Trim(')');
                var lastIndex = strSubticketsAndWagerNumbers.LastIndexOf(',');
                var subticketNumber = strSubticketsAndWagerNumbers.Substring(0, lastIndex);
                var wagerNumber = strSubticketsAndWagerNumbers.Substring(lastIndex + 1);
                if (String.IsNullOrWhiteSpace(wagerNumber)) throw new ArgumentNullException(nameof(wagerNumber));

                var wager = new CompletedWager(this, subticketNumber, int.Parse(wagerNumber));
                var wagerWinnerInfo = new WagerWinnerInfo(this, wager);
                wagerWinnerInfo.UpdatePrize(Prize);
                winnerInfoWagers.Add(wagerWinnerInfo);
            }
            else
            {
                foreach (var subticketAndWagerNumber in subticketsAndWagerNumbers)
                {
                    var arrayOfSubticketAndWagerNumber = subticketAndWagerNumber.Split(",");
                    var numbers = arrayOfSubticketAndWagerNumber[0].Split("-");
                    var winnerNumbers = WinnerDigits();
                    switch (numbers.Length)
                    {
                        case 2:
                            var subticket2 = new SubTicket<Pick2>((int)Char.GetNumericValue(numbers[0][0]), (int)Char.GetNumericValue(numbers[1][0]));
                            if ((BelongsToFireBallDraw && 
                                ((subticket2.IsTheSameNumberWithTheSameOrderForFireball((int)char.GetNumericValue(winnerNumbers[0]), (int)char.GetNumericValue(winnerNumbers[1]), Fireball) && (TicketType == TicketType.P2S)) ||
                                (subticket2.IsTheSameNumberWithDifferentOrderForFireball((int)char.GetNumericValue(winnerNumbers[0]), (int)char.GetNumericValue(winnerNumbers[1]), Fireball) && (TicketType == TicketType.P2B)))) ||
                                (!BelongsToFireBallDraw && 
                                ((subticket2.IsTheSameNumberWithTheSameOrder((int)char.GetNumericValue(winnerNumbers[0]), (int)char.GetNumericValue(winnerNumbers[1])) && (TicketType == TicketType.P2S)) ||
                                (subticket2.IsTheSameNumberWithDifferentOrder((int)char.GetNumericValue(winnerNumbers[0]), (int)char.GetNumericValue(winnerNumbers[1])) && (TicketType == TicketType.P2B)))))
                            {
                                var wager = new CompletedWager(this, arrayOfSubticketAndWagerNumber[0], int.Parse(arrayOfSubticketAndWagerNumber[1]));
                                var wagerWinnerInfo = new WagerWinnerInfo(this, wager);
                                winnerInfoWagers.Add(wagerWinnerInfo);
                            }
                            break;
                        case 3:
                            var subticket3 = new SubTicket<Pick3>((int)Char.GetNumericValue(numbers[0][0]), (int)Char.GetNumericValue(numbers[1][0]), (int)Char.GetNumericValue(numbers[2][0]));
                            if ((BelongsToFireBallDraw &&
                                ((subticket3.IsTheSameNumberWithTheSameOrderForFireball((int)char.GetNumericValue(winnerNumbers[0]), (int)char.GetNumericValue(winnerNumbers[1]), (int)char.GetNumericValue(winnerNumbers[2]), Fireball) && (TicketType == TicketType.P3S)) ||
                                (subticket3.IsTheSameNumberWithDifferentOrderForFireball((int)char.GetNumericValue(winnerNumbers[0]), (int)char.GetNumericValue(winnerNumbers[1]), (int)char.GetNumericValue(winnerNumbers[2]), Fireball) && (TicketType == TicketType.P3B)))) ||
                                (!BelongsToFireBallDraw && 
                                ((subticket3.IsTheSameNumberWithTheSameOrder((int)char.GetNumericValue(winnerNumbers[0]), (int)char.GetNumericValue(winnerNumbers[1]), (int)char.GetNumericValue(winnerNumbers[2])) && (TicketType == TicketType.P3S)) ||
                                (subticket3.IsTheSameNumberWithDifferentOrder((int)char.GetNumericValue(winnerNumbers[0]), (int)char.GetNumericValue(winnerNumbers[1]), (int)char.GetNumericValue(winnerNumbers[2])) && (TicketType == TicketType.P3B)))))
                            {
                                var wager = new CompletedWager(this, arrayOfSubticketAndWagerNumber[0], int.Parse(arrayOfSubticketAndWagerNumber[1]));
                                var wagerWinnerInfo = new WagerWinnerInfo(this, wager);
                                winnerInfoWagers.Add(wagerWinnerInfo);
                            }
                            break;
                        case 4:
                            var subticket4 = new SubTicket<Pick4>((int)Char.GetNumericValue(numbers[0][0]), (int)Char.GetNumericValue(numbers[1][0]), (int)Char.GetNumericValue(numbers[2][0]), (int)Char.GetNumericValue(numbers[3][0]));
                            if ((BelongsToFireBallDraw &&
                                ((subticket4.IsTheSameNumberWithTheSameOrderForFireball((int)char.GetNumericValue(winnerNumbers[0]), (int)char.GetNumericValue(winnerNumbers[1]), (int)char.GetNumericValue(winnerNumbers[2]), (int)char.GetNumericValue(winnerNumbers[3]), Fireball) && (TicketType == TicketType.P4S)) ||
                                (subticket4.IsTheSameNumberWithDifferentOrderForFireball((int)char.GetNumericValue(winnerNumbers[0]), (int)char.GetNumericValue(winnerNumbers[1]), (int)char.GetNumericValue(winnerNumbers[2]), (int)char.GetNumericValue(winnerNumbers[3]), Fireball) && (TicketType == TicketType.P4B)))) ||
                                (!BelongsToFireBallDraw && 
                                ((subticket4.IsTheSameNumberWithTheSameOrder((int)char.GetNumericValue(winnerNumbers[0]), (int)char.GetNumericValue(winnerNumbers[1]), (int)char.GetNumericValue(winnerNumbers[2]), (int)char.GetNumericValue(winnerNumbers[3])) && (TicketType == TicketType.P4S)) ||
                                (subticket4.IsTheSameNumberWithDifferentOrder((int)char.GetNumericValue(winnerNumbers[0]), (int)char.GetNumericValue(winnerNumbers[1]), (int)char.GetNumericValue(winnerNumbers[2]), (int)char.GetNumericValue(winnerNumbers[3])) && (TicketType == TicketType.P4B)))))
                            {
                                var wager = new CompletedWager(this, arrayOfSubticketAndWagerNumber[0], int.Parse(arrayOfSubticketAndWagerNumber[1]));
                                var wagerWinnerInfo = new WagerWinnerInfo(this, wager);
                                winnerInfoWagers.Add(wagerWinnerInfo);
                            }
                            break;
                        case 5:
                            var subticket5 = new SubTicket<Pick5>((int)Char.GetNumericValue(numbers[0][0]), (int)Char.GetNumericValue(numbers[1][0]), (int)Char.GetNumericValue(numbers[2][0]), (int)Char.GetNumericValue(numbers[3][0]), (int)Char.GetNumericValue(numbers[4][0]));
                            if ((BelongsToFireBallDraw &&
                                ((subticket5.IsTheSameNumberWithTheSameOrderForFireball((int)char.GetNumericValue(winnerNumbers[0]), (int)char.GetNumericValue(winnerNumbers[1]), (int)char.GetNumericValue(winnerNumbers[2]), (int)char.GetNumericValue(winnerNumbers[3]), (int)char.GetNumericValue(winnerNumbers[4]), Fireball) && (TicketType == TicketType.P5S)) ||
                                (subticket5.IsTheSameNumberWithDifferentOrderForFireball((int)char.GetNumericValue(winnerNumbers[0]), (int)char.GetNumericValue(winnerNumbers[1]), (int)char.GetNumericValue(winnerNumbers[2]), (int)char.GetNumericValue(winnerNumbers[3]), (int)char.GetNumericValue(winnerNumbers[4]), Fireball) && (TicketType == TicketType.P5B)))) || 
                                (!BelongsToFireBallDraw && 
                                ((subticket5.IsTheSameNumberWithTheSameOrder((int)char.GetNumericValue(winnerNumbers[0]), (int)char.GetNumericValue(winnerNumbers[1]), (int)char.GetNumericValue(winnerNumbers[2]), (int)char.GetNumericValue(winnerNumbers[3]), (int)char.GetNumericValue(winnerNumbers[4])) && (TicketType == TicketType.P5S)) ||
                                (subticket5.IsTheSameNumberWithDifferentOrder((int)char.GetNumericValue(winnerNumbers[0]), (int)char.GetNumericValue(winnerNumbers[1]), (int)char.GetNumericValue(winnerNumbers[2]), (int)char.GetNumericValue(winnerNumbers[3]), (int)char.GetNumericValue(winnerNumbers[4])) && (TicketType == TicketType.P5B)))))
                            {
                                var wager = new CompletedWager(this, arrayOfSubticketAndWagerNumber[0], int.Parse(arrayOfSubticketAndWagerNumber[1]));
                                var wagerWinnerInfo = new WagerWinnerInfo(this, wager);
                                winnerInfoWagers.Add(wagerWinnerInfo);
                            }
                            else if ((arrayOfSubticketAndWagerNumber.Length == 3)&& (TicketType == TicketType.PBS || TicketType == TicketType.PBP))
                            {
                                var wager = new CompletedWager(this, $"{arrayOfSubticketAndWagerNumber[0]},{arrayOfSubticketAndWagerNumber[1]}" , int.Parse(arrayOfSubticketAndWagerNumber[2]));
                                var wagerWinnerInfo = new WagerWinnerInfo(this, wager);
                                winnerInfoWagers.Add(wagerWinnerInfo);
                            }
                            break;
                        default:
                            throw new GameEngineException($"{nameof(numbers)} {numbers} is not valid length");
                    }
                }

                if (winnerInfoWagers.Count == 0) throw new GameEngineException($"{nameof(winnerInfoWagers)} must have at least one element");
                var prizeForWager = Prize / winnerInfoWagers.Count;
                foreach (var winnerInfoWager in winnerInfoWagers)
                {
                    winnerInfoWager.UpdatePrize(prizeForWager);
                }
            }
            return winnerInfoWagers;
        }

        internal LoserInfo TransformToLoserInfo()
        {
            var loserInfo = new LoserInfo(
                company: this.Company,
                draw: this.Draw,
                fireball: this.Fireball,
                gradedBy: this.GradedBy,
                stateAbb: this.StateAbb, 
                hour: this.Hour, 
                minute: this.Minute, 
                year: this.Year, 
                month: this.Month, 
                day: this.Day, 
                accountNumber: this.AccountNumber, 
                ticket: this.Ticket, 
                countOfTickets: this.CountOfTickets, 
                amount: this.Amount, 
                selectionMode: this.SelectionMode, 
                action: this.Action, 
                drawingId: this.DrawingId, 
                position: Position,
                drawingName: this.DrawingName,
                creation: this.Creation, 
                orderNumber: OrderNumber,
                ticketNumber: this.TicketNumber, 
                subticketsAndWagerNumbers: this.SubticketsAndWagerNumbers, 
                profit: WasPurchasedForFree ? 0 : profit,
                prizesVersion: this.PrizesVersion, 
                domainId: this.DomainId,
                domainUrl: this.DomainUrl,
                currencyId: this.CurrencyId
            );
            return loserInfo;
        }
    }

    internal abstract class MessageInfoByPrize : Objeto
    {
        private readonly TicketPicksMessage messageInfo;
        private readonly List<SubTicket<IPick>> subtickets = new List<SubTicket<IPick>>();
        private readonly int prizeCriteria;
        private readonly Company company;

        internal TicketPicksMessage MessageInfo { get { return messageInfo; } }
        internal IEnumerable<SubTicket<IPick>> Subtickets { get { return subtickets; } }
        internal bool HasOnlyOnePrize { get; set; }


        internal int Count
        {
            get
            {
                return subtickets.Count;
            }
        }

        protected MessageInfoByPrize(TicketPicksMessage messageInfo, int prizeCriteria)
        {
            this.messageInfo = messageInfo;
            this.prizeCriteria = prizeCriteria;

            var type = messageInfo.TicketType;
            this.company = messageInfo.Company;
            PicksLotteryGame lotteries = company.Lotto900();

            var subtickets = messageInfo.SubTickets();
            if (messageInfo.IsPowerball())
            {
                if (subtickets.Count() != 1) throw new GameEngineException("Powerball only must have 1 subticket.");
                Add(subtickets.First());
            }
            else
            {
                foreach (SubTicket<IPick> subTicket in messageInfo.SubTickets())
                {
                    var wayOfSubticket = lotteries.Prizes.WayOfSubticket(type, subTicket);
                    if (prizeCriteria == wayOfSubticket)
                    {
                        Add(subTicket);
                    }
                }
            }
        }

        internal virtual decimal Prize
        {
            get
            {
                var company = messageInfo.Company;
                var prizes = company.Lotto900().Prizes;
                var versionNumber = messageInfo.PrizesVersion;
                return prizes.Prize(versionNumber, messageInfo.TicketType, this.prizeCriteria);
            }
        }

        internal int PrizeCriteria
        {
            get
            {
                return this.prizeCriteria;
            }
        }

        protected MessageInfoByPrize()
        {
            this.messageInfo = null;
            this.prizeCriteria = -1;
        }

        internal decimal TicketAmount()
        {
            var amount = messageInfo.Amount * Count;
            var usePowerplay = messageInfo.TicketType == TicketType.PBP;
            if (usePowerplay)
            {
                amount += messageInfo.PowerplayCost();
            }
            return amount;
        }

        internal string TicketAmountFormatted()
        {
            var result = Currency.Factory(Coinage.GetById(messageInfo.CurrencyId), TicketAmount()).ToDisplayFormat();
            return result;
        }

        internal virtual string WonAmountFormatted()
        {
            var result = Currency.ZeroFactory(Coinage.GetById(messageInfo.CurrencyId).Iso4217Code).ToDisplayFormat();
            return result;
        }

        private void Add(SubTicket<IPick> subTicket)
        {
            subtickets.Add(subTicket);
        }

        internal void Add(IEnumerable<SubTicket<IPick>> subTickets)
        {
            subtickets.AddRange(subTickets);
        }

        internal abstract bool HasNumber(int[] numbers);

        internal string AsString()
        {
            switch (messageInfo.TicketType)
            {
                case TicketType.P2S:
                case TicketType.P3S:
                case TicketType.P4S:
                case TicketType.P5S:
                case TicketType.PBS:
                case TicketType.PBP:
                    return messageInfo.Ticket;
                case TicketType.P2B:
                case TicketType.P3B:
                case TicketType.P4B:
                case TicketType.P5B:
                    if (HasOnlyOnePrize && messageInfo.SelectionMode == Ticket.Selection.BALLS)
                    {
                        return messageInfo.Ticket;
                    }
                    StringBuilder result = new StringBuilder();
                    result.Append(messageInfo.TicketType);
                    foreach (var subticket in Subtickets)
                    {
                        result.Append(subticket.AsString());
                    }
                    return result.ToString();
                default:
                    throw new GameEngineException($"The ticket id {messageInfo.TicketType} is not valid.");
            }

        }

        internal string AsStringWithoutID()
        {
            var result = messageInfo.Ticket.Replace(messageInfo.TicketType.ToString(), "");
            return result;
        }

        internal virtual IEnumerable<CompletedWager> Wagers()
        {
            var wagersByPrize = new List<CompletedWager>();
            var wagers = messageInfo.Wagers();
            if (wagers.Count() == 1)
            {
                wagersByPrize.Add(wagers.ElementAt(0));
                return wagersByPrize;
            }

            foreach (var wager in wagers)
            {
                var existSubticket = subtickets.Exists(x => x.AsStringForAccounting() == wager.SubticketAsString);
                if (existSubticket)
                {
                    wagersByPrize.Add(wager);
                }
            }
            return wagersByPrize;
        }

        internal bool HasSubtickets()
        {
            var result = subtickets != null && subtickets.Count > 0;
            return result;
        }
    }

    internal class CompletedWinnersTicketsByOrderAndPrize : Objeto
    {
        internal List<WinnerInfo> Tickets { get; private set; } = new List<WinnerInfo>();
        internal int OrderNumber { get; private set; }
        internal decimal PrizeToWinForOneDollar => Tickets.First().PrizeToWinForOneDollar;
        internal string StateAbb => Tickets.First().StateAbb;
        internal DateTime DrawDate => Tickets.First().DrawDate();
        internal WinnerInfo FirstTicket => Tickets.First();
        public CompletedWinnersTicketsByOrderAndPrize(WinnerInfo ticket)
        {
            if (ticket == null) throw new ArgumentNullException(nameof(ticket));
            if (ticket.PrizeToWinForOneDollar <= 0) throw new GameEngineException($"{nameof(ticket.Prize)} {ticket.Prize} must be greater than zero");
            Tickets.Add(ticket);
            OrderNumber = ticket.OrderNumber;
        }
        internal void Add(WinnerInfo ticket)
        {
            if (ticket == null) throw new ArgumentNullException(nameof(ticket));
            if (ticket.PrizeToWinForOneDollar != PrizeToWinForOneDollar) throw new GameEngineException($"Tickets must have the prize");
            if (ticket.OrderNumber != OrderNumber) throw new GameEngineException("Tickets must have the same order number");
            if (ticket.StateAbb != StateAbb) throw new GameEngineException("Tickets must have the same state");
            if (ticket.DrawDate() != DrawDate) throw new GameEngineException("Tickets must have the same draw date");
            Tickets.Add(ticket);
        }
        internal decimal TicketAmount()
        {
            var amount = FirstTicket.TicketAmount();
            return amount;
        }
        internal string TicketAmountFormatted()
        {
            var result = Currency.Factory(Coinage.GetById(FirstTicket.CurrencyId), TicketAmount()).ToDisplayFormat();
            return result;
        }
        internal decimal WonAmount()
        {
            var amount = FirstTicket.Prize;
            return amount;
        }
        internal string WonAmountFormatted()
        {
            var result = Currency.Factory(Coinage.GetById(FirstTicket.CurrencyId), WonAmount()).ToDisplayFormat();
            return result;
        }
        internal IEnumerable<CompletedWager> Wagers()
        {
            var wagers = new List<CompletedWager>();
            foreach (var ticket in Tickets)
            {
                wagers.AddRange(ticket.Wagers());
            }
            return wagers;
        }
        internal bool IsWinner()
        {
            var result = Tickets.Any(x => x.IsWinner());
            return result;
        }
    }

    internal abstract class WinnerInfoByPrize : MessageInfoByPrize
    {
        public WinnerInfo WinnerInfo
        {
            get
            {
                var winnerInfo = MessageInfo as WinnerInfo;
                return winnerInfo;
            }
        }

        internal override decimal Prize
            {
            get
            {
                return WinnerInfo.Prize;
            }
        }

        internal decimal PrizeToWinForOneDollar
        {
            get
            {
                return WinnerInfo.Prize / WinnerInfo.Amount;
            }
        }

        protected WinnerInfoByPrize(WinnerInfo winnerInfo, int prizeCriteria):base(winnerInfo, prizeCriteria)
        {

        }

        protected WinnerInfoByPrize() : base()
        {

        }

        internal decimal WonAmount()
        {
            var amount = IsWinner() ? MessageInfo.Amount * base.Prize : 0m;
            return amount;
        }

        internal override string WonAmountFormatted()
        {
            var result = Currency.Factory(Coinage.GetById(WinnerInfo.CurrencyId), WonAmount()).ToDisplayFormat();
            return result;
        }

        internal bool IsWinner()
        {
            var winnerInfo = MessageInfo as WinnerInfo;
            var hasSequenceOfNumbers = winnerInfo.HasSequenceOfNumbers;
            var winnerNumber = hasSequenceOfNumbers ? Array.ConvertAll(winnerInfo.Draw.ToCharArray(), c => (int)Char.GetNumericValue(c)) : new int[] { };
            var hasNumbers = hasSequenceOfNumbers && HasNumber(winnerNumber);
            return hasNumbers;
        }
    }

    internal class WinnerInfoByPrizeWithTotalSubtickets : Objeto
    {
        private readonly List<WinnerInfoByPrize> ticketsByPrize = new List<WinnerInfoByPrize>();
        internal IEnumerable<WinnerInfoByPrize> TicketsByPrize
        {
            get
            {
                return ticketsByPrize;
            }
        }

        public WinnerInfoByPrizeWithTotalSubtickets(IEnumerable<WinnerInfoByPrize> ticketsByPrize)
        {
            this.ticketsByPrize = ticketsByPrize.ToList();
        }

        internal int TotalSubtickets()
        {
            var count = ticketsByPrize.Sum(x => x.Count);
            return count;
        }
    }

    internal class WinnerInfoByPrizeEmpty : WinnerInfoByPrize
    {
        internal WinnerInfoByPrizeEmpty() : base()
        {

        }

        internal override bool HasNumber(int[] numbers)
        {
            return false;
        }

        internal override IEnumerable<CompletedWager> Wagers()
        {
            var empty = new List<CompletedWager>();
            return empty;
        }
    }

    internal class LoserInfoByPrizeEmpty : LoserInfoByPrize
    {
        internal LoserInfoByPrizeEmpty() : base()
        {

        }

        internal override bool HasNumber(int[] numbers)
        {
            return false;
        }

        internal override IEnumerable<CompletedWager> Wagers()
        {
            var empty = new List<CompletedWager>();
            return empty;
        }
    }

    internal class NoActionInfoByPrizeEmpty : NoActionInfoByPrize
    {
        internal NoActionInfoByPrizeEmpty() : base()
        {

        }

        internal override bool HasNumber(int[] numbers)
        {
            return false;
        }

        internal override IEnumerable<CompletedWager> Wagers()
        {
            var empty = new List<CompletedWager>();
            return empty;
        }
    }

    internal class WinnerInfoByPrizePick2Straight : WinnerInfoByPrize
    {

        internal WinnerInfoByPrizePick2Straight(WinnerInfo ticket) : base(ticket, PrizesPicks.PRIZE_CRITERIA)
        {

        }

        internal override bool HasNumber(int[] numbers)
        {
            var result = Subtickets.Cast<SubTicket<IPick>>().Any(x => x.IsTheSameNumberWithTheSameOrder(numbers[0], numbers[1]));
            return result;
        }
    }

    internal class WinnerInfoByPrizePick2Boxed : WinnerInfoByPrize
    {
        internal WinnerInfoByPrizePick2Boxed(WinnerInfo ticket, int prizeCriteria) : base(ticket, prizeCriteria)
        {

        }

        internal override bool HasNumber(int[] numbers)
        {
            var result = Subtickets.Cast<SubTicket<IPick>>().Any(x => x.IsTheSameNumberWithDifferentOrder(numbers[0], numbers[1]));
            return result;
        }

        internal LoserInfoByPrize TransformToLoserInfoByPrizeBoxed()
        {
            var loserInfoByPrize = new LoserInfoByPrizePick2Boxed(WinnerInfo.TransformToLoserInfo(), base.PrizeCriteria);
            loserInfoByPrize.Add(this.Subtickets);
            return loserInfoByPrize;
        }
    }

    internal class WinnerInfoByPrizePick3Straight : WinnerInfoByPrize
    {
        internal WinnerInfoByPrizePick3Straight(WinnerInfo ticket) : base(ticket, PrizesPicks.PRIZE_CRITERIA)
        {

        }

        internal override bool HasNumber(int[] numbers)
        {
            var result = Subtickets.Cast<SubTicket<IPick>>().Any(x => x.IsTheSameNumberWithTheSameOrder(numbers[0], numbers[1], numbers[2]));
            return result;
        }
    }

    internal class WinnerInfoByPrizePick3Boxed : WinnerInfoByPrize
    {

        internal WinnerInfoByPrizePick3Boxed(WinnerInfo ticket, int prizeCriteria) : base(ticket, prizeCriteria)
        {

        }

        internal override bool HasNumber(int[] numbers)
        {
            var result = Subtickets.Cast<SubTicket<IPick>>().Any(x => x.IsTheSameNumberWithDifferentOrder(numbers[0], numbers[1], numbers[2]));
            return result;
        }

        internal LoserInfoByPrize TransformToLoserInfoByPrizeBoxed()
        {
            var loserInfoByPrize = new LoserInfoByPrizePick3Boxed(WinnerInfo.TransformToLoserInfo(), base.PrizeCriteria);
            loserInfoByPrize.Add(this.Subtickets);
            return loserInfoByPrize;
        }
    }

    internal class WinnerInfoByPrizePick4Straight : WinnerInfoByPrize
    {
        internal WinnerInfoByPrizePick4Straight(WinnerInfo ticket) : base(ticket, PrizesPicks.PRIZE_CRITERIA)
        {

        }

        internal override bool HasNumber(int[] numbers)
        {
            var result = Subtickets.Cast<SubTicket<IPick>>().Any(x => x.IsTheSameNumberWithTheSameOrder(numbers[0], numbers[1], numbers[2], numbers[3]));
            return result;
        }
    }

    internal class WinnerInfoByPrizePick4Boxed : WinnerInfoByPrize
    {
        internal WinnerInfoByPrizePick4Boxed(WinnerInfo ticket, int prizeCriteria) : base(ticket, prizeCriteria)
        {

        }

        internal override bool HasNumber(int[] numbers)
        {
            var result = Subtickets.Cast<SubTicket<IPick>>().Any(x => x.IsTheSameNumberWithDifferentOrder(numbers[0], numbers[1], numbers[2], numbers[3]));
            return result;
        }

        internal LoserInfoByPrize TransformToLoserInfoByPrizeBoxed()
        {
            var loserInfoByPrize = new LoserInfoByPrizePick4Boxed(WinnerInfo.TransformToLoserInfo(), base.PrizeCriteria);
            loserInfoByPrize.Add(this.Subtickets);
            return loserInfoByPrize;
        }
    }

    internal class WinnerInfoByPrizePick5Straight : WinnerInfoByPrize
    {
        internal WinnerInfoByPrizePick5Straight(WinnerInfo ticket) : base(ticket, PrizesPicks.PRIZE_CRITERIA)
        {

        }

        internal override bool HasNumber(int[] numbers)
        {
            var result = Subtickets.Cast<SubTicket<IPick>>().Any(x => x.IsTheSameNumberWithTheSameOrder(numbers[0], numbers[1], numbers[2], numbers[3], numbers[4]));
            return result;
        }
    }

    internal class WinnerInfoByPrizePick5Boxed : WinnerInfoByPrize
    {
        internal WinnerInfoByPrizePick5Boxed(WinnerInfo ticket, int prizeCriteria) : base(ticket, prizeCriteria)
        {

        }

        internal override bool HasNumber(int[] numbers)
        {
            var result = Subtickets.Cast<SubTicket<IPick>>().Any(x => x.IsTheSameNumberWithDifferentOrder(numbers[0], numbers[1], numbers[2], numbers[3], numbers[4]));
            return result;
        }

        internal LoserInfoByPrize TransformToLoserInfoByPrizeBoxed()
        {
            var loserInfoByPrize = new LoserInfoByPrizePick5Boxed(WinnerInfo.TransformToLoserInfo(), base.PrizeCriteria);
            loserInfoByPrize.Add(this.Subtickets);
            return loserInfoByPrize;
        }
    }

    internal class WinnerInfoByPrizePowerball : WinnerInfoByPrize
    {
        internal WinnerInfoByPrizePowerball(WinnerInfo ticket) : base(ticket, PrizesPicks.PRIZE_CRITERIA)
        {

        }

        internal override bool HasNumber(int[] numbers)
        {
            var result = MessageInfo.Ticket==$"{ MessageInfo.TicketType.ToString()}[{numbers[0]}{numbers[1]},{numbers[2]}{numbers[3]},{numbers[4]}{numbers[5]},{numbers[6]}{numbers[7]},{numbers[8]}{numbers[9]},{numbers[10]}{numbers[11]}]";
            return result;
        }
    }

    

    public sealed class LoserInfo : TicketPicksMessage
    {
        private string draw;
        private string gradedBy;

        internal LoserInfo(Company company, string draw, int? fireball, string gradedBy, string stateAbb, int hour, int minute, int year, int month, int day, string accountNumber, string ticket, int countOfTickets, 
            decimal amount, Ticket.Selection selectionMode, GameboardStatus action, int drawingId, TypeNumberSequence position, string drawingName, DateTime creation, int orderNumber, int ticketNumber, 
            string subticketsAndWagerNumbers, decimal profit, int prizesVersion, int domainId, string domainUrl, int currencyId) :
            base(stateAbb, hour, minute, year, month, day, accountNumber, ticket, countOfTickets, amount, selectionMode, action, drawingId, position, drawingName, creation, orderNumber, ticketNumber, subticketsAndWagerNumbers, 
                prizesVersion, domainId, domainUrl, currencyId, GradeTicketType.LOSER_TICKET)
        {
            this.Company = company;
            this.draw = draw;
            Fireball = fireball == null ? LotteryDraw.WITHOUT_FIREBALL : (int)fireball;
            this.gradedBy= gradedBy;
            this.profit = profit;
            ValidateFields();
        }

        internal LoserInfo(Company company, string accountNumber, string ticket, int countOfTickets,
            decimal amount, Ticket.Selection selectionMode, GameboardStatus action, string drawingName, DateTime creation, int orderNumber, int ticketNumber,
            string subticketsAndWagerNumbers, decimal profit, int prizesVersion, int domainId, string domainUrl, int currencyId) :
            base(accountNumber, ticket, countOfTickets, amount, selectionMode, action, drawingName, creation, orderNumber, ticketNumber,
                subticketsAndWagerNumbers, prizesVersion, domainId, domainUrl, currencyId, GradeTicketType.LOSER_TICKET)
        {
            this.Company = company;
            this.profit = profit;
        }

        public LoserInfo(string message) : base(message)
        {
            
        }

        internal void CompleteInfo(string stateAbb, DateTime drawDate, int drawingId, string gradedBy, string draw, int fireball)
        {
            base.CompleteInfo(stateAbb, drawDate, drawingId);
            this.draw = draw;
            Fireball = fireball;
            this.gradedBy = gradedBy;
        }

        public LoserInfo(int hour, int minute, int year, int month, int day, string draw, string gradedBy, int drawingId, TypeNumberSequence position, int fireball) :
            base(string.Empty, hour, minute, year, month, day, string.Empty, string.Empty, 0, 0, Selection.BALLS, GameboardStatus.UNPRIZED, drawingId, position, string.Empty, DateTime.MinValue, 0, 0, string.Empty,
                0, 0, string.Empty, 0, GradeTicketType.TEMP_TICKET)
        {
            SetDate(hour, minute, year, month, day);
            Draw = draw;
            Fireball = fireball;
            GradedBy = gradedBy;
        }

        protected override void Deserialize(string[] serializatedMessage, out int fieldOrder)
		{
            base.Deserialize(serializatedMessage, out fieldOrder);
            this.Company = null;
            profit = decimal.Parse(serializatedMessage[fieldOrder++]);
        }

		private void ValidateFields()
        {
            if (String.IsNullOrWhiteSpace(this.draw)) throw new ArgumentNullException("Draw can not be null");
            if (String.IsNullOrWhiteSpace(this.gradedBy)) throw new ArgumentNullException("Graded by can not be null");
        }

        internal override void SetValuesFromTicket(Ticket ticket)
        {
            base.SetValuesFromTicket(ticket);
            Profit = ticket.Profit();
            CountOfTickets = ticket.Count;
            SelectionMode = ticket.SelectionMode;
            SubticketsAndWagerNumbers = ticket.SubticketsAndWagerNumbersAsString();
            CurrencyId = Coinage.Coin(ticket.Order.CurrencyCode).Id;
        }

        public string Draw
        {
            set
            {
                this.draw = value;
            }
            get
            {
                return this.draw;
            }
        }

        public string WinnerDigits()
        {
            if (((TicketType == TicketType.P2S || TicketType == TicketType.P2B) && draw.Length == 2) ||
                ((TicketType == TicketType.P3S || TicketType == TicketType.P3B) && draw.Length == 3) ||
                ((TicketType == TicketType.P4S || TicketType == TicketType.P4B) && draw.Length == 4) ||
                (TicketType == TicketType.P5S || TicketType == TicketType.P5B))
                return draw;

            switch (TicketType)
            {
                case TicketType.P2S:
                case TicketType.P2B:
                    return Position == TypeNumberSequence.BEGINNING ? draw.Substring(0, 2) : draw.Substring(3, 2);
                case TicketType.P3S:
                case TicketType.P3B:
                    return draw.Substring(2, 3);
                case TicketType.P4S:
                case TicketType.P4B:
                    return draw.Substring(1, 4);
                default:
                    throw new GameEngineException($"Invalid ticket type {TicketType}");
            }
        }

        internal int Fireball { get; set; }

        internal bool BelongsToFireBallDraw => Fireball != LotteryDraw.WITHOUT_FIREBALL;

        public string GradedBy
        {
            set
            {
                this.gradedBy = value;
            }
            get
            {
                return this.gradedBy;
            }
        }

        private decimal profit;
        internal decimal Profit
        {
            get
            {
                return profit;
            }
            set
            {
                profit = value;
            }
        }

        internal bool WasPurchasedForFree
        {
            get
            {
                var result = profit == 0m;
                return result;
            }
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(profit);
        }

        internal LoserInfo(LoserInfo commonInfo): 
            base(commonInfo.StateAbb, commonInfo.Hour, commonInfo.Minute, commonInfo.Year, commonInfo.Month, commonInfo.Day, string.Empty, string.Empty, 0, 0, Selection.NONE, GameboardStatus.UNKNOWN, commonInfo.DrawingId, commonInfo.Position, string.Empty, DateTime.MinValue, 0, 0, string.Empty,
                0, 0, string.Empty, 0, GradeTicketType.LOSER_TICKET)
        {
            this.Draw = commonInfo.Draw;
            this.GradedBy = commonInfo.GradedBy;
            profit = commonInfo.profit;
        }
    }

    internal class CompletedLosersTicketsByOrderAndPrize : Objeto
    {
        internal List<LoserInfo> Tickets { get; private set; } = new List<LoserInfo>();
        internal int OrderNumber { get; private set; }
        internal decimal PrizeToWinForOneDollar { get; private set; }
        internal string StateAbb => Tickets.First().StateAbb;
        internal DateTime DrawDate => Tickets.First().DrawDate();
        internal LoserInfo FirstTicket => Tickets.First();
        public CompletedLosersTicketsByOrderAndPrize(LoserInfo ticket, decimal prizeToWinForOneDollar)
        {
            if (ticket == null) throw new ArgumentNullException(nameof(ticket));
            if (prizeToWinForOneDollar <= 0) throw new GameEngineException($"{nameof(prizeToWinForOneDollar)} {prizeToWinForOneDollar} must be greater than zero");
            Tickets.Add(ticket);
            OrderNumber = ticket.OrderNumber;
            PrizeToWinForOneDollar = prizeToWinForOneDollar;
        }
        internal void Add(LoserInfo ticket, decimal prizeToWinForOneDollar)
        {
            if (ticket == null) throw new ArgumentNullException(nameof(ticket));
            if (prizeToWinForOneDollar != PrizeToWinForOneDollar) throw new GameEngineException($"Tickets must have the prize");
            if (ticket.OrderNumber != OrderNumber) throw new GameEngineException("Tickets must have the same order number");
            if (ticket.StateAbb != StateAbb) throw new GameEngineException("Tickets must have the same state");
            if (ticket.DrawDate() != DrawDate) throw new GameEngineException("Tickets must have the same draw date");
            Tickets.Add(ticket);
        }
        internal decimal TicketAmount()
        {
            var amount = FirstTicket.TicketAmount();
            return amount;
        }
        internal string TicketAmountFormatted()
        {
            var result = Currency.Factory(Coinage.GetById(FirstTicket.CurrencyId), TicketAmount()).ToDisplayFormat();
            return result;
        }
        internal string WonAmountFormatted()
        {
            var result = Currency.ZeroFactory(Coinage.GetById(FirstTicket.CurrencyId).Iso4217Code).ToDisplayFormat();
            return result;
        }
        internal IEnumerable<CompletedWager> Wagers()
        {
            var wagers = new List<CompletedWager>();
            foreach (var ticket in Tickets)
            {
                wagers.AddRange(ticket.Wagers());
            }
            return wagers;
        }
    }

    internal abstract class LoserInfoByPrize : MessageInfoByPrize
    {
        public LoserInfo LoserInfo
        {
            get
            {
                var loserInfo = MessageInfo as LoserInfo;
                return loserInfo;
            }
        }

        internal LoserInfoByPrize(LoserInfo loserInfo, int prizeCriteria) : base(loserInfo, prizeCriteria)
        {
        }

        protected LoserInfoByPrize() : base()
        {

        }
    }

    internal class LoserInfoByPrizeWithTotalSubtickets : Objeto
    {
        private readonly List<LoserInfoByPrize> ticketsByPrize = new List<LoserInfoByPrize>();
        internal IEnumerable<LoserInfoByPrize> TicketsByPrize
        {
            get
            {
                return ticketsByPrize;
            }
        }

        public LoserInfoByPrizeWithTotalSubtickets(IEnumerable<LoserInfoByPrize> ticketsByPrize)
        {
            this.ticketsByPrize = ticketsByPrize.ToList();
        }

        internal int TotalSubtickets()
        {
            var count = ticketsByPrize.Sum(x => x.Count);
            return count;
        }
    }

    internal class LoserInfoByPrizePick2Straight : LoserInfoByPrize
    {
        internal LoserInfoByPrizePick2Straight(LoserInfo ticket) : base(ticket, PrizesPicks.PRIZE_CRITERIA)
        {

        }

        internal override bool HasNumber(int[] numbers)
        {
            var result = Subtickets.Cast<SubTicket<IPick>>().Any(x => x.IsTheSameNumberWithTheSameOrder(numbers[0], numbers[1]));
            return result;
        }
    }

    internal class LoserInfoByPrizePick2Boxed : LoserInfoByPrize
    {

        internal LoserInfoByPrizePick2Boxed(LoserInfo ticket, int prizeCriteria) : base(ticket, prizeCriteria)
        {

        }

        internal override bool HasNumber(int[] numbers)
        {
            var result = Subtickets.Cast<SubTicket<IPick>>().Any(x => x.IsTheSameNumberWithDifferentOrder(numbers[0], numbers[1]));
            return result;
        }
    }


    internal class LoserInfoByPrizePick3Straight : LoserInfoByPrize
    {
        internal LoserInfoByPrizePick3Straight(LoserInfo ticket) : base(ticket, PrizesPicks.PRIZE_CRITERIA)
        {

        }

        internal override bool HasNumber(int[] numbers)
        {
            var result = Subtickets.Cast<SubTicket<IPick>>().Any(x => x.IsTheSameNumberWithTheSameOrder(numbers[0], numbers[1], numbers[2]));
            return result;
        }
    }

    internal class LoserInfoByPrizePick3Boxed : LoserInfoByPrize
    {
        internal LoserInfoByPrizePick3Boxed(LoserInfo ticket, int prizeCriteria) : base(ticket, prizeCriteria)
        {

        }

        internal override bool HasNumber(int[] numbers)
        {
            var result = Subtickets.Cast<SubTicket<IPick>>().Any(x => x.IsTheSameNumberWithDifferentOrder(numbers[0], numbers[1], numbers[2]));
            return result;
        }
    }

    internal class LoserInfoByPrizePick4Straight : LoserInfoByPrize
    {
        internal LoserInfoByPrizePick4Straight(LoserInfo ticket) : base(ticket, PrizesPicks.PRIZE_CRITERIA)
        {

        }

        internal override bool HasNumber(int[] numbers)
        {
            var result = Subtickets.Cast<SubTicket<IPick>>().Any(x => x.IsTheSameNumberWithTheSameOrder(numbers[0], numbers[1], numbers[2], numbers[3]));
            return result;
        }
    }

    internal class LoserInfoByPrizePick4Boxed : LoserInfoByPrize
    {
        internal LoserInfoByPrizePick4Boxed(LoserInfo ticket, int prizeCriteria) : base(ticket, prizeCriteria)
        {

        }

        internal override bool HasNumber(int[] numbers)
        {
            var result = Subtickets.Cast<SubTicket<IPick>>().Any(x => x.IsTheSameNumberWithDifferentOrder(numbers[0], numbers[1], numbers[2], numbers[3]));
            return result;
        }
    }

    internal class LoserInfoByPrizePick5Straight : LoserInfoByPrize
    {
        internal LoserInfoByPrizePick5Straight(LoserInfo ticket) : base(ticket, PrizesPicks.PRIZE_CRITERIA)
        {

        }

        internal override bool HasNumber(int[] numbers)
        {
            var result = Subtickets.Cast<SubTicket<IPick>>().Any(x => x.IsTheSameNumberWithTheSameOrder(numbers[0], numbers[1], numbers[2], numbers[3], numbers[4]));
            return result;
        }
    }

    internal class LoserInfoByPrizePick5Boxed : LoserInfoByPrize
    {
        internal LoserInfoByPrizePick5Boxed(LoserInfo ticket, int prizeCriteria) : base(ticket, prizeCriteria)
        {

        }

        internal override bool HasNumber(int[] numbers)
        {
            var result = Subtickets.Cast<SubTicket<IPick>>().Any(x => x.IsTheSameNumberWithDifferentOrder(numbers[0], numbers[1], numbers[2], numbers[3], numbers[4]));
            return result;
        }
    }

    internal class LoserInfoByPrizePowerball : LoserInfoByPrize
    {
        internal LoserInfoByPrizePowerball(LoserInfo ticket) : base(ticket, PrizesPicks.PRIZE_CRITERIA)
        {

        }

        internal override bool HasNumber(int[] numbers)
        {
            var result = MessageInfo.Ticket == $"{MessageInfo.TicketType.ToString()}[{numbers[0]}{numbers[1]},{numbers[2]}{numbers[3]},{numbers[4]}{numbers[5]},{numbers[6]}{numbers[7]},{numbers[8]}{numbers[9]},{numbers[10]}{numbers[11]}]";
            return result;
        }
    }

    public sealed class NoActionInfo : TicketPicksMessage
    {
        private string noActionBy;

        internal NoActionInfo(Company company, string noActionBy, string stateAbb, int hour, int minute, int year, int month, int day, string accountNumber, string ticket, int countOfTickets, 
            decimal amount, Ticket.Selection selectionMode, GameboardStatus action, int drawingId, TypeNumberSequence position, string drawingName, DateTime creation, int orderNumber, int ticketNumber, 
            string subticketsAndWagerNumbers, int prizesVersion, int domainId, string domainUrl, int currencyId) :
            base(stateAbb, hour, minute, year, month, day, accountNumber, ticket, countOfTickets, amount, selectionMode, action, drawingId, position, drawingName, creation, orderNumber, ticketNumber, subticketsAndWagerNumbers, 
                prizesVersion, domainId, domainUrl, currencyId, GradeTicketType.NOACTION_TICKET)
        {
            this.Company = company;
            this.noActionBy = noActionBy;
        }

        internal NoActionInfo(Company company, string accountNumber, string ticket, int countOfTickets,
            decimal amount, Ticket.Selection selectionMode, GameboardStatus action, string drawingName, DateTime creation, int orderNumber, int ticketNumber,
            string subticketsAndWagerNumbers, int prizesVersion, int domainId, string domainUrl, int currencyId) :
            base(accountNumber, ticket, countOfTickets, amount, selectionMode, action, drawingName, creation, orderNumber, ticketNumber,
                subticketsAndWagerNumbers, prizesVersion, domainId, domainUrl, currencyId, GradeTicketType.NOACTION_TICKET)
        {
            this.Company = company;
        }

        public NoActionInfo(string message) : base(message)
        {
            
        }

        internal void CompleteInfo(string stateAbb, DateTime drawDate, int drawingId, string gradedBy)
        {
            base.CompleteInfo(stateAbb, drawDate, drawingId);
            this.noActionBy = gradedBy;
        }

		protected override void Deserialize(string[] serializatedMessage, out int fieldOrder)
		{
            base.Deserialize(serializatedMessage, out fieldOrder);
            this.Company = null;
        }

        public string NoActionBy
        {
            get
            {
                return this.noActionBy;
            }
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();

        }
    }

    internal class CompletedNoActionTicketsByOrderAndPrize : Objeto
    {
        internal List<NoActionInfo> Tickets { get; private set; } = new List<NoActionInfo>();
        internal int OrderNumber { get; private set; }
        internal decimal PrizeToWinForOneDollar { get; private set; }
        internal string StateAbb => Tickets.First().StateAbb;
        internal DateTime DrawDate => Tickets.First().DrawDate();
        internal NoActionInfo FirstTicket => Tickets.First();
        public CompletedNoActionTicketsByOrderAndPrize(NoActionInfo ticket, decimal prizeToWinForOneDollar)
        {
            if (ticket == null) throw new ArgumentNullException(nameof(ticket));
            if (prizeToWinForOneDollar <= 0) throw new GameEngineException($"{nameof(prizeToWinForOneDollar)} {prizeToWinForOneDollar} must be greater than zero");
            Tickets.Add(ticket);
            OrderNumber = ticket.OrderNumber;
            PrizeToWinForOneDollar = prizeToWinForOneDollar;
        }
        internal void Add(NoActionInfo ticket, decimal prizeToWinForOneDollar)
        {
            if (ticket == null) throw new ArgumentNullException(nameof(ticket));
            if (prizeToWinForOneDollar != PrizeToWinForOneDollar) throw new GameEngineException($"Tickets must have the prize");
            if (ticket.OrderNumber != OrderNumber) throw new GameEngineException("Tickets must have the same order number");
            if (ticket.StateAbb != StateAbb) throw new GameEngineException("Tickets must have the same state");
            if (ticket.DrawDate() != DrawDate) throw new GameEngineException("Tickets must have the same draw date");
            Tickets.Add(ticket);
        }
        internal decimal TicketAmount()
        {
            var amount = FirstTicket.TicketAmount();
            return amount;
        }
        internal string TicketAmountFormatted()
        {
            var result = Currency.Factory(Coinage.GetById(FirstTicket.CurrencyId), TicketAmount()).ToDisplayFormat();
            return result;
        }
        internal string WonAmountFormatted()
        {
            var result = Currency.ZeroFactory(Coinage.GetById(FirstTicket.CurrencyId).Iso4217Code).ToDisplayFormat();
            return result;
        }
        internal IEnumerable<CompletedWager> Wagers()
        {
            var wagers = new List<CompletedWager>();
            foreach (var ticket in Tickets)
            {
                wagers.AddRange(ticket.Wagers());
            }
            return wagers;
        }
    }

    internal abstract class NoActionInfoByPrize : MessageInfoByPrize
    {
        internal NoActionInfo NoActionInfo
        {
            get
            {
                var noActionInfo = MessageInfo as NoActionInfo;
                return noActionInfo;
            }
        }

        protected NoActionInfoByPrize(NoActionInfo noActionInfo, int prizeCriteria) : base(noActionInfo, prizeCriteria)
        {
        }

        protected NoActionInfoByPrize() : base()
        {
        }
    }

    internal class NoActionInfoByPrizeWithTotalSubtickets : Objeto
    {
        private readonly List<NoActionInfoByPrize> ticketsByPrize = new List<NoActionInfoByPrize>();
        internal IEnumerable<NoActionInfoByPrize> TicketsByPrize
        {
            get
            {
                return ticketsByPrize;
            }
        }

        public NoActionInfoByPrizeWithTotalSubtickets(IEnumerable<NoActionInfoByPrize> ticketsByPrize)
        {
            this.ticketsByPrize = ticketsByPrize.ToList();
        }

        internal int TotalSubtickets()
        {
            var count = ticketsByPrize.Sum(x => x.Count);
            return count;
        }
    }

    internal class NoActionInfoByPrizePick2Straight : NoActionInfoByPrize
    {
        internal NoActionInfoByPrizePick2Straight(NoActionInfo ticket) : base(ticket, PrizesPicks.PRIZE_CRITERIA)
        {

        }

        internal override bool HasNumber(int[] numbers)
        {
            var result = Subtickets.Cast<SubTicket<IPick>>().Any(x => x.IsTheSameNumberWithTheSameOrder(numbers[0], numbers[1]));
            return result;
        }
    }

    internal class NoActionInfoByPrizePick2Boxed : NoActionInfoByPrize
    {
        internal NoActionInfoByPrizePick2Boxed(NoActionInfo ticket, int prizeCriteria) : base(ticket, prizeCriteria)
        {

        }

        internal override bool HasNumber(int[] numbers)
        {
            var result = Subtickets.Cast<SubTicket<IPick>>().Any(x => x.IsTheSameNumberWithDifferentOrder(numbers[0], numbers[1]));
            return result;
        }
    }

    internal class NoActionInfoByPrizePick3Straight : NoActionInfoByPrize
    {
        internal NoActionInfoByPrizePick3Straight(NoActionInfo ticket) : base(ticket, PrizesPicks.PRIZE_CRITERIA)
        {

        }

        internal override bool HasNumber(int[] numbers)
        {
            var result = Subtickets.Cast<SubTicket<IPick>>().Any(x => x.IsTheSameNumberWithTheSameOrder(numbers[0], numbers[1], numbers[2]));
            return result;
        }
    }

    internal class NoActionInfoByPrizePick3Boxed : NoActionInfoByPrize
    {
        internal NoActionInfoByPrizePick3Boxed(NoActionInfo ticket, int prizeCriteria) : base(ticket, prizeCriteria)
        {

        }

        internal override bool HasNumber(int[] numbers)
        {
            var result = Subtickets.Cast<SubTicket<IPick>>().Any(x => x.IsTheSameNumberWithDifferentOrder(numbers[0], numbers[1], numbers[2]));
            return result;
        }
    }

    internal class NoActionInfoByPrizePick4Straight : NoActionInfoByPrize
    {
        internal NoActionInfoByPrizePick4Straight(NoActionInfo ticket) : base(ticket, PrizesPicks.PRIZE_CRITERIA)
        {

        }

        internal override bool HasNumber(int[] numbers)
        {
            var result = Subtickets.Cast<SubTicket<IPick>>().Any(x => x.IsTheSameNumberWithTheSameOrder(numbers[0], numbers[1], numbers[2], numbers[3]));
            return result;
        }
    }

    internal class NoActionInfoByPrizePick4Boxed : NoActionInfoByPrize
    {
        internal NoActionInfoByPrizePick4Boxed(NoActionInfo ticket, int prizeCriteria) : base(ticket, prizeCriteria)
        {

        }

        internal override bool HasNumber(int[] numbers)
        {
            var result = Subtickets.Cast<SubTicket<IPick>>().Any(x => x.IsTheSameNumberWithDifferentOrder(numbers[0], numbers[1], numbers[2], numbers[3]));
            return result;
        }
    }

    internal class NoActionInfoByPrizePick5Straight : NoActionInfoByPrize
    {
        internal NoActionInfoByPrizePick5Straight(NoActionInfo ticket) : base(ticket, PrizesPicks.PRIZE_CRITERIA)
        {

        }

        internal override bool HasNumber(int[] numbers)
        {
            var result = Subtickets.Cast<SubTicket<IPick>>().Any(x => x.IsTheSameNumberWithTheSameOrder(numbers[0], numbers[1], numbers[2], numbers[3], numbers[4]));
            return result;
        }
    }

    internal class NoActionInfoByPrizePick5Boxed : NoActionInfoByPrize
    {
        internal NoActionInfoByPrizePick5Boxed(NoActionInfo ticket, int prizeCriteria) : base(ticket, prizeCriteria)
        {

        }

        internal override bool HasNumber(int[] numbers)
        {
            var result = Subtickets.Cast<SubTicket<IPick>>().Any(x => x.IsTheSameNumberWithDifferentOrder(numbers[0], numbers[1], numbers[2], numbers[3], numbers[4]));
            return result;
        }
    }

    internal class NoActionInfoByPrizePowerball : NoActionInfoByPrize
    {
        internal NoActionInfoByPrizePowerball(NoActionInfo ticket, int prizeCriteria) : base(ticket, prizeCriteria)
        {

        }

        internal override bool HasNumber(int[] numbers)
        {
            var result = MessageInfo.Ticket == $"{MessageInfo.TicketType.ToString()}[{numbers[0]}{numbers[1]},{numbers[2]}{numbers[3]},{numbers[4]}{numbers[5]},{numbers[6]}{numbers[7]},{numbers[8]}{numbers[9]},{numbers[10]}{numbers[11]}]";
            return result;
        }
    }

}
