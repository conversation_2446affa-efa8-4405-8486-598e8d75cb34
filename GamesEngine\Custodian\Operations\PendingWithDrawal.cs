﻿using GamesEngine.Domains;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using GamesEngine.Settings;
using System;
using System.Collections.Generic;
using GamesEngine.Custodian;
using town.connectors.drivers;
using static GamesEngine.Business.WholePaymentProcessor;

namespace GamesEngine.Custodian.Operations
{
	internal sealed class PendingWithDrawal : WithDrawal
	{
		private readonly Guardian guardian;
		internal RequiredProfilesApproval RequiredProfilesApproval { get;}
		internal int Approvals
		{
			get
			{
				return RequiredProfilesApproval.Approved();
			}
		}
		internal int ApprovalsRequired
		{
			get
			{
				return RequiredProfilesApproval.Count();
			}
		}
		internal PendingWithDrawal(Guardian guardian, DateTime creationDate, int transactionId, int referenceNumber, Currency disbursementAmount, int group, PaymentProcessor processor, Domain domain, 
			Profiles requiredProfilesApprove, string identificationDocumentNumber, string accountNumber, string employeeName, string destinationAddress)
			: base(guardian, StatusCodes.PENDING, TransactionType.Withdrawal, creationDate, transactionId, referenceNumber, disbursementAmount, group, processor, domain, identificationDocumentNumber, accountNumber, employeeName, destinationAddress)
		{
			if (requiredProfilesApprove == null) throw new ArgumentNullException(nameof(requiredProfilesApprove));

			this.guardian = guardian;
			RequiredProfilesApproval = new RequiredProfilesApproval(requiredProfilesApprove);
		}
		internal bool Approve(bool itIsThePresent, DateTime date, Approver approver)
		{
			if (approver == null) throw new ArgumentNullException(nameof(approver));
			if (ApproversWhoAlreadyApprove == null) ApproversWhoAlreadyApprove = new Approvers();
			if (!Scheduled) throw new GameEngineException($"The transaction {TransactionId} it's not scheduled yet.");

			Profiles profilesWhichApproverRepresents = approver.ProfilesWhereWasAdded.Intersection(RequiredProfilesApproval.NotApproved());

			RequiredProfilesApproval.Approve(profilesWhichApproverRepresents);
			if(!ApproversWhoAlreadyApprove.Exists(approver)) ApproversWhoAlreadyApprove.Add(approver);

			bool hasEnoughApprovals = Approvals >= ApprovalsRequired;
			return hasEnoughApprovals;

		}
		internal bool Approve(bool itIsThePresent, DateTime date, Approver approver, Profiles profiles)
		{
			if (approver == null) throw new ArgumentNullException(nameof(approver));
			if (ApproversWhoAlreadyApprove == null) ApproversWhoAlreadyApprove = new Approvers();
			//if (!Scheduled) throw new GameEngineException($"The transaction {TransactionId} it's not scheduled yet.");
			if ( ! profiles.CanBeApprovedBy(approver)) throw new GameEngineException($"Only an {nameof(Approver)} which belong to the {nameof(Profile)} or an {nameof(Owner)} can approve in behalf other {nameof(Profiles)}.");
			
			Profiles profilesWhichApproverRepresents = profiles.Intersection(RequiredProfilesApproval.NotApproved());

			RequiredProfilesApproval.Approve(profilesWhichApproverRepresents);
			if (!ApproversWhoAlreadyApprove.Exists(approver)) ApproversWhoAlreadyApprove.Add(approver);

			bool hasEnoughApprovals = Approvals >= ApprovalsRequired;
			return hasEnoughApprovals;

		}
		internal override InternalOperationUpdateMessage GenerateMessage(DateTime now)
		{
			Approvers approvers = (ApproversWhoAlreadyApprove == null) ? new Approvers() : ApproversWhoAlreadyApprove;

			return new InternalOperationUpdateMessage(
				Abbreviation,
				CreationDate,
				now,
				TransactionId,
				ProcessorAccount.Id,
				ReferenceNumber,
				Description,
				DisbursementAmount,
				Group,
				Processor.Driver.Id,
				Approvals,
				ApprovalsRequired,
				Scheduled,
				Domain.Url,
				approvers,
				Status,
				false,
				CalculatePercentajeOfExecution(),
				Identifier,
                AccountNumber,
				DestinationAddress
                );
		}
	}
}
