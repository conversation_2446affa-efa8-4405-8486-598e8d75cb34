﻿using Elastic.Clients.Elasticsearch.Xpack;
using GamesEngine.Business.Liquidity.Containers;
using GamesEngine.Finance;
using GamesEngine.Settings;
using MySql.Data.MySqlClient;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static GamesEngine.Business.Liquidity.Persistence.DataModel;
using Bottle = GamesEngine.Business.Liquidity.Persistence.DataModel.Bottle;
using Dispenser = GamesEngine.Business.Liquidity.Persistence.DataModel.Dispenser;
using Tank = GamesEngine.Business.Liquidity.Persistence.DataModel.Tank;
using Tanker = GamesEngine.Business.Liquidity.Persistence.DataModel.Tanker;

namespace GamesEngine.Business.Liquidity.Persistence
{
    public class MySqlStorage : IStorage
    {
        private readonly string _connectionString;

        private const string BaseDepositTableName = "deposit";
        private const string BaseJarTableName = "jar";
        private const string BaseTankTableName = "tank";
        private const string BaseTankerTableName = "tanker";
        private const string BaseJarDetailTableName = "jardetail";
        private const string BaseTankDetailTableName = "tankdetail";
        private const string BaseTankerDetailTableName = "tankerdetail";
        private const string BaseWithdrawalTableName = "withdrawal";
        private const string BaseBottleTableName = "bottle";
        private const string BaseDispenserTableName = "dispenser";
        private const string BaseBottleDetailTableName = "bottledetail";
        private const string BaseDispenserDetailTableName = "dispenserdetail";

        public MySqlStorage(string connectionString)
        {
            _connectionString = connectionString ?? throw new ArgumentNullException(nameof(connectionString));
        }

        private string GetDynamicTableName(string baseName, string kind)
        {
            if (string.IsNullOrWhiteSpace(kind)) throw new ArgumentException("Kind cannot be null or whitespace for dynamic table naming.", nameof(kind));
            
            var sanitizedKind = new string(kind.Where(char.IsLetterOrDigit).ToArray());
            if (string.IsNullOrWhiteSpace(sanitizedKind)) throw new ArgumentException("Sanitized kind results in an empty string.", nameof(kind));
            
            return $"`{baseName}_{sanitizedKind}`";
        }

        private void ExecuteCreateTableMySql(string tableName, string createTableSql)
        {
            try
            {
                using (var connection = new MySqlConnection(_connectionString))
                {
                    connection.Open();
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = createTableSql;
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (MySqlException ex)
            {
                Loggers.GetIntance().Db.Error($"MySQL Error creating table `{tableName}`: {createTableSql}", ex);
                ErrorsSender.Send(ex, createTableSql);
                throw;
            }
        }

        public void CreateTablesIfNotExists(string kind)
        {
            string depositTableName = GetDynamicTableName(BaseDepositTableName, kind);
            string createDepositTableSql = $@"
                CREATE TABLE IF NOT EXISTS {depositTableName} (
                    `Id` BIGINT PRIMARY KEY,
                    `DocumentNumber` VARCHAR(45) NOT NULL,
                    `Amount` DECIMAL(16, 8) NOT NULL,
                    `Date` DATETIME NOT NULL,
                    `Store` TINYINT UNSIGNED NOT NULL,
                    `AccountNumber` VARCHAR(60) NOT NULL,
                    `DomainId` INT NOT NULL,
                    `Address` VARCHAR(255) NOT NULL,
                    `Created` DATETIME NOT NULL
                ) ENGINE=InnoDB CHARSET=utf8;";
            ExecuteCreateTableMySql(depositTableName, createDepositTableSql);

            string jarTableName = GetDynamicTableName(BaseJarTableName, kind);
            string createJarTableSql = $@"
                CREATE TABLE IF NOT EXISTS {jarTableName} (
                    `Version` BIGINT PRIMARY KEY,
                    `Description` TEXT NULL,
                    `Created` DATETIME NOT NULL
                ) ENGINE=InnoDB CHARSET=utf8;";
            ExecuteCreateTableMySql(jarTableName, createJarTableSql);

            string tankTableName = GetDynamicTableName(BaseTankTableName, kind);
            string createTankTableSql = $@"
                CREATE TABLE IF NOT EXISTS {tankTableName} (
                    `Id` BIGINT PRIMARY KEY,
                    `Description` TEXT NULL,
                    `Created` DATETIME NOT NULL
                ) ENGINE=InnoDB CHARSET=utf8;";
            ExecuteCreateTableMySql(tankTableName, createTankTableSql);

            string tankerTableName = GetDynamicTableName(BaseTankerTableName, kind);
            string createTankerTableSql = $@"
                CREATE TABLE IF NOT EXISTS {tankerTableName} (
                    `Id` BIGINT PRIMARY KEY,
                    `Description` TEXT NULL,
                    `Created` DATETIME NOT NULL
                ) ENGINE=InnoDB CHARSET=utf8;";
            ExecuteCreateTableMySql(tankerTableName, createTankerTableSql);

            string jarDetailTableName = GetDynamicTableName(BaseJarDetailTableName, kind);
            string createJarDetailTableSql = $@"
                CREATE TABLE IF NOT EXISTS {jarDetailTableName} (
                    `JarVersion` BIGINT NOT NULL,
                    `DepositId` BIGINT NOT NULL, 
                    `Created` DATETIME NOT NULL
                ) ENGINE=InnoDB CHARSET=utf8;";
            ExecuteCreateTableMySql(jarDetailTableName, createJarDetailTableSql);

            string tankDetailTableName = GetDynamicTableName(BaseTankDetailTableName, kind);
            string createTankDetailTableSql = $@"
                CREATE TABLE IF NOT EXISTS {tankDetailTableName} (
                    `TankId` BIGINT NOT NULL,
                    `DepositId` BIGINT NOT NULL,
                    `Created` DATETIME NOT NULL
                ) ENGINE=InnoDB CHARSET=utf8;";
            ExecuteCreateTableMySql(tankDetailTableName, createTankDetailTableSql);

            string tankerDetailTableName = GetDynamicTableName(BaseTankerDetailTableName, kind);
            string createTankerDetailTableSql = $@"
                CREATE TABLE IF NOT EXISTS {tankerDetailTableName} (
                    `TankerId` BIGINT NOT NULL,
                    `DepositId` BIGINT NOT NULL,
                    `Created` DATETIME NOT NULL
                ) ENGINE=InnoDB CHARSET=utf8;";
            ExecuteCreateTableMySql(tankerDetailTableName, createTankerDetailTableSql);

            string withdrawalTableName = GetDynamicTableName(BaseWithdrawalTableName, kind);
            string createWithdrawalTableSql = $@"
                CREATE TABLE IF NOT EXISTS {withdrawalTableName} (
                    `Id` BIGINT PRIMARY KEY,
                    `DocumentNumber` VARCHAR(45) NOT NULL,
                    `Amount` DECIMAL(16, 8) NOT NULL,
                    `Date` DATETIME NOT NULL,
                    `Store` TINYINT UNSIGNED NOT NULL,
                    `AccountNumber` VARCHAR(60) NOT NULL,
                    `DomainId` INT NOT NULL,
                    `Address` VARCHAR(255) NOT NULL,
                    `Created` DATETIME NOT NULL
                ) ENGINE=InnoDB CHARSET=utf8;";
            ExecuteCreateTableMySql(withdrawalTableName, createWithdrawalTableSql);

            string bottleTableName = GetDynamicTableName(BaseBottleTableName, kind);
            string createBottleTableSql = $@"
                CREATE TABLE IF NOT EXISTS {bottleTableName} (
                    `Id` BIGINT PRIMARY KEY,
                    `Description` TEXT NULL,
                    `Created` DATETIME NOT NULL
                ) ENGINE=InnoDB CHARSET=utf8;";
            ExecuteCreateTableMySql(bottleTableName, createBottleTableSql);

            string dispenserTableName = GetDynamicTableName(BaseDispenserTableName, kind);
            string createDispenserTableSql = $@"
                CREATE TABLE IF NOT EXISTS {dispenserTableName} (
                    `Id` BIGINT PRIMARY KEY,
                    `Description` TEXT NULL,
                    `Created` DATETIME NOT NULL
                ) ENGINE=InnoDB CHARSET=utf8;";
            ExecuteCreateTableMySql(dispenserTableName, createDispenserTableSql);

            string bottleDetailTableName = GetDynamicTableName(BaseBottleDetailTableName, kind);
            string createBottleDetailTableSql = $@"
                CREATE TABLE IF NOT EXISTS {bottleDetailTableName} (
                    `WithdrawalId` BIGINT NOT NULL,
                    `BottleId` BIGINT NOT NULL,
                    `Created` DATETIME NOT NULL
                ) ENGINE=InnoDB CHARSET=utf8;";
            ExecuteCreateTableMySql(bottleDetailTableName, createBottleDetailTableSql);

            string dispenserDetailTableName = GetDynamicTableName(BaseDispenserDetailTableName, kind);
            string createDispenserDetailTableSql = $@"
                CREATE TABLE IF NOT EXISTS {dispenserDetailTableName} (
                    `WithdrawalId` BIGINT NOT NULL,
                    `DispenserId` BIGINT NOT NULL,
                    `Created` DATETIME NOT NULL
                ) ENGINE=InnoDB CHARSET=utf8;";
            ExecuteCreateTableMySql(dispenserDetailTableName, createDispenserDetailTableSql);
        }

        public void DropAllTables(string kind)
        {
            var baseTableNames = new List<string>
            {
                BaseDepositTableName,
                BaseJarTableName,
                BaseTankTableName,
                BaseTankerTableName,
                BaseJarDetailTableName,
                BaseTankDetailTableName,
                BaseTankerDetailTableName,
                BaseWithdrawalTableName,
                BaseBottleTableName,
                BaseDispenserTableName,
                BaseBottleDetailTableName,
                BaseDispenserDetailTableName
            };

            foreach (var baseName in baseTableNames)
            {
                string tableName = GetDynamicTableName(baseName, kind);
                // MySQL uses backticks for identifiers, which are already in GetDynamicTableName
                string dropSql = $"DROP TABLE IF EXISTS {tableName};";
                try
                {
                    using (var connection = new MySqlConnection(_connectionString))
                    {
                        connection.Open();
                        using (var command = connection.CreateCommand())
                        {
                            command.CommandText = dropSql;
                            command.ExecuteNonQuery();
                        }
                    }
                }
                catch (MySqlException ex)
                {
                    Loggers.GetIntance().Db.Error($"MySQL Error executing non-query: {dropSql}", ex);
                    ErrorsSender.Send(ex, dropSql);
                    throw;
                }
            }
        }

        public void CreateDeposit(string kind, Deposit deposit)
        {
            string depositTableName = GetDynamicTableName(BaseDepositTableName, kind);
            string insertSql = $@"
                INSERT INTO {depositTableName}
                (`Id`, `DocumentNumber`, `Amount`, `Date`, `Store`, `AccountNumber`, `DomainId`, `Address`, `Created`)
                VALUES
                (@Id, @DocumentNumber, @Amount, @Date, @Store, @AccountNumber, @DomainId, @Address, @Created);";

            try
            {
                using (var connection = new MySqlConnection(_connectionString))
                {
                    connection.Open();
                    using (var command = new MySqlCommand(insertSql, connection))
                    {
                        command.Parameters.AddWithValue("@Id", deposit.Id);
                        command.Parameters.AddWithValue("@DocumentNumber", deposit.DocumentNumber);
                        command.Parameters.AddWithValue("@Amount", deposit.Amount);
                        command.Parameters.AddWithValue("@Date", deposit.Date);
                        command.Parameters.AddWithValue("@Store", deposit.StoreId);
                        command.Parameters.AddWithValue("@AccountNumber", deposit.AccountNumber);
                        command.Parameters.AddWithValue("@DomainId", deposit.DomainId);
                        command.Parameters.AddWithValue("@Address", deposit.Address);
                        command.Parameters.AddWithValue("@Created", deposit.Created);
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (MySqlException ex)
            {
                Loggers.GetIntance().Db.Error($"MySQL Error inserting transactions: {insertSql}", ex);
                ErrorsSender.Send(ex, insertSql);
                throw;
            }
        }

        public void CreateJar(string kind, long version, string description, DateTime created, long? previousJar)
        {
            string jarTableName = GetDynamicTableName(BaseJarTableName, kind);
            string insertSql = $"INSERT INTO {jarTableName} (`Version`, `Description`, `Created`) VALUES (@Version, @Description, @Created);";

            try
            {
                using (var connection = new MySqlConnection(_connectionString))
                {
                    connection.Open();

                    using (var insertCmd = new MySqlCommand(insertSql, connection))
                    {
                        insertCmd.Parameters.AddWithValue("@Version", version);
                        insertCmd.Parameters.AddWithValue("@Description", description);
                        insertCmd.Parameters.AddWithValue("@Created", created);
                        insertCmd.ExecuteNonQuery();
                    }
                }
            }
            catch (MySqlException ex)
            {
                Loggers.GetIntance().Db.Error($"MySQL Error in {nameof(CreateJar)} (Desc: {description}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(CreateJar)} (Desc: {description})");
                throw;
            }
        }

        public void CreateTank(string kind, Tank tank)
        {
            string tankTableName = GetDynamicTableName(BaseTankTableName, kind);
            string insertSql = $"INSERT INTO {tankTableName} (`Id`, `Description`, `Created`) VALUES (@Id, @Description, @Created);";
            try
            {
                using (var connection = new MySqlConnection(_connectionString))
                {
                    connection.Open();

                    using (var insertCmd = new MySqlCommand(insertSql, connection))
                    {
                        insertCmd.Parameters.AddWithValue("@Id", tank.Id);
                        insertCmd.Parameters.AddWithValue("@Description", tank.Description);
                        insertCmd.Parameters.AddWithValue("@Created", tank.Created);
                        insertCmd.ExecuteNonQuery();
                    }
                }
            }
            catch (MySqlException ex)
            {
                Loggers.GetIntance().Db.Error($"MySQL Error in {nameof(CreateTank)} (Desc: {tank.Description}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(CreateTank)} (Desc: {tank.Description})");
                throw;
            }
        }

        public void CreateTanker(string kind, Tanker tanker)
        {
            string tankerTableName = GetDynamicTableName(BaseTankerTableName, kind);
            string insertSql = $"INSERT INTO {tankerTableName} (`Id`, `Description`, `Created`) VALUES (@Id, @Description, @Created);";
            try
            {
                using (var connection = new MySqlConnection(_connectionString))
                {
                    connection.Open();

                    using (var insertCmd = new MySqlCommand(insertSql, connection))
                    {
                        insertCmd.Parameters.AddWithValue("@Id", tanker.Id);
                        insertCmd.Parameters.AddWithValue("@Description", tanker.Description);
                        insertCmd.Parameters.AddWithValue("@Created", tanker.Created);
                        insertCmd.ExecuteNonQuery();
                    }
                }
            }
            catch (MySqlException ex)
            {
                Loggers.GetIntance().Db.Error($"MySQL Error in {nameof(CreateTanker)} (Desc: {tanker.Description}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(CreateTanker)} (Desc: {tanker.Description})");
                throw;
            }
        }

        public void CreateJarDetailIfNotExists(string kind, long jarVersion, long depositId, DateTime created)
        {
            string jarDetailTableName = GetDynamicTableName(BaseJarDetailTableName, kind);
            string selectSql = $"SELECT COUNT(1) FROM {jarDetailTableName} WHERE `JarVersion` = @JarVersion AND `DepositId` = @DepositId;";
            string insertSql = $"INSERT INTO {jarDetailTableName} (`JarVersion`, `DepositId`, `Created`) VALUES (@JarVersion, @DepositId, @Created);";
            try
            {
                using (var connection = new MySqlConnection(_connectionString))
                {
                    connection.Open();
                    bool exists;
                    using (var selectCmd = new MySqlCommand(selectSql, connection))
                    {
                        selectCmd.Parameters.AddWithValue("@JarVersion", jarVersion);
                        selectCmd.Parameters.AddWithValue("@DepositId", depositId);
                        exists = Convert.ToInt64(selectCmd.ExecuteScalar()) > 0;
                    }

                    if (!exists)
                    {
                        using (var insertCmd = new MySqlCommand(insertSql, connection))
                        {
                            insertCmd.Parameters.AddWithValue("@JarVersion", jarVersion);
                            insertCmd.Parameters.AddWithValue("@DepositId", depositId);
                            insertCmd.Parameters.AddWithValue("@Created", created);
                            insertCmd.ExecuteNonQuery();
                        }
                    }
                }
            }
            catch (MySqlException ex)
            {
                Loggers.GetIntance().Db.Error($"MySQL Error in CreateJarDetailIfNotExists (JarVersion: {jarVersion}, DepositId: {depositId}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(CreateJarDetailIfNotExists)} (JarVersion: {jarVersion}, DepositId: {depositId})");
                throw;
            }
        }

        public void CreateTankDetailIfNotExists(string kind, long tankId, long depositId, DateTime created)
        {
            string tankDetailTableName = GetDynamicTableName(BaseTankDetailTableName, kind);
            string selectSql = $"SELECT COUNT(1) FROM {tankDetailTableName} WHERE `TankId` = @TankId AND `DepositId` = @DepositId;";
            string insertSql = $"INSERT INTO {tankDetailTableName} (`TankId`, `DepositId`, `Created`) VALUES (@TankId, @DepositId, @Created);";
            try
            {
                using (var connection = new MySqlConnection(_connectionString))
                {
                    connection.Open();
                    bool exists;
                    using (var selectCmd = new MySqlCommand(selectSql, connection))
                    {
                        selectCmd.Parameters.AddWithValue("@TankId", tankId);
                        selectCmd.Parameters.AddWithValue("@DepositId", depositId);
                        exists = Convert.ToInt64(selectCmd.ExecuteScalar()) > 0;
                    }

                    if (!exists)
                    {
                        using (var insertCmd = new MySqlCommand(insertSql, connection))
                        {
                            insertCmd.Parameters.AddWithValue("@TankId", tankId);
                            insertCmd.Parameters.AddWithValue("@DepositId", depositId);
                            insertCmd.Parameters.AddWithValue("@Created", created);
                            insertCmd.ExecuteNonQuery();
                        }
                    }
                }
            }
            catch (MySqlException ex)
            {
                Loggers.GetIntance().Db.Error($"MySQL Error in CreateTankDetailIfNotExists (TankId: {tankId}, DepositId: {depositId}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(CreateTankDetailIfNotExists)} (TankId: {tankId}, DepositId: {depositId})");
                throw;
            }
        }

        public void CreateTankDetails(string kind, long tankId, IEnumerable<int> uniqueDepositIds, DateTime created)
        {
            if (uniqueDepositIds == null || !uniqueDepositIds.Any())
            {
                return;
            }

            string tankDetailTableName = GetDynamicTableName(BaseTankDetailTableName, kind);
            int batchSize = 500;
            for (int i = 0; i < uniqueDepositIds.Count(); i += batchSize)
            {
                var batch = uniqueDepositIds.Skip(i).Take(batchSize).ToList();
                if (!batch.Any()) continue;

                var sbInsert = new StringBuilder();
                sbInsert.Append($"INSERT IGNORE INTO {tankDetailTableName} (`TankId`, `DepositId`, `Created`) VALUES ");

                var insertParams = new List<MySqlParameter>();
                var valueClauses = new List<string>();

                for (int j = 0; j < batch.Count; j++)
                {
                    string tankIdParam = $"@TankId_b{i}_r{j}";
                    string depositIdParam = $"@DepositId_b{i}_r{j}";
                    string createdParam = $"@Created_b{i}_r{j}";

                    valueClauses.Add($"({tankIdParam}, {depositIdParam}, {createdParam})");
                    insertParams.Add(new MySqlParameter(tankIdParam, tankId));
                    insertParams.Add(new MySqlParameter(depositIdParam, batch[j]));
                    insertParams.Add(new MySqlParameter(createdParam, created));
                }
                sbInsert.Append(string.Join(",", valueClauses));
                sbInsert.Append(";");

                try
                {
                    using (var connection = new MySqlConnection(_connectionString))
                    {
                        connection.Open();
                        using (var command = new MySqlCommand(sbInsert.ToString(), connection))
                        {
                            command.Parameters.AddRange(insertParams.ToArray());
                            command.ExecuteNonQuery();
                        }
                    }
                }
                catch (MySqlException ex)
                {
                    Loggers.GetIntance().Db.Error($"MySQL Error in batch inserting TankDetails (TankId: {tankId}): {ex.Message}. Query: {sbInsert.ToString()}", ex);
                    ErrorsSender.Send(ex, $"{nameof(CreateTankDetails)} batch insert (TankId: {tankId})");
                    throw;
                }
            }
        }

        public void CreateTankerDetailIfNotExists(string kind, long tankerId, long depositId, DateTime created)
        {
            string tankerDetailTableName = GetDynamicTableName(BaseTankerDetailTableName, kind);
            string selectSql = $"SELECT COUNT(1) FROM {tankerDetailTableName} WHERE `TankerId` = @TankerId AND `DepositId` = @DepositId;";
            string insertSql = $"INSERT INTO {tankerDetailTableName} (`TankerId`, `DepositId`, `Created`) VALUES (@TankerId, @DepositId, @Created);";
            try
            {
                using (var connection = new MySqlConnection(_connectionString))
                {
                    connection.Open();
                    bool exists;
                    using (var selectCmd = new MySqlCommand(selectSql, connection))
                    {
                        selectCmd.Parameters.AddWithValue("@TankerId", tankerId);
                        selectCmd.Parameters.AddWithValue("@DepositId", depositId);
                        exists = Convert.ToInt64(selectCmd.ExecuteScalar()) > 0;
                    }

                    if (!exists)
                    {
                        using (var insertCmd = new MySqlCommand(insertSql, connection))
                        {
                            insertCmd.Parameters.AddWithValue("@TankerId", tankerId);
                            insertCmd.Parameters.AddWithValue("@DepositId", depositId);
                            insertCmd.Parameters.AddWithValue("@Created", created);
                            insertCmd.ExecuteNonQuery();
                        }
                    }
                }
            }
            catch (MySqlException ex)
            {
                Loggers.GetIntance().Db.Error($"MySQL Error in {nameof(CreateTankerDetailIfNotExists)} (TankerId: {tankerId}, DepositId: {depositId}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(CreateTankerDetailIfNotExists)} (TankerId: {tankerId}, DepositId: {depositId})");
                throw;
            }
        }

        public List<Deposit> DepositsInNewestJar(string kind, DateTime startDate, DateTime endDate, string accountNumber = null)
        {
            var deposits = new List<Deposit>();
            long? newestJarVersionByVersion = null;

            string jarTableName = GetDynamicTableName(BaseJarTableName, kind);
            string depositTableName = GetDynamicTableName(BaseDepositTableName, kind);
            string jarDetailTableName = GetDynamicTableName(BaseJarDetailTableName, kind);

            string getNewestJarVersionSql = $"SELECT `Version` FROM {jarTableName} ORDER BY `Version` DESC, `Created` DESC LIMIT 1;";

            try
            {
                using (var connection = new MySqlConnection(_connectionString))
                {
                    connection.Open();
                    using (var jarCmd = new MySqlCommand(getNewestJarVersionSql, connection))
                    {
                        var result = jarCmd.ExecuteScalar();
                        if (result != null && result != DBNull.Value)
                        {
                            newestJarVersionByVersion = Convert.ToInt64(result);
                        }
                    }

                    if (!newestJarVersionByVersion.HasValue)
                    {
                        Loggers.GetIntance().Db.Debug("No Jars found, cannot retrieve deposits for the newest jar by version.");
                        return deposits;
                    }

                    var selectDepositsSqlBuilder = new StringBuilder($@"
                        SELECT
                            d.Id, d.DocumentNumber, d.Amount, d.Date, d.Store, d.AccountNumber, d.DomainId, d.Address, d.Created
                        FROM
                            {depositTableName} d
                        INNER JOIN
                            {jarDetailTableName} jd ON d.Id = jd.DepositId
                        WHERE
                            jd.JarVersion = @NewestJarVersion
                            AND d.Date >= @StartDate AND d.Date <= @EndDate");

                    if (!string.IsNullOrEmpty(accountNumber))
                    {
                        selectDepositsSqlBuilder.Append(" AND d.AccountNumber = @AccountNumber");
                    }
                    selectDepositsSqlBuilder.Append(";");

                    string selectDepositsSql = selectDepositsSqlBuilder.ToString();

                    using (var depositCmd = new MySqlCommand(selectDepositsSql, connection))
                    {
                        depositCmd.Parameters.AddWithValue("@NewestJarVersion", newestJarVersionByVersion.Value);
                        depositCmd.Parameters.AddWithValue("@StartDate", ToFullDateTimeString(startDate));
                        depositCmd.Parameters.AddWithValue("@EndDate", ToFullDateTimeString(endDate));
                        if (!string.IsNullOrEmpty(accountNumber))
                        {
                            depositCmd.Parameters.AddWithValue("@AccountNumber", accountNumber);
                        }

                        using (var reader = depositCmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var deposit = new Deposit
                                {
                                    Id = reader.GetInt64(reader.GetOrdinal("Id")),
                                    DocumentNumber = reader.GetString(reader.GetOrdinal("DocumentNumber")),
                                    Amount = reader.GetDecimal(reader.GetOrdinal("Amount")),
                                    Date = reader.GetDateTime(reader.GetOrdinal("Date")),
                                    StoreId = reader.GetByte(reader.GetOrdinal("Store")),
                                    AccountNumber = reader.GetString(reader.GetOrdinal("AccountNumber")),
                                    DomainId = reader.GetInt32(reader.GetOrdinal("DomainId")),
                                    Address = reader.GetString(reader.GetOrdinal("Address")),
                                    Created = reader.GetDateTime(reader.GetOrdinal("Created"))
                                };
                                deposits.Add(deposit);
                            }
                        }
                    }
                }
            }
            catch (MySqlException ex)
            {
                string accNumContext = string.IsNullOrEmpty(accountNumber) ? "null" : accountNumber;
                string errorContext = newestJarVersionByVersion.HasValue ? $"newestJarVersionByVersion: {newestJarVersionByVersion.Value}" : "newestJarVersionByVersion: null";
                Loggers.GetIntance().Db.Error($"MySQL Error in {nameof(DepositsInNewestJar)} ({errorContext}, startDate: {startDate}, endDate: {endDate}, accountNumber: {accNumContext}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(DepositsInNewestJar)}", $"newestJarVersionByVersion {newestJarVersionByVersion}, startDate {startDate}, endDate {endDate}, accountNumber {accNumContext}");
                throw;
            }
            return deposits;
        }

        public TankWithDeposits TankAndAllItsDeposits(string kind, long tankId)
        {
            var result = new TankWithDeposits();
            bool tankInfoSet = false;

            string tankTableName = GetDynamicTableName(BaseTankTableName, kind);
            string tankDetailTableName = GetDynamicTableName(BaseTankDetailTableName, kind);
            string depositTableName = GetDynamicTableName(BaseDepositTableName, kind);

            string selectSql = $@"
                SELECT
                    t.Id AS TankId, t.Description AS TankDescription, t.Created AS TankCreated,
                    d.Id AS DepositId, d.DocumentNumber, d.Amount, d.Date, d.Store, 
                    d.AccountNumber, d.DomainId, d.Address, d.Created AS DepositCreated
                FROM
                    {tankTableName} t
                LEFT JOIN
                    {tankDetailTableName} td ON t.Id = td.TankId
                LEFT JOIN
                    {depositTableName} d ON td.DepositId = d.Id
                WHERE
                    t.Id = @TankId;";

            try
            {
                using (var connection = new MySqlConnection(_connectionString))
                {
                    connection.Open();
                    using (var command = new MySqlCommand(selectSql, connection))
                    {
                        command.Parameters.AddWithValue("@TankId", tankId);

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                if (!tankInfoSet)
                                {
                                    result.TankInfo = new Tank
                                    {
                                        Id = reader.GetInt64(reader.GetOrdinal("TankId")),
                                        Description = reader.IsDBNull(reader.GetOrdinal("TankDescription")) ? null : reader.GetString(reader.GetOrdinal("TankDescription")),
                                        Created = reader.GetDateTime(reader.GetOrdinal("TankCreated"))
                                    };
                                    tankInfoSet = true;
                                }

                                if (!reader.IsDBNull(reader.GetOrdinal("DepositId")))
                                {
                                    result.Deposits.Add(new Deposit
                                    {
                                        Id = reader.GetInt64(reader.GetOrdinal("DepositId")),
                                        DocumentNumber = reader.GetString(reader.GetOrdinal("DocumentNumber")),
                                        Amount = reader.GetDecimal(reader.GetOrdinal("Amount")),
                                        Date = reader.GetDateTime(reader.GetOrdinal("Date")),
                                        StoreId = reader.GetByte(reader.GetOrdinal("Store")),
                                        AccountNumber = reader.GetString(reader.GetOrdinal("AccountNumber")),
                                        DomainId = reader.GetInt32(reader.GetOrdinal("DomainId")),
                                        Address = reader.GetString(reader.GetOrdinal("Address")),
                                        Created = reader.GetDateTime(reader.GetOrdinal("DepositCreated"))
                                    });
                                }
                            }
                        }
                    }
                }

                if (result.TankInfo == null)
                {
                    return null;
                }
                return result;
            }
            catch (MySqlException ex)
            {
                Loggers.GetIntance().Db.Error($"MySQL Error in {nameof(TankAndAllItsDeposits)} (TankId: {tankId}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(TankAndAllItsDeposits)}", $"TankId {tankId}");
                throw;
            }
        }

        public TankerWithDeposits TankerAndAllItsDeposits(string kind, long tankerId)
        {
            var result = new TankerWithDeposits();
            bool tankerInfoSet = false;

            string tankerTableName = GetDynamicTableName(BaseTankerTableName, kind);
            string tankerDetailTableName = GetDynamicTableName(BaseTankerDetailTableName, kind);
            string depositTableName = GetDynamicTableName(BaseDepositTableName, kind);

            string selectSql = $@"
                SELECT
                    t.Id AS TankerId, t.Name AS TankerName, t.Description AS TankerDescription, t.Created AS TankerCreated,
                    d.Id AS DepositId, d.DocumentNumber, d.Amount, d.Date, d.Store,
                    d.AccountNumber, d.DomainId, d.Address, d.Created AS DepositCreated
                FROM
                    {tankerTableName} t
                LEFT JOIN
                    {tankerDetailTableName} td ON t.Id = td.TankerId
                LEFT JOIN
                    {depositTableName} d ON td.DepositId = d.Id
                WHERE
                    t.Id = @TankerId;";

            try
            {
                using (var connection = new MySqlConnection(_connectionString))
                {
                    connection.Open();
                    using (var command = new MySqlCommand(selectSql, connection))
                    {
                        command.Parameters.AddWithValue("@TankerId", tankerId);

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                if (!tankerInfoSet)
                                {
                                    result.TankerInfo = new Tanker
                                    {
                                        Id = reader.GetInt64(reader.GetOrdinal("TankerId")),
                                        Name = reader.IsDBNull(reader.GetOrdinal("TankerName")) ? null : reader.GetString(reader.GetOrdinal("TankerName")),
                                        Description = reader.IsDBNull(reader.GetOrdinal("TankerDescription")) ? null : reader.GetString(reader.GetOrdinal("TankerDescription")),
                                        Created = reader.GetDateTime(reader.GetOrdinal("TankerCreated"))
                                    };
                                    tankerInfoSet = true;
                                }

                                if (!reader.IsDBNull(reader.GetOrdinal("DepositId")))
                                {
                                    result.Deposits.Add(new Deposit
                                    {
                                        Id = reader.GetInt64(reader.GetOrdinal("DepositId")),
                                        DocumentNumber = reader.GetString(reader.GetOrdinal("DocumentNumber")),
                                        Amount = reader.GetDecimal(reader.GetOrdinal("Amount")),
                                        Date = reader.GetDateTime(reader.GetOrdinal("Date")),
                                        StoreId = reader.GetByte(reader.GetOrdinal("Store")),
                                        AccountNumber = reader.GetString(reader.GetOrdinal("AccountNumber")),
                                        DomainId = reader.GetInt32(reader.GetOrdinal("DomainId")),
                                        Address = reader.GetString(reader.GetOrdinal("Address")),
                                        Created = reader.GetDateTime(reader.GetOrdinal("DepositCreated"))
                                    });
                                }
                            }
                        }
                    }
                }

                if (result.TankerInfo == null)
                {
                    return null;
                }
                return result;
            }
            catch (MySqlException ex)
            {
                Loggers.GetIntance().Db.Error($"MySQL Error in {nameof(TankerAndAllItsDeposits)} (TankerId: {tankerId}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(TankerAndAllItsDeposits)}", $"TankerId {tankerId}");
                throw;
            }
        }

        public DispenserWithWithdrawals DispenserAndAllItsWithdrawals(string kind, long dispenserId)
        {
            var result = new DispenserWithWithdrawals();
            bool dispenserInfoSet = false;

            string dispenserTableName = GetDynamicTableName(BaseDispenserTableName, kind);
            string dispenserDetailTableName = GetDynamicTableName(BaseDispenserDetailTableName, kind);
            string withdrawalTableName = GetDynamicTableName(BaseWithdrawalTableName, kind);

            string selectSql = $@"
                SELECT
                    d.Id AS DispenserId, d.Description AS DispenserDescription, d.Created AS DispenserCreated,
                    w.Id AS WithdrawalId, w.DocumentNumber, w.Amount, w.Date, w.Store,
                    w.AccountNumber, w.DomainId, w.Address, w.Created AS WithdrawalCreated
                FROM
                    {dispenserTableName} d
                LEFT JOIN
                    {dispenserDetailTableName} dd ON d.Id = dd.DispenserId
                LEFT JOIN
                    {withdrawalTableName} w ON dd.WithdrawalId = w.Id
                WHERE
                    d.Id = @DispenserId;";

            try
            {
                using (var connection = new MySqlConnection(_connectionString))
                {
                    connection.Open();
                    using (var command = new MySqlCommand(selectSql, connection))
                    {
                        command.Parameters.AddWithValue("@DispenserId", dispenserId);

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                if (!dispenserInfoSet)
                                {
                                    result.DispenserInfo = new Dispenser
                                    {
                                        Id = reader.GetInt64(reader.GetOrdinal("DispenserId")),
                                        Description = reader.IsDBNull(reader.GetOrdinal("DispenserDescription")) ? null : reader.GetString(reader.GetOrdinal("DispenserDescription")),
                                        Created = reader.GetDateTime(reader.GetOrdinal("DispenserCreated"))
                                    };
                                    dispenserInfoSet = true;
                                }

                                if (!reader.IsDBNull(reader.GetOrdinal("WithdrawalId")))
                                {
                                    result.Withdrawals.Add(new Withdrawal
                                    {
                                        Id = reader.GetInt64(reader.GetOrdinal("WithdrawalId")),
                                        DocumentNumber = reader.GetString(reader.GetOrdinal("DocumentNumber")),
                                        Amount = reader.GetDecimal(reader.GetOrdinal("Amount")),
                                        Date = reader.GetDateTime(reader.GetOrdinal("Date")),
                                        StoreId = reader.GetByte(reader.GetOrdinal("Store")),
                                        AccountNumber = reader.GetString(reader.GetOrdinal("AccountNumber")),
                                        DomainId = reader.GetInt32(reader.GetOrdinal("DomainId")),
                                        Address = reader.GetString(reader.GetOrdinal("Address")),
                                        Created = reader.GetDateTime(reader.GetOrdinal("WithdrawalCreated"))
                                    });
                                }
                            }
                        }
                    }
                }

                if (result.DispenserInfo == null)
                {
                    return null;
                }
                return result;
            }
            catch (MySqlException ex)
            {
                Loggers.GetIntance().Db.Error($"MySQL Error in DispenserAndAllItsWithdrawals (DispenserId: {dispenserId}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(DispenserAndAllItsWithdrawals)}", $"DispenserId {dispenserId}");
                throw;
            }
        }

        public void CreateWithdrawal(string kind, Withdrawal w)
        {
            string withdrawalTableName = GetDynamicTableName(BaseWithdrawalTableName, kind);
            string insertSql = $@"
                INSERT INTO {withdrawalTableName}
                (`Id`, `DocumentNumber`, `Amount`, `Date`, `Store`, `AccountNumber`, `DomainId`, `Address`, `Created`)
                VALUES
                (@Id, @DocumentNumber, @Amount, @Date, @Store, @AccountNumber, @DomainId, @Address, @Created);";
            try
            {
                using (var connection = new MySqlConnection(_connectionString))
                {
                    connection.Open();

                    using (var insertCmd = new MySqlCommand(insertSql, connection))
                    {
                        insertCmd.Parameters.AddWithValue("@Id", w.Id);
                        insertCmd.Parameters.AddWithValue("@DocumentNumber", w.DocumentNumber);
                        insertCmd.Parameters.AddWithValue("@Amount", w.Amount);
                        insertCmd.Parameters.AddWithValue("@Date", w.Date);
                        insertCmd.Parameters.AddWithValue("@Store", w.StoreId);
                        insertCmd.Parameters.AddWithValue("@AccountNumber", w.AccountNumber);
                        insertCmd.Parameters.AddWithValue("@DomainId", w.DomainId);
                        insertCmd.Parameters.AddWithValue("@Address", w.Address);
                        insertCmd.Parameters.AddWithValue("@Created", w.Created);
                        insertCmd.ExecuteNonQuery();
                    }
                }
            }
            catch (MySqlException ex)
            {
                Loggers.GetIntance().Db.Error($"MySQL Error in GetOrCreateWithdrawal: {ex.Message}", ex);
                ErrorsSender.Send(ex, "GetOrCreateWithdrawal");
                throw;
            }
        }

        public void CreateBottle(string kind, Bottle b)
        {
            string bottleTableName = GetDynamicTableName(BaseBottleTableName, kind);
            string insertSql = $@"
                INSERT INTO {bottleTableName}
                (`Id`, `Description`, `Created`)
                VALUES
                (@Id, @Description, @Created);";
            try
            {
                using (var connection = new MySqlConnection(_connectionString))
                {
                    connection.Open();

                    using (var insertCmd = new MySqlCommand(insertSql, connection))
                    {
                        insertCmd.Parameters.AddWithValue("@Id", b.Id);
                        insertCmd.Parameters.AddWithValue("@Description", b.Description ?? (object)DBNull.Value);
                        insertCmd.Parameters.AddWithValue("@Created", b.Created);
                        insertCmd.ExecuteNonQuery();
                    }
                }
            }
            catch (MySqlException ex)
            {
                Loggers.GetIntance().Db.Error($"MySQL Error in GetOrCreateBottle: {ex.Message}", ex);
                ErrorsSender.Send(ex, "GetOrCreateBottle");
                throw;
            }
        }

        public void CreateDispenser(string kind, Dispenser d)
        {
            string dispenserTableName = GetDynamicTableName(BaseDispenserTableName, kind);
            string insertSql = $@"
                INSERT INTO {dispenserTableName}
                (`Id`, `Description`, `Created`)
                VALUES
                (@Id, @Description, @Created);";
            try
            {
                using (var connection = new MySqlConnection(_connectionString))
                {
                    connection.Open();

                    using (var insertCmd = new MySqlCommand(insertSql, connection))
                    {
                        insertCmd.Parameters.AddWithValue("@Id", d.Id);
                        insertCmd.Parameters.AddWithValue("@Description", d.Description ?? (object)DBNull.Value);
                        insertCmd.Parameters.AddWithValue("@Created", d.Created);
                        insertCmd.ExecuteNonQuery();
                    }
                }
            }
            catch (MySqlException ex)
            {
                Loggers.GetIntance().Db.Error($"MySQL Error in GetOrCreateDispenser: {ex.Message}", ex);
                ErrorsSender.Send(ex, "GetOrCreateDispenser");
                throw;
            }
        }

        public void CreateBottleDetailIfNotExists(string kind, long withdrawalId, long bottleId, DateTime created)
        {
            string bottleDetailTableName = GetDynamicTableName(BaseBottleDetailTableName, kind);
            string selectSql = $"SELECT COUNT(1) FROM {bottleDetailTableName} WHERE `WithdrawalId` = @WithdrawalId AND `BottleId` = @BottleId;";
            string insertSql = $"INSERT INTO {bottleDetailTableName} (`WithdrawalId`, `BottleId`, `Created`) VALUES (@WithdrawalId, @BottleId, @Created);";
            try
            {
                using (var connection = new MySqlConnection(_connectionString))
                {
                    connection.Open();
                    bool exists;
                    using (var selectCmd = new MySqlCommand(selectSql, connection))
                    {
                        selectCmd.Parameters.AddWithValue("@WithdrawalId", withdrawalId);
                        selectCmd.Parameters.AddWithValue("@BottleId", bottleId);
                        exists = Convert.ToInt64(selectCmd.ExecuteScalar()) > 0;
                    }
                    if (!exists)
                    {
                        using (var insertCmd = new MySqlCommand(insertSql, connection))
                        {
                            insertCmd.Parameters.AddWithValue("@WithdrawalId", withdrawalId);
                            insertCmd.Parameters.AddWithValue("@BottleId", bottleId);
                            insertCmd.Parameters.AddWithValue("@Created", created);
                            insertCmd.ExecuteNonQuery();
                        }
                    }
                }
            }
            catch (MySqlException ex)
            {
                Loggers.GetIntance().Db.Error($"MySQL Error in CreateBottleDetailIfNotExists: {ex.Message}", ex);
                ErrorsSender.Send(ex, "CreateBottleDetailIfNotExists");
                throw;
            }
        }

        public void CreateDispenserDetailIfNotExists(string kind, long withdrawalId, long dispenserId, DateTime created)
        {
            string dispenserDetailTableName = GetDynamicTableName(BaseDispenserDetailTableName, kind);
            string selectSql = $"SELECT COUNT(1) FROM {dispenserDetailTableName} WHERE `WithdrawalId` = @WithdrawalId AND `DispenserId` = @DispenserId;";
            string insertSql = $"INSERT INTO {dispenserDetailTableName} (`WithdrawalId`, `DispenserId`, `Created`) VALUES (@WithdrawalId, @DispenserId, @Created);";
            try
            {
                using (var connection = new MySqlConnection(_connectionString))
                {
                    connection.Open();
                    bool exists;
                    using (var selectCmd = new MySqlCommand(selectSql, connection))
                    {
                        selectCmd.Parameters.AddWithValue("@WithdrawalId", withdrawalId);
                        selectCmd.Parameters.AddWithValue("@DispenserId", dispenserId);
                        exists = Convert.ToInt64(selectCmd.ExecuteScalar()) > 0;
                    }
                    if (!exists)
                    {
                        using (var insertCmd = new MySqlCommand(insertSql, connection))
                        {
                            insertCmd.Parameters.AddWithValue("@WithdrawalId", withdrawalId);
                            insertCmd.Parameters.AddWithValue("@DispenserId", dispenserId);
                            insertCmd.Parameters.AddWithValue("@Created", created);
                            insertCmd.ExecuteNonQuery();
                        }
                    }
                }
            }
            catch (MySqlException ex)
            {
                Loggers.GetIntance().Db.Error($"MySQL Error in CreateDispenserDetailIfNotExists: {ex.Message}", ex);
                ErrorsSender.Send(ex, "CreateDispenserDetailIfNotExists");
                throw;
            }
        }

        private string ToFullDateTimeString(DateTime day)
        {
            return day.ToString("yyyy-MM-dd HH:mm:ss.fff");
        }

        void IStorage.CreateInvoicePayment(string kind, InvoicePayment paymentDetails)
        {
            throw new NotImplementedException();
        }
    }

}
