﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using static GamesEngine.Business.Liquidity.Persistence.DataModel;
using System.Text;
using System.Data.Common;
using GamesEngine.Business.Liquidity.Persistence;

namespace GamesEngineTests.Unit_Tests.Business
{
    public abstract class OlapStorageTestBase
    {
        protected Mock<IDbConnectionFactory> mockConnectionFactory;
        protected Mock<IAppConnection> mockAppConnection;
        protected Mock<IAppCommand> mockAppCommand;
        protected Mock<DbDataReader> mockDataReader;
        protected Mock<IAppBulkCopy> mockAppBulkCopy;

        protected OlapSchemaManager olapSchemaManager;
        protected OlapRepository olapRepository;
        protected OlapQueryService olapQueryService;
        protected const string TestKind = "testkind";

        protected List<string> executedSqlStatements;
        protected List<List<DbParameter>> capturedParams;
        protected MockDbParameterCollection mockParameters;

        [TestInitialize]
        public virtual void TestInitialize()
        {
            executedSqlStatements = new List<string>();
            capturedParams = new List<List<DbParameter>>();

            mockDataReader = new Mock<DbDataReader>();
            mockAppCommand = new Mock<IAppCommand>();
            mockAppConnection = new Mock<IAppConnection>();
            mockConnectionFactory = new Mock<IDbConnectionFactory>();
            mockAppBulkCopy = new Mock<IAppBulkCopy>();

            mockConnectionFactory
                .Setup(f => f.CreateAndOpenConnectionAsync(It.IsAny<CancellationToken>()))
                .ReturnsAsync(mockAppConnection.Object);

            mockAppConnection
                .Setup(c => c.CreateCommand())
                .Returns(mockAppCommand.Object);

            mockAppCommand
            .Setup(cmd => cmd.ExecuteNonQueryAsync(It.IsAny<CancellationToken>()))
            .Callback(() => {
                executedSqlStatements.Add(mockAppCommand.Object.CommandText);
            })
            .ReturnsAsync(1);

            mockAppCommand.SetupProperty(cmd => cmd.CommandText);
            mockAppCommand.Setup(cmd => cmd.AddParameter(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<DbType>()));

            mockAppBulkCopy.Setup(abc => abc.Configure(It.IsAny<string>(), It.IsAny<int>()));
            mockAppBulkCopy.Setup(abc => abc.InitAsync(It.IsAny<CancellationToken>())).Returns(Task.CompletedTask);
            mockAppBulkCopy.Setup(abc => abc.WriteToServerAsync(It.IsAny<IEnumerable<object[]>>(), It.IsAny<CancellationToken>())).Returns(Task.CompletedTask);

            olapSchemaManager = new OlapSchemaManager(mockConnectionFactory.Object);
            olapRepository = new OlapRepository(mockConnectionFactory.Object);
            olapQueryService = new OlapQueryService(mockConnectionFactory.Object);
        }

        protected string GetDynamicTableName(string baseName, string kind)
        {
            var sb = new StringBuilder(baseName.Length + kind.Length + 3);
            sb.Append('`').Append(baseName).Append('_');
            foreach (char c in kind)
            {
                sb.Append(char.ToLowerInvariant(c));
            }
            sb.Append('`');
            return sb.ToString();
        }
    }

    public class MockDbParameterCollection : DbParameterCollection
    {
        private readonly List<DbParameter> _list = new List<DbParameter>();
        public string AssociatedCommandText { get; set; }
        public override int Add(object value) { _list.Add((DbParameter)value); return _list.Count - 1; }
        public override bool Contains(object value) => _list.Contains(value as DbParameter);
        public override void Clear() => _list.Clear();
        public override int IndexOf(object value) => _list.IndexOf(value as DbParameter);
        public override void Insert(int index, object value) => _list.Insert(index, (DbParameter)value);
        public override void Remove(object value) => _list.Remove(value as DbParameter);
        public override void RemoveAt(int index) => _list.RemoveAt(index);
        protected override DbParameter GetParameter(int index) => _list[index];
        protected override void SetParameter(int index, DbParameter value) => _list[index] = value;
        public override int Count => _list.Count;
        public override object SyncRoot => this;
        public override bool IsFixedSize => false;
        public override bool IsReadOnly => false;
        public override bool IsSynchronized => false;
        public override System.Collections.IEnumerator GetEnumerator() => _list.GetEnumerator();
        public override void AddRange(Array values) { foreach (var v in values) Add(v); }
        public override bool Contains(string value) => _list.Any(p => p.ParameterName == value);
        public override int IndexOf(string parameterName) => _list.FindIndex(p => p.ParameterName == parameterName);
        public override void RemoveAt(string parameterName) => _list.RemoveAll(p => p.ParameterName == parameterName);
        protected override DbParameter GetParameter(string parameterName) => _list.FirstOrDefault(p => p.ParameterName == parameterName);
        protected override void SetParameter(string parameterName, DbParameter value)
        {
            int index = IndexOf(parameterName);
            if (index >= 0) _list[index] = value; else Add(value);
        }

        public override void CopyTo(Array array, int index)
        {
            throw new NotImplementedException();
        }
    }

    [TestClass]
    public class OlapStorageTests : OlapStorageTestBase
    {
        [TestMethod]
        public void CreateTablesIfNotExists_GeneratesCorrectSql()
        {
            // Arrange
            int expectedNumberOfDdlStatements = 23;

            // Act
            olapSchemaManager.CreateTablesIfNotExists(TestKind);

            // Assert
            mockAppCommand.Verify(
                cmd => cmd.ExecuteNonQueryAsync(It.IsAny<CancellationToken>()),
                Times.Exactly(expectedNumberOfDdlStatements)
            );

            Assert.AreEqual(
                expectedNumberOfDdlStatements,
                executedSqlStatements.Count,
                "An unexpected number of DDL statements were executed."
            );

            string depositTableName = GetDynamicTableName("deposit", TestKind);
            Assert.IsTrue(
                executedSqlStatements.Any(s => s.Contains($"CREATE TABLE IF NOT EXISTS {depositTableName}")),
                "The CREATE TABLE statement for the deposit table was not found."
            );

            string mvJarAssignmentName = GetDynamicTableName("mv_deposit_current_jar_assignment", TestKind);
            Assert.IsTrue(
                executedSqlStatements.Any(s => s.Contains($"CREATE MATERIALIZED VIEW IF NOT EXISTS {mvJarAssignmentName}")),
                "The CREATE MATERIALIZED VIEW statement for jar assignments was not found."
            );

            string dailySummaryTableName = GetDynamicTableName("daily_transaction_summary", TestKind);
            Assert.IsTrue(
                executedSqlStatements.Any(s => s.Contains($"CREATE TABLE IF NOT EXISTS {dailySummaryTableName}")),
                "The CREATE TABLE statement for the daily summary table was not found."
            );
        }

        [TestMethod]
        public void DropAllTables_GeneratesCorrectSql()
        {
            // Arrange
            int expectedNumberOfDropStatements = 23;

            // Act
            olapSchemaManager.DropAllTables(TestKind);

            // Assert
            mockAppCommand.Verify(
                cmd => cmd.ExecuteNonQueryAsync(It.IsAny<CancellationToken>()),
                Times.Exactly(expectedNumberOfDropStatements)
            );

            Assert.AreEqual(
                expectedNumberOfDropStatements,
                executedSqlStatements.Count,
                "An unexpected number of DROP statements were executed."
            );

            string mvDailyDepositSummary = GetDynamicTableName("mv_daily_deposit_to_summary", TestKind);
            Assert.IsTrue(
                executedSqlStatements.Any(s => s.Contains($"DROP VIEW IF EXISTS {mvDailyDepositSummary}")),
                "The DROP VIEW statement for the daily deposit summary MV was not found."
            );

            string depositTableName = GetDynamicTableName("deposit", TestKind);
            Assert.IsTrue(
                executedSqlStatements.Any(s => s.Contains($"DROP TABLE IF EXISTS {depositTableName}")),
                "The DROP TABLE statement for the deposit table was not found."
            );

            string dailySummaryTableName = GetDynamicTableName("daily_transaction_summary", TestKind);
            Assert.IsTrue(
               executedSqlStatements.Any(s => s.Contains($"DROP TABLE IF EXISTS {dailySummaryTableName}")),
               "The DROP TABLE statement for the daily summary table was not found."
           );
        }

        [TestMethod]
        public void CreateDeposit_ConstructsCorrectSqlAndParameters()
        {
            // Arrange
            var deposit = new Deposit
            {
                Id = 1L,
                DocumentNumber = "DN001",
                Amount = 100.50m,
                Date = new DateTime(2025, 6, 24),
                StoreId = 1,
                AccountNumber = "ACC123",
                DomainId = 10,
                Address = "123 Main St",
                Created = new DateTime(2025, 6, 24, 16, 15, 9)
            };
            var expectedTableName = GetDynamicTableName("deposit", TestKind);

            // Act
            olapRepository.CreateDeposit(TestKind, deposit);

            // Assert
            mockConnectionFactory.Verify(f => f.CreateAndOpenConnectionAsync(It.IsAny<CancellationToken>()), Times.Once);
            mockAppCommand.Verify(cmd => cmd.ExecuteNonQueryAsync(It.IsAny<CancellationToken>()), Times.Once);

            Assert.AreEqual(1, executedSqlStatements.Count);
            string expectedSqlStart = $"INSERT INTO {expectedTableName}";
            Assert.IsTrue(executedSqlStatements[0].Trim().StartsWith(expectedSqlStart, StringComparison.OrdinalIgnoreCase));

            mockAppCommand.Verify(cmd => cmd.AddParameter(
                It.IsAny<string>(), It.IsAny<object>(), It.IsAny<DbType>()),
                Times.Exactly(10)
            );

            mockAppCommand.Verify(cmd => cmd.AddParameter(
                "Id", 
                (object)deposit.Id, 
                DbType.Int64 
            ), Times.Once);

            mockAppCommand.Verify(cmd => cmd.AddParameter(
                "AccountNumber",
                (object)deposit.AccountNumber,
                DbType.String
            ), Times.Once);

            mockAppCommand.Verify(cmd => cmd.AddParameter(
                "Amount",
                (object)deposit.Amount,
                DbType.Decimal
            ), Times.Once);
        }

        [TestMethod]
        public void CreateJarDetailIfNotExists_ConstructsCorrectSql()
        {
            // Arrange
            long jarVersion = 123L;
            long depositId = 456L;
            DateTime created = DateTime.UtcNow;

            string expectedTableName = GetDynamicTableName("jardetail", TestKind);
            string expectedSql = $"INSERT INTO {expectedTableName} (`JarVersion`, `DepositId`, `Created`) VALUES (@JarVersion, @DepositId, @Created);";

            // Act
            olapRepository.CreateJarDetailIfNotExists(TestKind, jarVersion, depositId, created);

            // Assert
            Assert.AreEqual(1, executedSqlStatements.Count);
            Assert.AreEqual(expectedSql.Trim(), executedSqlStatements[0].Trim());

            mockAppCommand.Verify(
                cmd => cmd.AddParameter(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<DbType>()),
                Times.Exactly(3)
            );

            mockAppCommand.Verify(
                cmd => cmd.AddParameter("JarVersion", jarVersion, DbType.Int64),
                Times.Once
            );

            mockAppCommand.Verify(
                cmd => cmd.AddParameter("DepositId", depositId, DbType.Int64),
                Times.Once
            );

            mockAppCommand.Verify(
                cmd => cmd.AddParameter("Created", created, DbType.DateTime),
                Times.Once
            );

            mockAppCommand.Verify(
                cmd => cmd.ExecuteNonQueryAsync(It.IsAny<CancellationToken>()),
                Times.Once
            );
        }

        [TestMethod]
        public async Task DepositsInNewestJarAsync_ConstructsQueryAndMapsResults()
        {
            DateTime startDate = new DateTime(2025, 6, 1);
            DateTime endDate = new DateTime(2025, 6, 30);
            string accountNumber = "ACC456";

            var expectedDeposit1 = new Deposit { Id = 101, DocumentNumber = "D001", Amount = 100m };
            var expectedDeposit2 = new Deposit { Id = 102, DocumentNumber = "D002", Amount = 250m };

            var readCallCount = 0;
            mockDataReader.Setup(r => r.ReadAsync(It.IsAny<CancellationToken>()))
                          .ReturnsAsync(() => ++readCallCount <= 2);

            mockDataReader.Setup(r => r.GetOrdinal(It.IsAny<string>())).Returns<string>(name =>
            {
                return name switch
                {
                    "DepositId" => 0,
                    "DocumentNumber" => 1,
                    "Amount" => 2,
                    "Date" => 3,
                    "Store" => 4,
                    "AccountNumber" => 5,
                    "DomainId" => 6,
                    "Address" => 7,
                    "Created" => 8,
                    "TotalAmount" => 9,
                    "DepositCount" => 10,
                    _ => -1
                };
            });

            mockDataReader.SetupSequence(r => r.GetInt64(0)).Returns(101L).Returns(102L);
            mockDataReader.SetupSequence(r => r.GetString(1)).Returns("D001").Returns("D002");

            mockDataReader.Setup(r => r.GetDecimal(9)).Returns(350m);
            mockDataReader.Setup(r => r.GetValue(10)).Returns(2UL);
            mockDataReader.Setup(r => r.IsDBNull(It.IsAny<int>())).Returns(false);

            mockAppCommand
                .Setup(cmd => cmd.ExecuteReaderAsync(It.IsAny<CancellationToken>()))
                .ReturnsAsync(mockDataReader.Object);


            // Act
            var result = await olapQueryService.DepositsInNewestJarAsync(TestKind, startDate, endDate, accountNumber);

            // Assert
            Assert.AreEqual(2, result.Deposits.Count);
            Assert.AreEqual(101L, result.Deposits[0].Id);
            Assert.AreEqual(102L, result.Deposits[1].Id);
            Assert.AreEqual(350m, result.TotalAmount);
        }

    }

}