﻿using Puppeteer.EventSourcing.Libraries;
using System;

namespace GamesEngine.Business.Liquidity.Transactions
{
    abstract class Transaction : Objeto
    {
        internal DateTime CreatedAt { get; private set; }
        internal int Id { get; private set; }
        internal decimal Rate { get; private set; }
        protected Transaction(int id, DateTime createdAt)
        {
            Id = id;
            CreatedAt = createdAt;
        }
        protected Transaction(int id,DateTime createdAt, decimal rate)
        {
            Id = id;
            CreatedAt = createdAt;
            Rate = rate;
        }

        internal decimal ExchangeVariation(decimal actualRate)
        {
            decimal amount =0;
            decimal rate =0;
            switch (this)
            {
                case Deposit deposit:
                    amount =  deposit.Amount;
                    rate = deposit.Rate;
                    break;
                case Withdrawal withdrawal:
                    amount = withdrawal.Amount;
                    rate = withdrawal.Rate;
                    break;
            }
            var usdAmount = amount * actualRate;
            var usdAmountOldRate = amount * rate;
            var diffrence = usdAmount - usdAmountOldRate;
            decimal rounded = Math.Round(diffrence, 2);
            return rounded == 0 ? 0.0m : rounded;
        }

        internal RateDirection ValueDirection(decimal actualRate)
        {
            decimal rate = 0;
            switch (this)
            {
                case Deposit deposit:
                    rate = deposit.Rate;
                    break;
                case Withdrawal withdrawal:
                    rate = withdrawal.Rate;
                    break;
            }
            
            if (actualRate > rate)
            {
                return RateDirection.Up;
            }
            else if (actualRate < rate)
            {
                return RateDirection.Down;
            }
            else
            {
                return RateDirection.Stable;
            }
        }

        internal string ValueDirectionAsString(decimal rate)
        {
            return $"{ValueDirection(rate)}";
        }

        internal enum RateDirection
        {
            Up,
            Down,
            Stable
        }
    }
}
